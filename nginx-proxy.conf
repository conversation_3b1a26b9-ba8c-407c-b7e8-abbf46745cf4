events {
    worker_connections 1024;
}

http {
    server {
        listen 8080;
        server_name _;

        location / {
            proxy_pass http://*********:80;
            proxy_set_header Host umbrel.local;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            proxy_set_header X-Forwarded-Host $host;

            # Modify CSP header to allow connections to the domain and inline scripts
            proxy_hide_header Content-Security-Policy;
            add_header Content-Security-Policy "img-src *;connect-src 'self' https://apps.umbrel.com;default-src 'self' 'unsafe-inline';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src 'self' 'unsafe-inline';style-src 'self' https: 'unsafe-inline'";

            # Force HTTPS for mixed content
            add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        }
    }
}
