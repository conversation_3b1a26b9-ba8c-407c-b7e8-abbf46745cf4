version: '3.7'
services:
  app_proxy:
    environment:
      APP_HOST: n8n_server_1
      APP_PORT: 5678
      PROXY_AUTH_WHITELIST: /webhook-test/*,/webhook/*
    container_name: n8n_app_proxy_1
  server:
    image: >-
      n8nio/n8n:1.100.1@sha256:ae08af2be497c7af445633d577365d8f6155362bdeaa52b54e7f47c0f672f7c8
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data:/home/<USER>/.n8n
    stop_grace_period: 1m
    environment:
      - PUID=1000
      - PGID=1000
      - N8N_HOST=n8n.reizedispatch.com
      - N8N_SECURE_COOKIE=false
      - N8N_DIAGNOSTICS_ENABLED=false
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
      - N8N_RUNNERS_ENABLED=true
    container_name: n8n_server_1
