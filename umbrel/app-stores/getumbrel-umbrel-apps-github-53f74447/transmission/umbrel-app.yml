manifestVersion: 1
id: transmission
category: networking
name: Transmission
version: "4.0.6"
tagline: A fast, easy and free BitTorrent client
description: >-
  Transmission is designed for easy, powerful use. We've set the defaults to just work and it only takes a few clicks to configure advanced features like watch directories, bad peer blocklists, and the web interface. When Ubuntu chose Transmission as its default BitTorrent client, one of the most-cited reasons was its easy learning curve.


  Transmission has one of the lowest memory & resource footprints of any major BitTorrent client. Transmission's light overhead is one reason why it is so well suited for home NAS and media servers. Having been used by Western Digital, Zyxel and Belkin, Transmission gives truly impressive performance on almost any compatible hardware.

  
  Transmission is an open source, volunteer-based project. Unlike some BitTorrent clients, Transmission doesn't play games with its users to make money.

  
  We don't bundle toolbars, pop-up ads, flash ads, twitter tools, or anything else. It doesn't hold some features back for a payware version. We don't track our users. Our website and forums have no third-party ads or analytics.


  ⚠️ Transmission downloads torrents over the Clearnet, not Tor.
developer: Transmission
website: https://transmissionbt.com/
dependencies: []
repo: https://github.com/transmission/transmission
support: https://github.com/transmission/transmission/issues
port: 9091
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: "/transmission/web/"
defaultUsername: ""
defaultPassword: ""
torOnly: false
releaseNotes: >-
  This release includes several bug fixes and improvements for better performance and stability. Full release notes are available at https://github.com/transmission/transmission/releases
permissions:
  - STORAGE_DOWNLOADS
widgets:
  - id: "status"
    type: "four-stats"
    refresh: "5s"
    endpoint: "widget-server:80/widgets/status"
    link: ""
    example:
      type: "four-stats"
      link: ""
      items:
        - title: "Downloading"
          text: "3"
          subtext: "torrents"
        - title: "Seeding"
          text: "1"
          subtext: "torrents"
        - title: "Stopped"
          text: "0"
          subtext: "torrents"
        - title: "Queued"
          text: "2"
          subtext: "torrents"
  - id: "torrent-list"
    type: "list"
    refresh: "2s"
    endpoint: "widget-server:80/widgets/torrent-list"
    link: ""
    example:
      type: "list"
      link: ""
      items:
        - text: "33% • Ubuntu 22.04.3 amd64 ISO image"
          subtext: "↓4.4Mbps ↑1.2Mbps"
        - text: "100% • Ubuntu 21.1.0 amd64 ISO image"
          subtext: "↓0Mbps ↑78Kbps"
        - text: "100% • Debian 11.0 amd64 ISO image"
          subtext: "↓0Mbps ↑827Kbps"
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/commit/60878f278d544b204d8e7c96240c797f43a9b319