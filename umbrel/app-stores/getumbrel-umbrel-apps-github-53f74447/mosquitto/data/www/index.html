<!DOCTYPE html>
<html><head><meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <title><PERSON><PERSON><PERSON>tto</title>
    <style>
        /*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
        html {
            line-height: 1.15;
            -webkit-text-size-adjust: 100%
        }

        body {
            margin: 0
        }

        main {
            display: block
        }

        h1 {
            font-size: 2em;
            margin: .67em 0
        }

        hr {
            box-sizing: content-box;
            height: 0;
            overflow: visible
        }

        pre {
            font-family: monospace, monospace;
            font-size: 1em
        }

        a {
            background-color: transparent
        }

        abbr[title] {
            border-bottom: none;
            text-decoration: underline;
            text-decoration: underline dotted
        }

        b,
        strong {
            font-weight: bolder
        }

        code,
        kbd,
        samp {
            font-family: monospace, monospace;
            font-size: 1em
        }

        small {
            font-size: 80%
        }

        sub,
        sup {
            font-size: 75%;
            line-height: 0;
            position: relative;
            vertical-align: baseline
        }

        sub {
            bottom: -.25em
        }

        sup {
            top: -.5em
        }

        img {
            border-style: none
        }

        button,
        input,
        optgroup,
        select,
        textarea {
            font-family: inherit;
            font-size: 100%;
            line-height: 1.15;
            margin: 0
        }

        button,
        input {
            overflow: visible
        }

        button,
        select {
            text-transform: none
        }

        [type=button],
        [type=reset],
        [type=submit],
        button {
            -webkit-appearance: button
        }

        [type=button]::-moz-focus-inner,
        [type=reset]::-moz-focus-inner,
        [type=submit]::-moz-focus-inner,
        button::-moz-focus-inner {
            border-style: none;
            padding: 0
        }

        [type=button]:-moz-focusring,
        [type=reset]:-moz-focusring,
        [type=submit]:-moz-focusring,
        button:-moz-focusring {
            outline: 1px dotted ButtonText
        }

        fieldset {
            padding: .35em .75em .625em
        }

        legend {
            box-sizing: border-box;
            color: inherit;
            display: table;
            max-width: 100%;
            padding: 0;
            white-space: normal
        }

        progress {
            vertical-align: baseline
        }

        textarea {
            overflow: auto
        }

        [type=checkbox],
        [type=radio] {
            box-sizing: border-box;
            padding: 0
        }

        [type=number]::-webkit-inner-spin-button,
        [type=number]::-webkit-outer-spin-button {
            height: auto
        }

        [type=search] {
            -webkit-appearance: textfield;
            outline-offset: -2px
        }

        [type=search]::-webkit-search-decoration {
            -webkit-appearance: none
        }

        ::-webkit-file-upload-button {
            -webkit-appearance: button;
            font: inherit
        }

        details {
            display: block
        }

        summary {
            display: list-item
        }

        template {
            display: none
        }

        [hidden] {
            display: none
        }
    </style>
    <style>
        body {
            background-color: #1D1B1B;
            font-family: system-ui, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Segoe UI, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
            color: #fff;
        }

        *,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p,
        span {
            color: #fff;
            font-size: 20px;
            font-weight: normal;
        }

        .success {
            color: #63FB72;
        }

        .text-muted {
            opacity: 0.8;
        }

        .text-small {
            font-size: 14px;
        }

        hr {
            width: 100%;
            height: 2px;
            background: #2F2C2C;
            border: none;
            margin: 40px 0;
        }

        .container {
            padding: 40px;
            max-width: 1440px;
            margin: auto;
        }

        .app-icon {
            border-radius: 20px;
        }

        .app {
            display: flex;
            margin: 20px 0 40px 0;
        }

        .app>.app-icon {
            flex-shrink: 0;
            height: 140px;
            width: 140px;
            box-shadow: 0 0 40px 0 rgba(0, 0, 0, 0.95);
            margin-right: 24px;
        }

        .app>.app-details>.app-status {
            display: block;
            font-size: 20px;
            margin: 10px 0 0 0;
        }

        .app>.app-details>.app-name {
            font-size: 52px;
            line-height: 52px;
            font-weight: bold;
            margin: 10px 0 0 0;
        }
    </style>
    <link rel="shortcut icon" type="image/jpg" href="favicon.png">
</head>

<body>
    <section class="container app-container">
        <div class="app">
            <img class="app-icon" src="logo.png" width="256" height="256">
            <div class="app-details">
                <span class="app-status success">&#9679; Running</span>
                <h1 class="app-name">Eclipse Mosquitto</h1>
            </div>
        </div>
        <p class="text-muted"></p>
        <hr>
        <h2>How to Connect to the MQTT-Broker</h2>
        <p>Follow these steps to connect to the MQTT-Broker:</p>

        <h3>MQTTX Web (Webbased)</h3>
        <ol>
            <li>Install <strong>MQTTX Web</strong> from the Umbrel Appstore.</li>
            <li>use <strong>umbrel.local</strong> as server address.</li>
            <li>use <strong>Port 9021</strong> as server port.</li>
            <li>Click <strong>connect</strong> and you should now be able to subscribe/publish to the topics you like.</li>
        </ol>

        <h3>MQTT Explorer (Windows)</h3>
        <ol>
            <li>Install <strong>MQTT Explorer</strong> from <a href="https://mqtt-explorer.com/">mqtt-explorer.com</a>.</li>
            <li>use <strong>umbrel.local</strong> as server address.</li>
            <li>use <strong>Port 1883</strong> as server port.</li>
            <li>Click <strong>connect</strong> and you should now be able to subscribe/publish to the topics you like.</li>
        </ol>

        <p>Use the access information above to configure devices and services to access the MQTT-Broker.</p>
        <br>
        <h1><strong>&#128680; Heads up &#128680;</strong></h1>
        <p>If you plan to expose your MQTT-Broker to the internet you are <strong>strongly</strong> advised to configure <a href="https://mosquitto.org/documentation/authentication-methods">authentification</a>.</p>
        <p>Change the configuration by following these steps to modify the <strong>mosquitto.conf</strong> manualy:</p>
        <ul>
            <li>open a terminal by clicking <strong>Settings -&gt; Advanced settings -&gt; Terminal -&gt; umbrelOS</strong></li>
            <li>enter <code><strong>sudo nano ~/umbrel/app-data/mosquitto/data/config/mosquitto.conf</strong></code></li>
        </ul>
    </section>

</body>
</html>
