manifestVersion: 1
id: mosquitto
name: Mosquitto
tagline: An open source MQTT broker
category: automation
version: "2.0.21"
port: 9021
description: >-
  Eclipse Mosquitto is an open source (EPL/EDL licensed) message broker that implements the MQTT protocol versions 5.0, 3.1.1 and 3.1. <PERSON><PERSON><PERSON><PERSON> is lightweight and is suitable for use on all devices from low power single board computers to full servers.


  The MQTT protocol provides a lightweight method of carrying out messaging using a publish/subscribe model. This makes it suitable for Internet of Things messaging such as with low power sensors or mobile devices such as phones, embedded computers or microcontrollers.


  The Mosquitto project also provides a C library for implementing MQTT clients, and the very popular mosquitto_pub and mosquitto_sub command line MQTT clients.
developer: Eclipse Foundation
website: https://mosquitto.org/
submitter: dirstel
submission: https://github.com/getumbrel/umbrel-apps/pull/1789
repo: https://github.com/eclipse-mosquitto/mosquitto
support: https://github.com/eclipse-mosquitto/mosquitto
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  ⚠️ Important security update that fixes vulnerabilities


  This update includes several bug fixes and improvements:
    - Fixed memory leak on malicious SUBSCRIBE and patched a CVE
    - Improved handling of invalid or reserved packets
    - Fixed issues with IPv6 interface binding and anonymous access settings
    - Added option to expire unused retained messages from memory
    - Improved error handling for SSL config file combinations
    - Fixed client deadlock when using certain flags
    - Enabled client ID setting in mosquitto_ctrl dynsec
    - Added timezone data to Docker images
    - Fixed SSL-related test failures under load


  Full release notes can be found at https://mosquitto.org/blog/
dependencies: []
path: ""
defaultUsername: ""
