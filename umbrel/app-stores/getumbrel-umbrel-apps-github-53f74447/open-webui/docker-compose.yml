version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: open-webui_web_1
      APP_PORT: 8080
      PROXY_AUTH_ADD: "false"

  web:
    image: ghcr.io/open-webui/open-webui:v0.6.15@sha256:fda936056184035a71a9ad5ff3721530ff061f16bc3907efb33a3201ba87ccfe
    volumes:
      - ${APP_DATA_DIR}/data/open-webui:/app/backend/data
    environment:
      # Exported from ollama app, which is currently a required dependency.
      # This will need to change once optional dependencies are supported.
      OLLAMA_BASE_URL: $APP_OLLAMA_URL
    restart: on-failure
