manifestVersion: 1
id: open-webui
name: Open WebUI
tagline: Chat with Ollama models like DeepSeek-R1 and LLama, or use an OpenAI API key
category: ai
version: "0.6.15"
port: 2876
description: >-
  Open WebUI lets you chat with advanced AI models running locally on your own device or connect to online models using an API key.


  **Getting Started with Local AI Models:**


  🦙 Install Ollama: Start by installing the Ollama app from the Umbrel App Store. Ollama enables you to download and run large language models like Llama 3 and DeepSeek-R1 directly on your device.


  ⬇️ Download a Model: In the Open WebUI app, type the name of the model you want in the search bar and click "Pull from Ollama.com." A full list of models is available at https://ollama.com/.


  🤖 Example - Running DeepSeek-R1 1.5B: To use the DeepSeek-R1 model with 1.5 billion parameters, type deepseek-r1:1.5b in the search bar and start the download. Once it's ready, you can chat with the model directly in Open WebUI.


  ⚠️ Warning: Before running a model, make sure your device has enough free RAM to support it. Attempting to run a model that exceeds your available memory could cause your device to crash or become unresponsive. Always check the model requirements before downloading or starting it.
developer: Open WebUI
website: https://openwebui.com/
submitter: al-lac
submission: https://github.com/getumbrel/umbrel-apps/pull/1977
repo: https://github.com/open-webui/open-webui
support: https://github.com/open-webui/open-webui/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
defaultUsername: ""
defaultPassword: ""
dependencies:
  - ollama
releaseNotes: >-
  Key highlights in this release include:
    - Global Image Compression Option to optimize all image uploads and outputs for faster load times
    - Custom Speech-to-Text Content-Type for better audio transcription compatibility
    - User Message Timestamps on Hover in Chat Bubble UI for better context
    - Leaderboard Sorting Options for clearer analysis of top performers
    - Evaluation Details Modal for deeper insights into feedbacks and rankings
    - Support for Multiple Pages in External Document Loaders
    - New Accessibility Enhancements including better tab navigation and ARIA support
    - Performance and Stability Upgrades across frontend and backend
    - Updated and Expanded Localizations for multiple languages
    - More descriptive Ollama error messages for better troubleshooting
    - Fixed issues with webloader bypass, citation duplicates, and OAuth callbacks
    - Dedicated Permission for System Prompts for enhanced security control


  Full release notes can be found at https://github.com/open-webui/open-webui/releases
path: ""
