version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: nitter_web_1
      APP_PORT: 8080

  web:
    image: ceramicwhite/nitter:build-38985af@sha256:594a184c3a834c29957ddd917800d61f3d560411d26fa6cf389bef350d4c4ac6
    restart: on-failure
    stop_grace_period: 1m
    init: true
    user: 1000:1000
    command: >
      /bin/sh -c "sed -i 
      -e 's/localhost/nitter_redis_1/g' 
      -e 's/redisPassword = \"\"/redisPassword = \"moneyprintergobrrr\"/g' 
      -e 's/theme = \"Nitter\"/theme = \"auto_(twitter)\"/g' 
      /src/nitter.conf
      && ./nitter"
    depends_on:
      - redis
  
  redis:
    image: redis:6.2.2-buster@sha256:e10f55f92478715698a2cef97c2bbdc48df2a05081edd884938903aa60df6396
    restart: on-failure
    stop_grace_period: 1m
    user: 1000:1000
    command: "redis-server --save 60 1 --loglevel warning --requirepass moneyprintergobrrr"
    volumes:
      - ${APP_DATA_DIR}/data/redis:/data