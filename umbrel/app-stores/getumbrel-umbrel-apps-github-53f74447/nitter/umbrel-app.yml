manifestVersion: 1
id: nitter
category: social
name: Nitter
version: "38985af"
tagline: Browse Twitter without tracking or ads
description: >
  ⚠️ Removal Notice: The Nitter project is no longer maintained and is non-functional due to changes in X's API. For more information, visit the project's GitHub repository.


  Nitter is a free and open source alternative Twitter front-end focused on privacy and performance.


  It's impossible to use Twitter without JavaScript enabled. For privacy-minded folks, preventing JavaScript analytics and IP-based tracking is important, but apart from using a VPN and uBlock/uMatrix, it's impossible. Despite being behind a VPN and using heavy-duty adblockers, you can get accurately tracked with your browser's fingerprint, no JavaScript required. This all became particularly important after Twitter removed the ability for users to control whether their data gets sent to advertisers.


  Using an instance of Nitter hosted on your Umbrel, you can browse Twitter without JavaScript while retaining your privacy. In addition to respecting your privacy, Nitter is on average around 15 times lighter than Twitter, and in most cases serves pages faster (eg. timelines load 2-4x faster).


  In the future a simple account system will be added that lets you follow Twitter users, allowing you to have a clean chronological timeline without needing a Twitter account.


  Features:

  - No JavaScript or ads

  - All requests go through the backend, client never talks to Twitter

  - Prevents Twitter from tracking your IP or JavaScript fingerprint

  - Uses Twitter's unofficial API (no rate limits or developer account required)

  - Lightweight (for @nim_lang, 60KB vs 784KB from twitter.com)

  - RSS feeds

  - Themes

  - Mobile support (responsive design)

  - AGPLv3 licensed, no proprietary instances permitted

developer: Zedeus
website: https://github.com/zedeus
dependencies: []
repo: https://github.com/zedeus/nitter
support: https://matrix.to/#/#nitter:matrix.org
port: 8420
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >
  - fixed bug that caused everybody to be displayed as verified

  - fixed bug that caused threads on user profiles to be hidden

  - Add proper tombstone for subscriber tweets

  - Remove outdated tweet from card test

  - Fix pinned "TweetPreviewDisplay" crash

  - Update badges

  - Prevent search endpoint from discarding tokens

  - Add actions workflow to run Selenium tests

  - Uppercase HLS in preference description
submitter: Jasper
submission: https://github.com/getumbrel/umbrel-apps/pull/128
disabled: true