manifestVersion: 1
id: webcheck
name: WebCheck
tagline: An all-in-one Open Source Intelligence tool for analyzing any website
category: networking
version: "1.0.0"
port: 3093
description: >-
  WebCheck is a versatile OSINT (Open Source Intelligence) tool for analyzing and monitoring websites. It offers a comprehensive range of features to assist users in security audits and performance analysis of websites. Key functionalities include:


  🔍 Website Scanning: Automated detection of vulnerabilities and security weaknesses.

  
  🚀 Performance Analysis: Metrics like load times, visibility, and optimization potential.

  
  📝 Content Analysis: Extraction of metadata, DNS info, and HTTP headers.

  
  📊 Visual Representation: Generation of screenshots to document site layouts.

  
  📝 Monitoring and Reporting: Continuous tracking with custom reporting options.

  
  WebCheck is modular, allowing users to integrate additional plugins for extended functionality. It supports API integration, is cross-platform, and is easy to deploy via Docker, making it accessible on any server. Its open-source nature ensures transparency and full control over the conducted analyses, making it ideal for security-conscious users.


  In addition to public website analysis, WebCheck can be used for internal network security and infrastructure checks within organizations. The customizable features and extensibility make it an essential tool for web analysts and IT security teams.

developer: <PERSON>
website: https://web-check.xyz/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/1742
repo: https://github.com/Lissy93/web-check
support: https://github.com/Lissy93/web-check/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
releaseNotes: ""
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
