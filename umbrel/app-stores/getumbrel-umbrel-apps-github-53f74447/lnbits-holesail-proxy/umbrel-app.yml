manifestVersion: 1
id: lnbits-holesail-proxy
category: bitcoin
name: LNbits Holesail Proxy
version: "1.0.7"
tagline: A Holesail proxy to connect remotely to your LNbits
description: >-
  Running Umbrel behind a home router (NAT) without a public domain?

  TOR is too slow?

  This app will allow you (and your friends) to connect remotely to your LNbits, using a Holesail - an instant P2P tunnel to bypass any network, firewall, NAT restrictions.

  
  Note: The tunnel only works when the client side is running from outside the local network (which is the main use case of this app).
releaseNotes: ""
developer: oren-z0
website: https://github.com/oren-z0/holesail-server-manager
dependencies:
  - lnbits
repo: https://github.com/oren-z0/holesail-server-manager
support: https://github.com/oren-z0/holesail-server-manager/discussions
port: 3107
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: oren-z0
submission: https://github.com/getumbrel/umbrel-apps/pull/2007
