manifestVersion: 1
id: etherpad  
name: Etherpad
tagline: An intuitive tool for real-time collaborative writing
category: files
version: "2.3.2"
port: 9002
description: >-
  ⚙️ The admin panel for Etherpad is accessible at "http://umbrel.local:9002/admin/".
  You can find your unique credentials by right-clicking on the Etherpad app icon on the umbrelOS homescreen and selecting "Show default credentials".


  📝 Etherpad is an open-source, web-based collaborative real-time editor that enables multiple users to simultaneously edit a document from different locations. It's particularly valuable for teamwork, group projects, or situations where many people need to contribute to the same document at once. One of Etherpad's standout features is its live collaboration functionality - as users make changes, those changes appear in real-time for all participants, with each user's contributions color-coded to distinguish them.


  The platform is designed to be simple and intuitive, with a clean and straightforward interface that emphasizes writing and editing. Etherpad doesn't require users to sign up or install any software, making it easy for anyone to start collaborating quickly.


  Another key feature of Etherpad is its version history system, which allows users to view and revert to previous versions of the document. This ensures that changes can be tracked and recovered if necessary, making it a useful tool for managing long-term collaborative projects.


  As an open-source tool, Etherpad offers flexibility in how it's used. Users can run it on their own servers for greater control and customization or simply use a public instance hosted by various service providers. The platform is highly extensible, with plugins available for additional features like embedding images, adding task lists, or even integrating with other tools.


  Etherpad has gained popularity in academic, business, and development environments for its simple yet effective collaboration features. Whether you're writing meeting notes, drafting a report, or brainstorming ideas with a team, Etherpad offers a powerful and user-friendly solution for collaborative writing.
developer: The Etherpad Foundation
website: https://etherpad.org/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2132
repo: https://github.com/ether/etherpad-lite
support: https://github.com/ether/etherpad-lite/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  This release includes various dependency updates and bug fixes


  Full release notes can be found at https://github.com/ether/etherpad-lite/releases
dependencies: []
path: ""
defaultUsername: "admin"
deterministicPassword: true
