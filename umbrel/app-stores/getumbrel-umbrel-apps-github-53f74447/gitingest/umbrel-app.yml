manifestVersion: 1
id: gitingest
name: Gitingest
tagline: An effortless way to make Git repositories LLM-friendly
category: developer
version: "0.1.5"
port: 8895
description: >-
  Gitingest is an open-source tool that converts Git repositories into structured, LLM-friendly text summaries, making it easier to analyze and process code with AI models like ChatGPT and Claude. By simply replacing "github.com" with "gitingest.com" in any repository URL, users can instantly generate an AI-optimized digest of the codebase. Additionally, a one-click button provides seamless integration with GitHub, ensuring a smooth workflow for developers.


  The tool offers smart formatting to present repository content in a structured way that enhances prompt effectiveness for LLMs. Users can either rely on the default GitIngest service or configure their own setup for added flexibility. With a strong emphasis on privacy, Gitingest collects no data and works entirely offline, making it a secure choice for developers and organizations alike.


  Gitingest is particularly useful for AI developers crafting precise prompts, teams working with AI-assisted development, and anyone who needs quick insights into a codebase. As part of the GitIngest ecosystem, it also provides CLI tools and Python packages for more advanced usage. Whether you're looking to streamline your AI-powered development workflow or improve your understanding of complex repositories, Gitingest offers a simple yet powerful solution.
developer: <PERSON><PERSON>
website: https://gitingest.com/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2225
repo: https://github.com/cyclotruc/gitingest
support: https://github.com/cyclotruc/gitingest/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  This release brings exciting new features and improvements.


  New features include:
    - Private repository support with GitHub token integration for both CLI and web interface
    - Enhanced CLI with new flags for including ignored files and streaming output
    - Support for self-hosted GitLab instances and custom Git domains
    - New AI documentation endpoint for better agent integration


  Key improvements:
    - Upgraded tokenizer for better AI model compatibility
    - Improved file handling with better symlink processing and UTF-16 encoding support
    - Enhanced user interface with fixed directory picker and better page titles
    - Better pattern matching for directory traversal and file selection


  Bug fixes include:
    - Fixed UI selection issues for exclude/include patterns
    - Improved error handling for private repositories
    - Better UTF-16 encoding support and various UI spacing improvements


  Full release notes are found at https://github.com/cyclotruc/gitingest/releases
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
