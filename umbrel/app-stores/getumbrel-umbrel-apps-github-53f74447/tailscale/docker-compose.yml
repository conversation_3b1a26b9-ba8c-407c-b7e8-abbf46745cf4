version: "3.7"

services:
  web:
    network_mode: "host" # TODO: We can remove this later with some iptables magic
    image: tailscale/tailscale:v1.84.2@sha256:8fcad6613f57c42f3073a58823b83b7c961f8e042fb784dc97378828660d65dc
    restart: on-failure
    stop_grace_period: 1m
    command: "sh -c 'tailscale web --listen 0.0.0.0:8240 & exec tailscaled --tun=userspace-networking'"
    volumes:
      - ${APP_DATA_DIR}/data:/var/lib
