version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: syncthing_server_1
      APP_PORT: 8384

  server:
    image: syncthing/syncthing:1.29.7@sha256:dde2305f0b78260a66e4a12785f0e26d86e89276ffccf50209c34943f270a954
    restart: on-failure
    stop_grace_period: 1m
    hostname: umbrel
    environment:
      - PUID=1000
      - PGID=1000
    volumes:
      - ${APP_DATA_DIR}/data:/var/syncthing
    ports:
      - 22000:22000/tcp
      - 22000:22000/udp
