version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: $APP_CORE_LIGHTNING_IP
      APP_PORT: $APP_CORE_LIGHTNING_PORT

  app:
    image: ghcr.io/elementsproject/cln-application:0.0.7@sha256:430efedac652abeffb2fbb5dcb991ab1128c942f64204c4599d48c331c9eeb10
    command: npm run start
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/app:${APP_CONFIG_DIR}
      - ${APP_CORE_LIGHTNING_DATA_DIR}:${CORE_LIGHTNING_PATH}
      - ${APP_CORE_LIGHTNING_REST_CERT_DIR}:${APP_REST_CERT_VOLUME_DIR}
    environment:
      SINGLE_SIGN_ON: "true"
      APP_IP: ${APP_CORE_LIGHTNING_IP}
      APP_PORT: ${APP_CORE_LIGHTNING_PORT}
      BITCOIN_NETWORK: ${APP_CORE_LIGHTNING_BITCOIN_NETWORK}
      LIGHTNING_IP: ${APP_CORE_LIGHTNING_DAEMON_IP}
      LIGHTNING_GRPC_PORT: ${APP_CORE_LIGHTNING_DAEMON_GRPC_PORT}
      LIGHTNING_WEBSOCKET_PORT: ${APP_CORE_LIGHTNING_WEBSOCKET_PORT}
      LIGHTNING_REST_PORT: ${APP_CORE_LIGHTNING_REST_PORT}
      APP_CORE_LIGHTNING_REST_CERT_DIR: ${APP_REST_CERT_VOLUME_DIR}
      HIDDEN_SERVICE_URL: http://${APP_CORE_LIGHTNING_REST_HIDDEN_SERVICE}
      LIGHTNING_PATH: ${CORE_LIGHTNING_PATH}
      COMMANDO_CONFIG: ${COMMANDO_CONFIG}
      APP_CONFIG_DIR: ${APP_CONFIG_DIR}
      APP_MODE: ${APP_MODE}
      DEVICE_DOMAIN_NAME: ${DEVICE_DOMAIN_NAME}
      LOCAL_HOST: http://${DEVICE_DOMAIN_NAME}
      CA_CERT: ${CORE_LIGHTNING_PATH}/bitcoin/ca.pem
      CLIENT_KEY: ${CORE_LIGHTNING_PATH}/bitcoin/client-key.pem
      CLIENT_CERT: ${CORE_LIGHTNING_PATH}/bitcoin/client.pem
    networks:
      default:
        ipv4_address: ${APP_CORE_LIGHTNING_IP}

  c-lightning-rest:
    image: saubyk/c-lightning-rest:0.10.7@sha256:6666e9f1fd107a9946cc94d8c82862ed18ac0d2c80fcddf30282979a6c8eb46e
    restart: on-failure
    ports:
      - ${APP_CORE_LIGHTNING_REST_PORT}:${APP_CORE_LIGHTNING_REST_PORT}
    environment:
      PORT: "${APP_CORE_LIGHTNING_REST_PORT}"
      PROTOCOL: "http"
      LN_PATH: "${CORE_LIGHTNING_PATH}"
    volumes:
      - "${APP_CORE_LIGHTNING_REST_CERT_DIR}:/usr/src/app/certs"
      - "${APP_CORE_LIGHTNING_DATA_DIR}:${CORE_LIGHTNING_PATH}"
    networks:
      default:
        ipv4_address: ${APP_CORE_LIGHTNING_REST_IP}

  lightningd:
    image: elementsproject/lightningd:v25.05@sha256:1afd2496b285577c252d8b3b81810f7bf6a24b573002ce994edb99097e6a7fae
    restart: on-failure
    ports:
      - ${APP_CORE_LIGHTNING_DAEMON_PORT}:9735
      - ${APP_CORE_LIGHTNING_WEBSOCKET_PORT}:${APP_CORE_LIGHTNING_WEBSOCKET_PORT}
      - ${CORE_LIGHTNING_REST_PORT}:${CORE_LIGHTNING_REST_PORT}
      - ${APP_CORE_LIGHTNING_DAEMON_GRPC_PORT}:${APP_CORE_LIGHTNING_DAEMON_GRPC_PORT}
    command:
      - --bitcoin-rpcconnect=${APP_BITCOIN_NODE_IP}
      - --bitcoin-rpcuser=${APP_BITCOIN_RPC_USER}
      - --bitcoin-rpcpassword=${APP_BITCOIN_RPC_PASS}
      - --bitcoin-rpcport=${APP_BITCOIN_RPC_PORT}
      - --lightning-dir=${CORE_LIGHTNING_PATH}
      - --proxy=${TOR_PROXY_IP}:${TOR_PROXY_PORT}
      - --bind-addr=${APP_CORE_LIGHTNING_DAEMON_IP}:9735
      - --bind-addr=ws:${APP_CORE_LIGHTNING_DAEMON_IP}:${APP_CORE_LIGHTNING_WEBSOCKET_PORT}
      - --addr=statictor:${TOR_PROXY_IP}:29051
      - --tor-service-password=${TOR_PASSWORD}
      - --network=${APP_CORE_LIGHTNING_BITCOIN_NETWORK}
      - --database-upgrade=true
      #- --experimental-offers
      - --grpc-host=${APP_CORE_LIGHTNING_DAEMON_IP}
      - --grpc-port=${APP_CORE_LIGHTNING_DAEMON_GRPC_PORT}
      - --clnrest-host=${APP_CORE_LIGHTNING_DAEMON_IP}
      - --clnrest-port=${CORE_LIGHTNING_REST_PORT}
    volumes:
      - "${APP_CORE_LIGHTNING_DATA_DIR}:${CORE_LIGHTNING_PATH}"
    networks:
      default:
        ipv4_address: ${APP_CORE_LIGHTNING_DAEMON_IP}

  tor:
    image: getumbrel/tor:*******@sha256:2ace83f22501f58857fa9b403009f595137fa2e7986c4fda79d82a8119072b6a
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/torrc:/etc/tor/torrc:ro
      - ${TOR_DATA_DIR}:/data
    environment:
      HOME: "/tmp"
