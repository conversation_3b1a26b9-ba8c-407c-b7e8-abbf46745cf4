manifestVersion: 1.1
id: librechat
name: LibreChat
tagline: Enhanced ChatGPT Clone
category: ai
version: "v0.7.8"
port: 7050
description: >-
  Enhanced ChatGPT Clone: Features Agents, DeepSeek, Anthropic, AWS, OpenAI, Assistants API, Azure, Groq, o1, GPT-4o, Mistral, OpenRouter, Vertex AI, Gemini, Artifacts, AI model switching, message search, Code Interpreter, langchain, DALL-E-3, OpenAPI Actions, Functions, Secure Multi-User Auth, Presets, open-source for self-hosting.


  ✨ Features
    - 🖥️ UI & Experience inspired by ChatGPT with enhanced design and features
    - 🤖 AI Model Selection:
      - Anthropic (Claude), AWS Bedrock, OpenAI, Azure OpenAI, Google, Vertex AI, OpenAI Assistants API (incl. Azure)
      - Custom Endpoints: Use any OpenAI-compatible API with LibreChat, no proxy required
      - Compatible with Local & Remote AI Providers:
        - Ollama, groq, Cohere, Mistral AI, Apple MLX, koboldcpp, together.ai,
        - <PERSON><PERSON>out<PERSON>, Perplexity, ShuttleAI, Deepseek, Qwen, and more
    - 🔧 Code Interpreter API:
      - Secure, Sandboxed Execution in Python, Node.js (JS/TS), Go, C/C++, Java, PHP, Rust, and Fortran
      - Seamless File Handling: Upload, process, and download files directly
      - No Privacy Concerns: Fully isolated and secure execution
    - 🔦 Agents & Tools Integration:
      - LibreChat Agents:
        - No-Code Custom Assistants: Build specialized, AI-driven helpers without coding
        - Flexible & Extensible: Attach tools like DALL-E-3, file search, code execution, and more
        - Compatible with Custom Endpoints, OpenAI, Azure, Anthropic, AWS Bedrock, and more
        - Model Context Protocol (MCP) Support for Tools
      - Use LibreChat Agents and OpenAI Assistants with Files, Code Interpreter, Tools, and API Actions
    - 🔍 Web Search:
      - Search the internet and retrieve relevant information to enhance your AI context
      - Combines search providers, content scrapers, and result rerankers for optimal results
    - 🪄 Generative UI with Code Artifacts:
      - Code Artifacts allow creation of React, HTML, and Mermaid diagrams directly in chat
    - 🎨 Image Generation & Editing
      - Text-to-image and image-to-image with GPT-Image-1
      - Text-to-image with DALL-E (3/2), Stable Diffusion, Flux, or any MCP server
      - Produce stunning visuals from prompts or refine existing images with a single instruction
    - 💾 Presets & Context Management:
      - Create, Save, & Share Custom Presets
      - Switch between AI Endpoints and Presets mid-chat
      - Edit, Resubmit, and Continue Messages with Conversation branching
      - Fork Messages & Conversations for Advanced Context control
    - 💬 Multimodal & File Interactions:
      - Upload and analyze images with Claude 3, GPT-4.5, GPT-4o, o1, Llama-Vision, and Gemini 📸
      - Chat with Files using Custom Endpoints, OpenAI, Azure, Anthropic, AWS Bedrock, & Google 🗃️
    - 🌎 Multilingual UI:
      - English, 中文, Deutsch, Español, Français, Italiano, Polski, Português Brasileiro
      - Русский, 日本語, Svenska, 한국어, Tiếng Việt, 繁體中文, العربية, Türkçe, Nederlands, עברית
    - 🧠 Reasoning UI:
      - Dynamic Reasoning UI for Chain-of-Thought/Reasoning AI models like DeepSeek-R1
    - 🎨 Customizable Interface:
      - Customizable Dropdown & Interface that adapts to both power users and newcomers
    - 🗣️ Speech & Audio:
      - Chat hands-free with Speech-to-Text and Text-to-Speech
      - Automatically send and play Audio
      - Supports OpenAI, Azure OpenAI, and Elevenlabs
    - 📥 Import & Export Conversations:
      - Import Conversations from LibreChat, ChatGPT, Chatbot UI
      - Export conversations as screenshots, markdown, text, json
    - 🔍 Search & Discovery:
      - Search all messages/conversations
    - 👥 Multi-User & Secure Access:
      - Multi-User, Secure Authentication with OAuth2, LDAP, & Email Login Support
      - Built-in Moderation, and Token spend tools
    - ⚙️ Configuration & Deployment:
      - Configure Proxy, Reverse Proxy, Docker, & many Deployment options
      - Use completely local or deploy on the cloud
    - 📖 Open-Source & Community:
      - Completely Open-Source & Built in Public
      - Community-driven development, support, and feedback
developer: Danny Avila
website: https://librechat.ai/
submitter: al-lac
submission: https://github.com/getumbrel/umbrel-apps/pull/2947
repo: https://github.com/danny-avila/LibreChat
support: https://github.com/danny-avila/LibreChat/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
defaultUsername: "<EMAIL>"
defaultPassword: ""
deterministicPassword: true
dependencies: []
releaseNotes: ""
path: ""
