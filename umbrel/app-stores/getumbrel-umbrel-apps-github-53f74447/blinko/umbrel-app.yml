manifestVersion: 1
id: blinko
name: Blinko
tagline: An AI-powered hub for your ideas and notes
category: files
version: "1.1.2"
port: 1111
description: >-
  😉 Blinko is an open-source application that combines note-taking, microblogging, pastebin functionality, task management, and AI-powered features in a self-hosted environment. The platform allows users to quickly and efficiently capture their thoughts, with full Markdown support for easy formatting.


  With the integration of AI models like OpenAI or Ollama, content can be intelligently searched and organized.

  If you want to use the Ollama instance of your Umbrel, enter the following URL in the settings to connect to it: **http://ollama_ollama_1:11434**


  The application provides a flexible way to manage notes, which can remain private or be shared publicly. Regular automatic backups and seamless data import and export ensure that users always retain control over their content. Since Blinko is self-hosted, users maintain full data sovereignty and can rely on a secure environment without commercial restrictions.


  Built on Next.js, Blinko is optimized for speed and efficiency, offering a responsive and modern user experience. As an open-source project, it encourages collaboration and is available for free without hidden fees. It is designed for anyone looking for a powerful and customizable solution for digital organization.
developer: Blinko
website: https://blinko-demo.vercel.app/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2183
repo: https://github.com/blinko-space/blinko
support: https://github.com/blinko-space/blinko/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
releaseNotes: >-
  This update includes new features, bug fixes, and improvements.


  Key changes in this version:
    - Added deployment selection and API key validation for Azure OpenAI integration
    - Added deployment selection and API key configuration prompts in multiple languages
    - Improved note management with support for multiple views and better data fetching
    - Enhanced settings page layout with responsive design
    - Added cache clearing functionality and new translations
    - Updated editor to version 3.11.1 with improved CSS styles
    - Added compression for better performance


  Full release notes are available at https://github.com/blinko-space/blinko/releases
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
