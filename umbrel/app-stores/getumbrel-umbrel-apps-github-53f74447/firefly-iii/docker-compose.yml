version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: firefly-iii_server_1
      APP_PORT: 8080
      PROXY_AUTH_WHITELIST: "/api/*"

  server:
    image: fireflyiii/core:version-6.2.19@sha256:8ff0132eb0756a668088a388fb628eade992d4b07d0a4aa9c6b99bd9b699e157
    # This image runs under the user/group www-data:www-data
    # user: "1000:1000"
    restart: on-failure
    environment:
      APP_KEY: $APP_FIREFLY_III_APP_KEY
      DB_HOST: firefly-iii_db_1
      DB_PORT: 3306
      DB_CONNECTION: mysql
      DB_DATABASE: fireflyiii
      DB_USERNAME: fireflyiii
      DB_PASSWORD: moneyprintergobrrr
    volumes:
      - ${APP_DATA_DIR}/data/upload:/var/www/html/storage/upload
      - ${APP_DATA_DIR}/data/logs:/var/www/html/storage/logs
  
  db:
    image: mariadb:10.9.4@sha256:f5146e2e0999a6afcf2a9134bb415ad068bf80ec29704f374f6f7dabb145738d
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data/mysql:/var/lib/mysql
    environment:
      MYSQL_DATABASE: "fireflyiii"
      MYSQL_USER: "fireflyiii"
      MYSQL_PASSWORD: "moneyprintergobrrr"
      MYSQL_ROOT_PASSWORD: "moneyprintergobrrr"
