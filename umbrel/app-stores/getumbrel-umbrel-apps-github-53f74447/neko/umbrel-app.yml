manifestVersion: 1
id: neko
name: Neko
tagline: A self-hosted virtual browser powered by WebRTC
category: networking
version: "3.0.4"
port: 3880
description: >-
  🐈 Neko is a self-hosted virtual browser that streams a fully interactive web browsing experience directly to the user's device using WebRTC. It provides access to a secure, sandboxed browser environment that runs independently of the user's local system, ensuring privacy and isolation. The browser is rendered remotely, and all interactions are transmitted in real time, resulting in a smooth and low-latency experience.


  Designed with collaboration in mind, Neko allows multiple users to join and share the same session, making it ideal for activities like co-browsing, watch parties, or remote support. The interface is optimized for responsiveness, with minimal delay between input and feedback, creating an experience that feels native despite running remotely.


  When integrated into Umbrel, Neko offers a private and always-available browser instance that can be accessed securely from within the home network or over the internet through Umbrel's tunneling options. It fits seamlessly into the self-hosted ecosystem, providing a reliable and user-friendly way to browse the web without relying on third-party services or exposing your local device.


  Neko is especially useful in scenarios where privacy, simplicity, and ease of access are important - whether for personal use, collaborative tasks, or secure remote browsing.


  💡 Neko offers a wide range of configuration options, including WebRTC configuration, plugin settings, and audio & video capture. For more detailed information, check out the official documentation here: https://neko.m1k1o.net/docs/v3/configuration.
developer: m1k1o
website: https://neko.m1k1o.net/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2524
repo: https://github.com/m1k1o/neko
support: https://github.com/m1k1o/neko/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: ""
dependencies: []
path: ""
defaultUsername: ""
deterministicPassword: true
