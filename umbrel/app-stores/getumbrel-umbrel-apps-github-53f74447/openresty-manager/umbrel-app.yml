manifestVersion: 1
id: openresty-manager
name: OpenResty Manager
tagline: Expose your services easily and securely
category: networking
version: "1.5.0"
port: 34567
description: >-
  Expose your apps to the internet easily and securely.


  ⚠️ Be cautious when exposing apps to the public internet. Ensure they have proper security, such as login protection, and avoid exposing sensitive apps without adequate safeguards.
  

  🔧 OpenResty Manager uses port 3080 for HTTP (unsecured) traffic and port 3443 for HTTPS (secured) traffic.
  To make your apps accessible from the public internet, you will need to set up port forwarding on your router.
  Forward external port 80 (HTTP) to internal port 3080 and external port 443 (HTTPS) to internal port 3443.
  

  🔍 Features:
  
    - Provide a beautiful, powerful, and easy-to-use web management UI
    - Free SSL support both for HTTP-01 and DNS-01 challenge or provide your own SSL certificates
    - Provide some powerful security function, such as Access Control, HTTP Flood Protection, etc
    - Easily create reverse proxy for your websites without knowing anything about OpenResty
    - Advanced OpenResty configuration available for super users
    - Support and inherit all powerful features of OpenResty
developer: UUSEC
website: https://om.uusec.com/
submitter: UUSEC
submission: https://github.com/getumbrel/umbrel-apps/pull/2779
repo: https://github.com/Safe3/openresty-manager
support: https://github.com/Safe3/openresty-manager/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: ""
dependencies: []
path: ""
defaultUsername: "admin"
defaultPassword: "#Passw0rd"
