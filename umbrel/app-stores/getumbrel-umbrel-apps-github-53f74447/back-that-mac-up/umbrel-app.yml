manifestVersion: 1.2
id: back-that-mac-up
category: files
name: Back That Mac Up
version: "1.1.0"
tagline: Backup your Mac to your Umbrel using Time Machine
description: >
  ⚠️ Removal Notice: This app has been removed from the Umbrel App Store. It would really make sense for this to be integrated directly into umbrelOS... hmmmmmmm.


  Introducing Back That Mac Up — an official app by Umbrel.


  Effortlessly back up your Mac to your Umbrel using Time Machine. Simply follow the instructions in the app to set up your Umbrel as a Time Machine backup destination and you're good to go!


  Make sure to follow the instructions in the app to appropriately allocate the backup size. Time Machine backs up your Mac by keeping hourly backups for the past 24 hours, daily backups for the past month and weekly backups for all previous months.
  Over time, it will likely use all the allotted space on your Umbrel, automatically deleting the oldest backups to free up space as needed.


  For seamless backups even when your Mac isn't connected to the same network as your Umbrel, simply install Tailscale on your Umbrel and your Mac, and use smb://umbrel as the server address.
releaseNotes: >-
  🎉 Widgets are here for umbrelOS 1.0.


  This update brings a new widget for Back That Mac Up, allowing you to see your Time Machine usage at a glance right from your Umbrel's home screen.


  After updating to umbrelOS 1.0, you can add widgets by right-clicking on the home screen and selecting "Edit widgets", or by clicking on Widgets in the Dock.
developer: Umbrel
website: https://umbrel.com
dependencies: []
repo: https://github.com/getumbrel/umbrel-back-that-mac-up
support: https://community.getumbrel.com
port: 3012
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: false
torOnly: false
widgets:
  - id: "disk-usage"
    type: "text-with-progress"
    refresh: "1m"
    endpoint: "server:80/api/widgets/disk-usage"
    link: ""
    example:
      type: "text-with-progress"
      link: ""
      title: "Time Machine Usage"
      text: "420 GB"
      progressLabel: "580 GB left"
      progress: 0.42
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/929
disabled: true
