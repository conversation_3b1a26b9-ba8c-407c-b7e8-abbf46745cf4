manifestVersion: 1
id: overseerr
category: media
name: Overseerr
version: "1.34.0"
tagline: Beautiful media discovery
description: >-
  Overseerr is a request management and media discovery tool built to work with your existing Plex ecosystem.


  Overseerr scans your Plex libraries at regular intervals, so it knows which items are already available on your server.
  It also integrates with the popular DVR applications Radarr and Sonarr, and supports activity monitoring within Overseerr itself.


  🛠️ SET-UP INSTRUCTIONS

  During initial set-up, you will need to input your Umbrel device's IP address to connect to Plex (and optional services such as Radarr and Sonarr).
  You can find your device's IP address by visiting your router's admin dashboard or by using an IP scanning tool like Angry IP Scanner.
releaseNotes: >-
  This release includes several bug fixes and improvements:
    - Improved web push notification management
    - Added support for requesting "Specials" for TV Shows
    - Enhanced count badge styling in the sidebar and mobile menu
    - Updated Plex logo in the UI
    - Added tooltips to display exact time on date hover
    - Improved handling of API language query parameters


  New features:
    - Added Korean language support
    - Introduced Pushover sound options for notifications
    - Added IMDB ratings proxy for Radarr
    - Implemented a requests/issues menu count
    - Added option to select default series type for anime


  Full release notes can be found at https://github.com/sct/overseerr/releases
developer: <PERSON>
website: https://overseerr.dev/
dependencies: []
repo: https://github.com/sct/overseerr
support: https://github.com/sct/overseerr/issues
port: 5055
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: false
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/607
