version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: overseerr_server_1
      APP_PORT: 5055
      PROXY_AUTH_WHITELIST: "/api/*"

  server:
    image: linuxserver/overseerr:1.34.0@sha256:314a170f02666f2509634321d36e674d3441f73421f838295ce71024c17aef60
    environment:
      - PUID=1000
      - PGID=1000
    volumes:
      - ${APP_DATA_DIR}/data/config:/config
    restart: on-failure
