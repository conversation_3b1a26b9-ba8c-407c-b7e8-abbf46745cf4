manifestVersion: 1
id: peerswap
category: bitcoin
name: PeerSwap
version: "1.7.8"
tagline: Balance your lightning channels with Liquid BTC
description: PeerSwap enables Lightning Network nodes to balance their channels by facilitating atomic swaps with direct peers. PeerSwap enhances decentralization of the Lightning Network by enabling all nodes to be their own swap provider. No centralized coordinator, no 3rd party rent collector, and lowest cost channel balancing means small nodes can better compete with large nodes. Includes channel AutoFees, Liquid Peg-in and BTC send with coin select + fee bump functionality.
developer: PeerSwap Project
website: https://peerswap.dev
dependencies:
  - lightning
  - elements
repo: https://github.com/Impa10r/peerswap-web
support: https://discord.com/invite/wpNv3PG8G2
port: 1984
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
submitter: Impa10r
submission: https://github.com/getumbrel/umbrel-apps/pull/932
releaseNotes: >-
  Bug fixes and improvements. Full changelog: https://github.com/Impa10r/peerswap-web/blob/main/CHANGELOG.md
