version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: peerswap_web_1
      APP_PORT: 1984

  web:
    image: ghcr.io/impa10r/peerswap-web:v1.7.8@sha256:eb0d586ebac4c94b02ea84f46633c30b61d3c78b493bd7ec1063a31ff3502fb4
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    environment:
      # App
      NETWORK: $APP_BITCOIN_NETWORK
      NO_HTTPS: "true"
      # LND
      LND_HOST: ${APP_LIGHTNING_NODE_IP}:${APP_LIGHTNING_NODE_GRPC_PORT}
      LND_MACAROONPATH: /home/<USER>/.lnd/data/chain/bitcoin/${APP_BITCOIN_NETWORK}/admin.macaroon
      # Elements RPC
      ELEMENTS_USER: elements
      ELEMENTS_PORT: $APP_ELEMENTS_NODE_RPC_PORT
      ELEMENTS_HOST: http://elements_node_1
      ELEMENTS_WALLET: peerswap
      ELEMENTS_FOLDER: /home/<USER>/.elements
      ELEMENTS_FOLDER_MAPPED: /home/<USER>/.elements
      # Bitcoin RPC
      BITCOIN_HOST: $APP_BITCOIN_NODE_IP
      BITCOIN_PORT: $APP_BITCOIN_RPC_PORT
      BITCOIN_USER: $APP_BITCOIN_RPC_USER
      BITCOIN_PASS: $APP_BITCOIN_RPC_PASS
    volumes:
      - ${APP_DATA_DIR}/data:/home/<USER>/.peerswap
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/home/<USER>/.lnd:ro
      - ${ELEMENTS_DATA_DIR}:/home/<USER>/.elements:ro
