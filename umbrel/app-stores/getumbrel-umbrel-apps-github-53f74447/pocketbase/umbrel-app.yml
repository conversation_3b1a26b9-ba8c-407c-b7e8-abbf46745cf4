manifestVersion: 1
id: pocketbase
name: PocketBase
tagline: Open Source backend for your next SaaS and Mobile app in 1 file
category: developer
version: "0.28.4"
port: 5400
description: >-
  PocketBase is an open source backend consisting of embedded database (SQLite) with realtime subscriptions, built-in auth management, convenient dashboard UI and simple REST-ish API.


  ⏱️ Realtime database

  Embedded performant database with schema builder, data validations, realtime subscriptions and easy to use REST api.


  🪪 Authentication

  Manage your app users and handle email/password and OAuth2 sign ups (Google, Facebook, GitHub, GitLab) without the hassle.


  💾 File storage

  Sanely store files locally or in a S3 storage. Easily attach media to your database records and generate thumbs on the fly.


  🧩 Extendable

  Use as a standalone app OR as a framework, that you can extend via Go and JavaScript hooks to create your own custom portable backend.
developer: PocketBase
website: https://pocketbase.io/
defaultUsername: "<EMAIL>"
defaultPassword: "umbrel-pocketbase"
submitter: Sharknoon
submission: https://github.com/getumbrel/umbrel-apps/pull/1082
repo: https://github.com/pocketbase/pocketbase
support: https://github.com/pocketbase/pocketbase/discussions
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
path: /_/
dependencies: []
releaseNotes: >-
  This release includes several improvements and bug fixes:


    - Added new geographic coordinates field for storing location data
    - Improved file handling and storage capabilities
    - Enhanced authentication and OAuth2 support
    - Better performance optimizations for database operations
    - Updated user interface improvements
    - Various security enhancements and dependency updates


  Full release notes are found at https://github.com/pocketbase/pocketbase/releases
