version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: pocketbase_app_1
      APP_PORT: 8090
      PROXY_AUTH_ADD: "false"
  
  app:
    image: ghcr.io/muchobien/pocketbase:0.28.4@sha256:291670139f413a13122dc4f156debd5c7c509df2af7d112cbe46124fa943550f
    # pocketbase needs to run as root
    # user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data/data:/pb_data
      - ${APP_DATA_DIR}/data/public:/pb_public
      - ${APP_DATA_DIR}/data/hooks:/pb_hooks
      - ${APP_DATA_DIR}/data/migrations:/pb_migrations
