version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: datum_datum_1
      APP_PORT: 21000

  datum:
    image: ghcr.io/retropex/datum:v1.10-umbrel-widget-2@sha256:ec1b4f39b280c316df30404f392582dd016c34911aa83ac631c696e2478e0e54
    entrypoint: ["/app/datum_gateway"]
    command: ["--config=/app/conf/datum_gateway_config.json"]
    user: 1000:1000
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/settings/:/app/conf/
    ports:
      # datum gateway port
      - 23334:23334
