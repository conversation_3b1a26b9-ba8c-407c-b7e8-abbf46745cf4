version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: penpot_penpot-frontend_1
      APP_PORT: 8080
      # Can consider disabling auth all together. Penpot has its own auth system.
      PROXY_AUTH_WHITELIST: "/api/*"

  penpot-frontend:
    image: penpotapp/frontend:2.7.2@sha256:9434b3847f219b926787286ff6f9c9cbf683e508d11bb16cc897a45891ac2503
    volumes:
      - ${APP_DATA_DIR}/data/assets:/opt/data/assets
    environment:
      - PENPOT_FLAGS=enable-registration enable-login-with-password
    depends_on:
      - penpot-backend
      - penpot-exporter
    restart: on-failure

  penpot-backend:
    image: penpotapp/backend:2.7.2@sha256:0d755e5824308234dc098bdb89c295813aae9ee451fa7ba3e6df98de994927bc
    # user 1000:1000 to avoid permission issues when importing libraries and templates
    user: "1000:1000"
    volumes:
      - ${APP_DATA_DIR}/data/assets:/opt/data/assets
    environment:
      - PENPOT_FLAGS=enable-registration enable-login-with-password disable-email-verification disable-smtp enable-prepl-server disable-secure-session-cookies
      - PENPOT_DATABASE_URI=postgresql://penpot-postgres/penpot
      - PENPOT_DATABASE_USERNAME=penpot
      - PENPOT_DATABASE_PASSWORD=penpot
      - PENPOT_REDIS_URI=redis://penpot-redis/0
      - PENPOT_ASSETS_STORAGE_BACKEND=assets-fs
      - PENPOT_STORAGE_ASSETS_FS_DIRECTORY=/opt/data/assets
      - PENPOT_TELEMETRY_ENABLED=false
      # PENPOT_PUBLIC_URI needs to be set to in order to render thumbnails in the frontend
      - PENPOT_PUBLIC_URI=http://${DEVICE_DOMAIN_NAME}:${APP_PENPOT_UI_PORT}
    depends_on:
      - penpot-postgres
      - penpot-redis
    restart: on-failure

  penpot-exporter:
    image: penpotapp/exporter:2.7.2@sha256:6ec9b876a4aaa7808b1afe3d242baf120bf15f0e79db1cf12e80a3ed1430658a
    user: "1000:1000"
    environment:
      - PENPOT_PUBLIC_URI=http://penpot-frontend
      - PENPOT_REDIS_URI=redis://penpot-redis/0
    restart: on-failure

  penpot-postgres:
    image: postgres:15@sha256:546445ad21cb5893c0997080b831ee45945e798c4359270e16413c8bd93575db
    user: "1000:1000"
    stop_signal: SIGINT
    volumes:
      - ${APP_DATA_DIR}/data/postgres:/var/lib/postgresql/data
    environment:
      - POSTGRES_INITDB_ARGS=--data-checksums
      - POSTGRES_DB=penpot
      - POSTGRES_USER=penpot
      - POSTGRES_PASSWORD=penpot
    restart: on-failure

  penpot-redis:
    image: redis:7@sha256:45bfd95117a171ff9c1b5304bdae80a7a61eb5bbf0793d6a86c9078526ad88fa
    user: "1000:1000"
    restart: on-failure
