manifestVersion: 1
id: penpot
category: developer
name: Penpot
version: "2.7.2"
tagline: Design and prototyping platform
description: >-
  Penpot is a pioneering open-source platform for design and prototyping, tailored specifically for product teams.
  Unconstrained by operating systems, Penpot utilizes the flexibility of web-based platforms and the interoperability
  of open standards, specifically SVG. Penpot invites designers all over the world to fall in love with open source
  while getting developers excited about the design process in return.
releaseNotes: >-
  This update includes bug fixes and improvements:
    - Fixed file importing issues that were preventing some files from being imported properly
    - Resolved problems with tokens disappearing after manual additions
    - Fixed incorrect handling of strokes with images when importing files
    - Improved error messages for better user experience
    - Fixed invitation link URLs


  Full release notes are found at https://github.com/penpot/penpot/releases
developer: Penpot
website: https://penpot.app/
dependencies: []
repo: https://github.com/penpot/penpot
support: https://github.com/penpot/penpot/discussions
port: 9001
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/605
