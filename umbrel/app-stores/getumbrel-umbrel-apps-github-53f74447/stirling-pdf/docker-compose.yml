version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: stirling-pdf_app_1
      APP_PORT: 8080
      PROXY_AUTH_WHITELIST: "/api/*"
  
  app:
    image: stirlingtools/stirling-pdf:0.46.2@sha256:396ca949b81198a65b9abfec38518fa44e871b1970dad9f9718ffd030eef9b92
    # Stirling PDF does not work with a custom user
    #user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      # Required for extra OCR languages
      - ${APP_DATA_DIR}/data/tessdata:/usr/share/tessdata
      - ${APP_DATA_DIR}/data/configs:/configs
      - ${APP_DATA_DIR}/data/logs:/logs
    environment:
      # Not needed as <PERSON><PERSON><PERSON> authenticates the user
      DOCKER_ENABLE_SECURITY: "false"
      INSTALL_BOOK_AND_ADVANCED_HTML_OPS: "true"
      LANGS: ALL
