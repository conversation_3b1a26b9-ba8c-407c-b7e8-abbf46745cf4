manifestVersion: 1
id: stirling-pdf
name: Stirling PDF
tagline: Merge, split, rotate, and convert PDFs with ease
category: files
version: "0.46.2"
port: 27829
description: >-
  Stirling PDF is a robust, locally hosted web-based PDF manipulation tool that offers a comprehensive set of features to address
  all your PDF requirements. It allows you to perform various operations on PDF files, including splitting, merging, converting,
  reorganizing, adding images, rotating, compressing, and more. All operations are performed locally, ensuring the utmost privacy
  and security of your files.


  ⚠️ Note

  After installation, Stirling PDF takes a few minutes to start up. Please be patient and wait for the app to download packages and fonts.


  📄 Page Operations
  
  Stirling PDF provides a full interactive GUI for merging, splitting, rotating, and moving PDFs and their pages. You can view and
  modify multi-page PDFs with custom viewing, sorting, and searching options. On-page edit features like annotation, drawing, and
  adding text and images are also available. You can also reorganize PDF pages into different orders, rotate PDFs in 90-degree
  increments, and remove pages.


  🔄️ Conversion Operations
  
  Convert PDFs to and from images, convert any common file to PDF, and convert PDF to Word, PowerPoint, and other formats. You can
  also convert HTML, URLs, and Markdown to PDF.


  🛡️ Security & Permissions
  
  Add and remove passwords, change or set PDF permissions, add watermarks, certify or sign PDFs, sanitize PDFs, and auto-redact text
  for enhanced security.


  🛠️ Other Operations:
  
  Add, generate, or write signatures, repair PDFs, detect and remove blank pages, compare two PDFs and show differences in text, add
  images to PDFs, compress PDFs to decrease their filesize, extract images from PDF, add page numbers, perform OCR on PDF, convert PDF/A,
  edit metadata, flatten PDFs, and get all information on a PDF to view or export as JSON.

developer: Stirling Tools
website: https://stirlingtools.com/
submitter: Sharknoon
submission: https://github.com/getumbrel/umbrel-apps/pull/1111
repo: https://github.com/Stirling-Tools/Stirling-PDF
support: https://github.com/Stirling-Tools/Stirling-PDF/discussions
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  This update of Stirling PDF includes several enhancements and improvements:


    - Legacy homepage has been removed due to great feedback and love from the community for the new homepage
    - Malayalam language added
    - Pro/Enterprise licenses now support floating between machines
    - Various bug fixes and minor improvements
    - Updated translations


  Full release notes are found at https://github.com/Stirling-Tools/Stirling-PDF/releases
dependencies: []
path: ""
