version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: woofbot_web_1
      APP_PORT: 8080
  
  web:
    image: woofbot/woofbot:v0.9.8@sha256:483076f0f364291d569025639ab502dc19c9c61d38931402955482e53cc3d950
    user: 1000:1000
    restart: on-failure
    stop_grace_period: 1m
    environment:
      MONGODB_URI: "mongodb://woofbot_mongodb_1:27017/woofbot"
      APP_PORT: "8080"
      APP_SEED: $APP_SEED
      APP_BITCOIN_NODE_IP: $APP_BITCOIN_NODE_IP
      APP_BITCOIN_RPC_USER: $APP_BITCOIN_RPC_USER
      APP_BITCOIN_RPC_PASS: $APP_BITCOIN_RPC_PASS
      APP_BITCOIN_RPC_PORT: $APP_BITCOIN_RPC_PORT
      SERVER_LOGS_FILEPATH: /app/logs/server.log
    volumes:
      - ${APP_DATA_DIR}/data/server-logs:/app/logs
    depends_on:
      - mongodb
  
  mongodb:
    # Newer mongo versions don't support Raspberry Pi 4 arm64
    image: mongo:4.4.6-bionic@sha256:3d0e6df9fd5bc42cbf8ef8bc9e6c4e78f6f26c7157dbd7bdec72d202ab8ebe3a
    user: 1000:1000
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data/db:/data/db
      - ${APP_DATA_DIR}/data/db/diagnostic.data:/data/db/diagnostic.data
      - ${APP_DATA_DIR}/data/db/journal:/data/db/journal
