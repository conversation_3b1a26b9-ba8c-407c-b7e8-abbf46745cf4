manifestVersion: 1.1
id: woofbot
category: bitcoin
name: WoofBot (without <PERSON>)
version: "0.9.8"
tagline: A chatbot for your personal node
description: >-
  WoofBot is a chat bot that runs on your personal Bitcoin node and sends telegram notifications
  for configured events: payments from/to specified addresses, transactions being confirmed,
  new-blocks and more.

  This version of WoofBot requires only Bitcoin Core to be installed, and does not integrate with
  any lightning api.
developer: WoofBotApp
website: https://github.com/woofbotapp
dependencies:
  - bitcoin
repo: https://github.com/woofbotapp/woofbotapp
support: https://github.com/woofbotapp/woofbotapp/discussions
port: 8092
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  - New emojies for notifications about address being paid
  
  - Upgrade server-side dependencies
submitter: WoofBotApp
submission: https://github.com/getumbrel/umbrel-apps/pull/28
