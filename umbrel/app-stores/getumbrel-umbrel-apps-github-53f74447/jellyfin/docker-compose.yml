version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: jellyfin_server_1
      APP_PORT: 8096
      PROXY_AUTH_ADD: "false"

  server:
    image: linuxserver/jellyfin:10.10.7@sha256:01c9d2311eb7710867fb3a8c2718068a276cff468a71d2a2dc58a0f5165ad0d2
    restart: on-failure
    hostname: "${DEVICE_HOSTNAME}"
    environment:
      - PUID=1000
      - PGID=1000
    volumes:
      - ${APP_DATA_DIR}/data/config:/config
      - ${UMBREL_ROOT}/data/storage/downloads:/downloads
    ports:
      # Service auto-discovery
      - 7359:7359/udp
