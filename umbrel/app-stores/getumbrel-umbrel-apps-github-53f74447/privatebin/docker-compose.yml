version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: privatebin_server_1
      APP_PORT: 8080
      PROXY_AUTH_ADD: "false"

  server:
    image: privatebin/nginx-fpm-alpine:1.7.8@sha256:201c3d34278ee5e1eb997c5fb38e24af1e0eb46e93d96029a5931a1b8406a4d8
    user: "1000:1000"
    read_only: true
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/privatebin-data:/srv/data
