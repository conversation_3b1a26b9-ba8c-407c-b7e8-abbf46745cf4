manifestVersion: 1
id: node-red-standalone
category: automation
name: "Node-RED"
version: "4.0.9"
tagline: Wire together the Internet of Things
description: >-
  Node-RED is a visual programming tool for wiring together hardware
  devices, APIs and online services in new and interesting ways.


  It provides a browser-based editor that makes it easy to wire together flows using the wide range of nodes in the palette that can be deployed to its runtime in a single-click. A built-in library allows you to save useful functions, templates or flows for re-use.


  The flows created in Node-RED are stored using JSON which can be easily imported and exported for sharing with others. An online flow library allows you to share your best flows with the world. 
releaseNotes: >-
  This release includes improvements and bug fixes:
    - Improved handling of context variables and environment access
    - Enhanced library browser and sidebar functionality
    - Better handling of node groups and subflows
    - Various UI improvements and fixes


  Full release notes are found at https://github.com/node-red/node-red/releases
developer: OpenJS Foundation
website: https://nodered.org
dependencies: []
repo: https://github.com/node-red/node-red
support: https://nodered.org/about/community/slack/
port: 1881
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: false
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/38
