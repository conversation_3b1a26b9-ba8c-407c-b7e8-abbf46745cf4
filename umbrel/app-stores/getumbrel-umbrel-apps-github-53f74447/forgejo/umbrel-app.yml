manifestVersion: 1.1
id: forgejo
category: developer
name: Forgejo
version: "11.0.1"
tagline: A self-hosted lightweight software forge
description: >-
  Forgejo is a self-hosted lightweight software forge, designed to be a fully self-hosted, privacy-respecting alternative to GitHub, GitLab, and Bitbucket. It is a fork of Gitea with additional features and community-driven enhancements. Forgejo is written in Go and can run on low-resource hardware like a Raspberry Pi. 


  🔑 Key features include:

  
  🏠 Fully self-hosted and private
  
  🎯 Issue tracker and project management
  
  👥 Account/Organization/Repository management
  
  🕶️ Tor support
  
  🛠️ Repository Git hooks/deploy keys
  
  📝 Repository issues, pull requests, and wiki
  
  👤 Add/Remove repository collaborators
  
  🌐 Gravatar and custom source
  
  🖥️ Admin panel
  
  ⚙️ Forgejo Actions for CI/CD
  
  🚀 Lightweight and performant


  🌍 Forgejo is brought to you by an inclusive community under the umbrella of Codeberg e.V., a democratic non-profit organization. It is 100% Free Software, focusing on security, scaling, federation, and privacy.

developer: Forgejo Community
website: https://forgejo.org/
dependencies: []
repo: https://codeberg.org/forgejo/forgejo
support: https://codeberg.org/forgejo/forgejo/issues
port: 8101
path: ""
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  This release includes various improvements and bug fixes.
  
  
  Key highlights:
    - Fixed LFS upload permissions allowing read-only users to upload files
    - Enforced 2FA security key use with external account logins
    - Improved runner task display and branch commit icon visibility
    - Adjusted UI spacing and icons for clarity
    - Updated translations and fixed logging for missing locale strings


  Full release notes are available at https://forgejo.org/releases/
submitter: John Wilddip
submission: https://github.com/getumbrel/umbrel-apps/pull/2221
