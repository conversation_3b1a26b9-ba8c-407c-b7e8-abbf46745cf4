version: "3.7"
services:
  app_proxy:
    environment:
      APP_HOST: albyhub_server_1
      APP_PORT: 8080
      PROXY_AUTH_ADD: "false"
  server:
    image: ghcr.io/getalby/hub:v1.17.2@sha256:49f9019bf218239c2cd57091524e130d4e74c815bd731d4e9c8b3d6deb7887c0
    user: 1000:1000
    volumes:
      - ${APP_DATA_DIR}/data:/data
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
    environment:
      LN_BACKEND_TYPE: "LND"
      LND_ADDRESS: $APP_LIGHTNING_NODE_IP:$APP_LIGHTNING_NODE_GRPC_PORT
      LND_CERT_FILE: "/lnd/tls.cert"
      LND_MACAROON_FILE: "/lnd/data/chain/bitcoin/$APP_BITCOIN_NETWORK/admin.macaroon"
      WORK_DIR: "/data/albyhub"
      COOKIE_SECRET: ${APP_SEED}
      LOG_EVENTS: "true"
    restart: on-failure
    stop_grace_period: 3m
