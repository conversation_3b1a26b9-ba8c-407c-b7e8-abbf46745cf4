manifestVersion: 1
id: albyhub
name: Alby Hub ✨
tagline: Self-custodial Lightning wallet with integrated node and app connections
category: bitcoin
version: "1.17.2"
port: 59000
description: >-
  Alby Hub is an open-source, self-custodial Bitcoin Lightning wallet, with the easiest-to-use Lightning Network node for everyone.
  Whether you're an individual, creator, or developer, Alby Hub is your centre for seamless Bitcoin payments.


  Effortlessly connect to a variety of apps like the Alby Browser Extension or Alby Go mobile app, create sub-wallets for family and friends, and take full control of your funds—all within an intuitive interface and developer-ready APIs.


  USEFUL LINKS
    - Feature requests, bug reports, feedback board - https://feedback.getalby.com/-alby-hub-request-a-feature
    - Community of users and developers - https://discord.getalby.com/
    - List of apps and projects that use NWC protocol - https://github.com/getAlby/awesome-nwc

developer: Alby
website: https://albyhub.com
submitter: Alby
submission: https://github.com/getumbrel/umbrel-apps/pull/1409
repo: https://github.com/getAlby/hub
support: https://support.getalby.com
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
releaseNotes: >-
  This release introduces auto-swaps to ensure you can always receive payments, an onchain transaction list on the node page, and a redesigned sub-wallets page (formerly Friends & Family).


  Key highlights include:
    - Auto-swaps for guaranteed payment reception
    - Onchain transaction list added to the node page
    - Redesigned sub-wallets page with improved functionality
    - Receive UI improvements
    - Added update banner and info page in settings
    - Various minor improvements and bug fixes


  This hotfix addresses two important issues:
    - Added LNServer_Wave to trusted peers for 0-conf channels
    - Updated rust lightning dependency to fix a rare startup issue for nodes that recently experienced a channel HTLCsTimedOut


  Full release notes are found at https://github.com/getAlby/hub/releases
dependencies:
  - lightning
path: ""
defaultUsername: ""
defaultPassword: ""
