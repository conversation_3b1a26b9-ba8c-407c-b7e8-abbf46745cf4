manifestVersion: 1
id: usocial
category: social
name: usocial
version: "0.1.7-build-1"
tagline: Read. Listen. Pay back.
description: >-
  The podcast client and feed reader for your personal server. With
  Lightning Network support.


  - Simple interface (minimalist HN-like design)

  - Optionally multi-user

  - Decentralized karma

  - Clean code (Python / Flask / SQLite)

  - Search podcasts from Podcast Index

  - Support for the Podcasting 2.0 value tag, sending Bitcoin payments to podcast creators over the Lightning Network
developer: Ioan Bizău
website: http://usocial.me
dependencies:
  - lightning
repo: https://github.com/ibz/usocial
support: https://github.com/ibz/usocial/issues
port: 8448
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: true
torOnly: false
submitter: <PERSON>oan Bizău
submission: https://github.com/getumbrel/umbrel/pull/1247
releaseNotes: ""