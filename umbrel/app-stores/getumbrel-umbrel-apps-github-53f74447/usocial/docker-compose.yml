version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: usocial_web_1
      APP_PORT: 5000

  web:
    image: ghcr.io/ibz/usocial:v0.1.5-buster@sha256:1b1215d7fba847bf922a2615fee6400ce61cac4545dff6bfcf9703af8e05b953
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
      - ${APP_DATA_DIR}/data:/instance
    environment:
        USOCIAL_JOB: "WEB"
        APP_PASSWORD: "${APP_PASSWORD}"
        LND_IP: "${APP_LIGHTNING_NODE_IP}"
        LND_GRPC_PORT: ${APP_LIGHTNING_NODE_GRPC_PORT}
        LND_DIR: "/lnd"
  
  fetcher:
    depends_on:
      - web
    image: ghcr.io/ibz/usocial:v0.1.7-buster@sha256:864d0d225ed3f48060e6020abbe95355c99fb4426da3fdfb4e185581ccd0368b
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data:/instance
    environment:
        USOCIAL_JOB: "FETCH_FEEDS"
