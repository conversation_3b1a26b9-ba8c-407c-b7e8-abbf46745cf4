manifestVersion: 1.1
id: navidrome
category: media
name: Navidrome
version: "0.56.1"
tagline: Your personal streaming service
description: >-
  Navidrome is an open source web-based music collection server and streamer.
  It gives you freedom to listen to your music collection from any browser or mobile device. It's like your personal Spotify!


  Features:

  - Handles very large music collections

  - Streams virtually any audio format available

  - Reads and uses all your beautifully curated metadata

  - Great support for compilations (Various Artists albums) and box sets (multi-disc albums)

  - Multi-user, each user has their own play counts, playlists, favourites, etc...

  - Very low resource usage

  - Multi-platform, runs on macOS, Linux and Windows. Docker images are also provided

  - Ready to use binaries for all major platforms, including Raspberry Pi

  - Automatically monitors your library for changes, importing new files and reloading new metadata

  - Themeable, modern and responsive Web interface based on Material UI

  - Compatible with all Subsonic/Madsonic/Airsonic clients

  - Transcoding on the fly. Can be set per user/player. Opus encoding is supported

  - Translated to various languages

developer: Navidrome
website: https://www.navidrome.org/
dependencies: []
repo: https://github.com/navidrome/navidrome
support: https://github.com/navidrome/navidrome/discussions
port: 4533
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
permissions:
  - STORAGE_DOWNLOADS
releaseNotes: >-
  This release includes new features, improvements and bug fixes.


  Highlights include:
    - Bulk removal and auto-purge of missing files
    - Playlist improvements: M3U downloads, cover art, save queue as playlist
    - UI enhancements: smooth image transitions, real-time scan progress, sample rate display
    - Filesystem lyrics and ISRC support
    - Fixed potential SQL injection vulnerability (CVE pending)
    - Restricted transcoding config changes to admin users
    - Symlink and artist filtering options
    - Fix for Artists not showing up in Subsonic clients
    - Optimized scanner performance


  Full release notes can be found at https://github.com/navidrome/navidrome/releases
submitter: owmsoton
submission: https://github.com/getumbrel/umbrel-apps/pull/1232
