version: "3.7"

services:
  web:
    image: aceberg/watchyourlan:2.1.2@sha256:a78d41fe76e3774a2df6e9d2d2943f1d7e6271f21fe752c1b92961f21752a79e
    network_mode: "host"        
    restart: on-failure
    stop_grace_period: 1m
    init: true
    environment:
      TZ: "UTC"                         # required: needs your TZ for correct time
      IFACES: "eth0 wlan0 enp1s0"       # required: 1 or more interface
      HOST: "0.0.0.0"                   # optional, default: 0.0.0.0
      PORT: "8840"                      # optional, default: 8840
      # Exclude to allow custom config:
      # TIMEOUT: "120"                    # optional, time in seconds, default: 120
      # THEME: "sand"                     # optional
      # COLOR: "dark"                     # optional
    volumes:
      - ${APP_DATA_DIR}/data:/data/WatchYourLAN
