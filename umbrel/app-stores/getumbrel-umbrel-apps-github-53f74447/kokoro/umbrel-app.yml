manifestVersion: 1
id: kokoro
name: Kokoro
tagline: An intelligent, high-quality TTS solution
category: ai
version: "0.2.4"
port: 8877
description: >-
  Kokoro is an advanced Text-to-Speech (TTS) model that delivers impressive speech quality with only 82 million parameters, making it competitive with much larger and more resource-intensive models. Despite its relatively compact architecture, Kokoro effectively transforms text into clear, natural-sounding speech, making it an excellent choice for applications relying on speech synthesis. The model has been specifically designed to ensure high efficiency and fast processing, making it suitable for both resource-constrained environments and production systems. In comparison to traditional TTS models, which often require substantial computational resources, Kokoro offers a more cost-effective and faster alternative without compromising the quality of speech output.


  Its lightweight architecture ensures that Kokoro can be deployed even on less powerful devices, making it easier to integrate into various applications. Developers can use Kokoro in a wide range of projects, whether for virtual assistants, interactive systems, or enhancing accessibility. The model not only provides accurate and intelligible speech, but also introduces emotional nuances that enhance the user experience. With its flexibility and ability to be applied across diverse scenarios, Kokoro is a valuable resource for anyone seeking an efficient, lightweight, and powerful speech synthesis solution in their projects.


  ⚠️ This app only works in Chromium-based browsers (e.g., Chrome, Edge, Brave) and is available at "umbrel.local:8877/web/". Please note that the app is approximately 4GB in size, so the loading process may take a few moments.


  ⚙️ The API is available at "umbrel.local:8877", and the API documentation can be found at "umbrel.local:8877/docs".
developer: Hexgrad
website: https://kokorotts.net/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2498
repo: https://github.com/remsky/Kokoro-FastAPI
support: https://github.com/remsky/Kokoro-FastAPI/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  This release includes numerous improvements and fixes:


    - Added support for Apple Silicon with MPS acceleration
    - Improved text normalization and parsing capabilities
    - Enhanced audio quality and pause handling
    - Added Chinese punctuation-based sentence splitting
    - Fixed segmentation faults and various stability issues
    - Added volume multiplier setting
    - Improved streaming and download functionality
    - Better safety checks for captioned speech
    - Fixed phenome handling and normalization issues


  Full release notes are available at https://github.com/remsky/Kokoro-FastAPI/releases/tag/v0.2.4
dependencies: []
path: "/web"
defaultUsername: ""
defaultPassword: ""
