manifestVersion: 1
id: urbit
category: networking
name: Urbit
version: "v3.2-1"
tagline: Run Urbit on your Umbrel
description: >-
  Urbit is a personal server for self-sovereign personal & networked
  computing. Nock, a functional combinator, is built into Hoon, a system
  programming language, used to implement Arvo, a deterministic operating
  system, that communicates over Ames, a decentralized, encrypted P2P network.


  This app helps you boot and manage Urbit instances on your Umbrel. It provides a simple management GUI, start and stop operations, and passes your +code through the interface.


  If you are interested in purchasing an L2 planet checkout: subject.network/buy


  Credit to ~timluc-miptev, ~master-forwex, ~sipsen-pilser & ~rivpyl-sidfyl
developer: ~mopfel-winrux & ~sitful-hatred
website: https://github.com/mopfel-winrux/urbit-umbrel
dependencies: []
repo: https://github.com/mopfel-winrux/urbit-umbrel
support: https://github.com/mopfel-winrux/urbit-umbrel/issues
port: 8090
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: /launch
defaultUsername: umbrel
deterministicPassword: true
torOnly: false
submitter: ~mopfel-winrux
submission: https://github.com/getumbrel/umbrel/pull/1246
releaseNotes: >-
  This release is a rewrite of the Urbit Umbrel app with a modern frontend and monolithic self-contained backend. 

  
  `vere-v3.2` brings the following highlighted features:
    - Compatibility with the upcoming zuse 410 kelvin release.
    - A directed messaging driver for 1-3 orders of magnitude faster networking performance.
    - Lagoon jets for native matrix math in Urbit.
    - Double boot protection to prevent the most common cause of irrecoverable ship failure.
    - Loom memory usage statistics in Arvo to help hosting providers and provide a foundation for zero click maintenance.
    - HTTP scry streaming for serving individual chunks of large files over HTTP.
    - Note that all these features are dependent on the 410k Arvo release.


  Moon keyfiles generated with Arvo versions of 411 or above are not compatible with vere-v3.2. To recreate an old moon keyfile, upgrade Arvo to 410 first and then do |moon-cycle-keys <moon-name>. This only applies to the moon keyfiles, previously booted moons can upgrade to vere-v3.2 without issue.


  Full release notes here: https://github.com/urbit/vere/releases/tag/vere-v3.2
