version: "3.7"
services:
  app_proxy:
    environment:
      APP_HOST: urbit_manager_1
      APP_PORT: 8090
      PROXY_AUTH_ADD: "false"
  
  manager:
    image: mopfelwinrux/urbit-umbrel:3.2@sha256:757619c372235ca09b10e20fa990075ff92d16a2327de6a8f9824d623059498a
    ports:
      - "34343:34343"
    volumes:
      - ${APP_DATA_DIR}/piers:/data/piers
      - ${APP_DATA_DIR}/keys:/data/keys
    environment:
      FLASK_APP: app
      APP_PASSWORD: ${APP_PASSWORD}
