version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: nginx-proxy-manager_web_1
      APP_PORT: 81
      
  web:
    image: jc21/nginx-proxy-manager:2.12.4@sha256:c4b78efef47879a693cdd04e6c0d06f3c37e9626bf9101c4686a4c12033339ad
    hostname: nginx-proxy-manager_web_1
    restart: on-failure
    ports:
      - 40080:80
      - 40443:443
    volumes:
      - ${APP_DATA_DIR}/data/npm-data:/data
      - ${APP_DATA_DIR}/data/letsencrypt:/etc/letsencrypt
    environment:
      PUID: 1000
      PGID: 1000
  
  docker-host:
    image: qoomon/docker-host:3.3.1@sha256:ec9d214d7fa88932e9186ca2f4419d44661376929931248525b08e7fa9e270f7
    hostname: $DEVICE_DOMAIN_NAME
    cap_add:
      - NET_ADMIN
      - NET_RAW
    restart: on-failure
