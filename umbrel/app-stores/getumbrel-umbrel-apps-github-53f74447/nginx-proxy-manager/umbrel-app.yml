manifestVersion: 1
id: nginx-proxy-manager
name: Nginx Proxy Manager
tagline: Expose your services easily and securely
category: networking
version: "2.12.4"
port: 4498
description: >-
  Expose your apps to the internet easily and securely.


  ⚠️ Be cautious when exposing apps to the public internet. Ensure they have proper security, such as login protection, and avoid exposing sensitive apps without adequate safeguards.
  

  🔧 Nginx Proxy Manager uses port 40080 for HTTP (unsecured) traffic and port 40443 for HTTPS (secured) traffic.
  To make your apps accessible from the public internet, you will need to set up port forwarding on your router.
  Forward external port 80 (HTTP) to internal port 40080 and external port 443 (HTTPS) to internal port 40443.
  

  🔍 Features:
  
    - Beautiful and Secure Admin Interface based on Tabler
    - Easily create forwarding domains, redirections, streams and 404 hosts without knowing anything about Nginx
    - Free SSL using Let's Encrypt or provide your own custom SSL certificates
    - Access Lists and basic HTTP Authentication for your hosts
    - Advanced Nginx configuration available for super users
    - User management, permissions and audit log  
developer: <PERSON>
website: https://nginxproxymanager.com/
submitter: Sahil <PERSON>ule
submission: https://github.com/getumbrel/umbrel-apps/pull/1296
repo: https://github.com/NginxProxyManager/nginx-proxy-manager
support: https://github.com/NginxProxyManager/nginx-proxy-manager/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  Key highlights in this release:
    - API Schema Improvements
    - Updated copyright year to 2025
    - Fixed incorrect API status codes
    - Optimized certbot ownership script to reduce container startup time
    - Added IP_RANGES_FETCH_ENABLED environment variable
    - Fixed an issue with 500 error code on the Stream list page
    - Added and updated several DNS providers for certbot plugins


  Full release notes are available at https://github.com/NginxProxyManager/nginx-proxy-manager/releases
dependencies: []
path: ""
defaultUsername: "<EMAIL>"
defaultPassword: "changeme"
