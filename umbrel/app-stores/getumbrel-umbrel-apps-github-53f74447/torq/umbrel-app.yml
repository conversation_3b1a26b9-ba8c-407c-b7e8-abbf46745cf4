manifestVersion: 1
id: torq
category: bitcoin
name: Torq
version: "v2.2.2"
tagline: Scalable Node Management Software
description: >-
  Operating a Lightning node requires a lot of work. You need to monitor your channels, rebalance them, keep track of your fees and much more.


  With Torq, you can collect and analyze your data with increasingly advanced tools.


  Features:

  - Advanced decision engine for automation. Create your own logic visually through workflows.
  
  - Notifications via Telegram, Discord and Slack (with advanced logic and templating)

  - Fast even with more than 1000 channels.
  
  - Advanced charts and visualizations of aggregated forwarding statistics

  - Manage multiple nodes at once.

  - Filter, sort and group data

  - Stores all events from your node including HTLC events, fee rate changes and channel enable/disable events

  - Store custom table views configurations for quickly finding the right information

  - Fetch and analyse data from any point in time

  - Navigate through time (days, weeks, months) to track your progress

  - Tag channels and nodes with custom labels (Exchange, Routing node, Sink, Source, or anything else)
developer: Torq
website: https://torq.co
dependencies:
  - lightning
repo: ""
support: https://github.com/lncapital/torq/issues/new
port: 7028
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
releaseNotes: >-
  Highlights:
    - Torq API node handle grouping
      - A possibility to make hierarchy of nodes that are used in Torq API.
      - For example: primarly use Node A, but if it is unreachable, use Node B. Or: use Node A 70% of the time and Node B 30% of the time.
    - Node data import downtime threshold
      - Configurable threshold for when to run events and imports for node data depending on how long was Torq down for.
      - For example: there is a workflow that is set to trigger on "Payment". Then a payment happened on the node while Torq was down. If Torq was down less than the threshold that was set, the workflow **is** triggered when Torq starts, and if Torq was down longer than the threshold, the workflow **is not** triggered.
    - Possibility to add/remove tags via Tags -> Manage -menu
    - Show tagging history on "Inspect Channel" -page
    - Bug fixes and minor improvements:
       - Bitcoind node status now shown in the "status modal"
       - Fixed "last balance change" filter and make columns sortable by "last balance change"
       - Fixed bug when updating CLN node connection details
       - Ordered tags list on "Tags" page alphabetically
       - Fixed bolt11 invoice not working in signet
       - More accurate "unconfirmed" balance on bitcoind node
       - mempool-url conf parameter now allows trailing slash
       - Fixed an UI bug that created an error in UI when making an on-chain payment
       - "Contact support" text in footer is now clickable and opens support chat window
       - In earlier release an internal table of "payment attempt" was added that saves each attempted payment even if the node with which to make the payment is not reachable. This might bloat the database too much if there are a lot of payments. This release adds a new setting to set the amount of days that they are kept before being deleted from database (If zero, deleted after 1h).
       - Fixed CLN showing doubled balance while on-chain transaction to self is unconfirmed  
       - Fix a race condition in LND channel balance cache that might cause Torq to crash
       - Fix bugs that caused channel balance to not be updated in some cases and channel "peer total local balance" not getting updated.

path: ""
defaultUsername: ""
deterministicPassword: false
submitter: Torq
submission: https://github.com/getumbrel/umbrel-apps/pull/549
