version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: torq_web_1
      APP_PORT: 7028

  web:
    image: lncapital/torq:2.2.2@sha256:28cfac8c81f92e7d4e021a780b2d3f2dc766a37c7ebeef68f6105f682f3b196f
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
    command:
      - --torq.port
      - "7028"
      - --db.host
      - torq_db_1
      - --db.password
      - moneyprintergobrrr
      - --torq.password
      - ${APP_PASSWORD}
      - --lnd.url
      - ${APP_LIGHTNING_NODE_IP}:${APP_LIGHTNING_NODE_GRPC_PORT}
      - --lnd.macaroon-path
      - /lnd/data/chain/bitcoin/${APP_BITCOIN_NETWORK}/admin.macaroon
      - --lnd.tls-path
      - /lnd/tls.cert
      - --torq.auto-login
      - start

  db:
    image: timescale/timescaledb:2.8.1-pg14@sha256:96c3a5587a24bb336437403b394f5927f639322cf3fcc0eff4ee0294da540dd7
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    environment:
      POSTGRES_PASSWORD: moneyprintergobrrr
    volumes:
      - ${APP_DATA_DIR}/data/torq:/var/lib/postgresql/data
      - ${APP_DATA_DIR}/data/passwd:/etc/passwd:ro # Enables postgres to init on user 1000
