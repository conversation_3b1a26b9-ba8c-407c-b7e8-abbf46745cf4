version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: readarr_server_1
      APP_PORT: 8787
      PROXY_AUTH_WHITELIST: "/api/*"

  server:
    image: linuxserver/readarr:develop-version-0.4.18.2805@sha256:b6a7420770adeea5cac1f3ee5da606183d014e3f96fb473f3abd88ed11d9f344
    environment:
      - PUID=1000
      - PGID=1000
    volumes:
      - ${APP_DATA_DIR}/data/config:/config
      - ${UMBREL_ROOT}/data/storage/downloads:/downloads
    restart: on-failure

  mac:
    image: getumbrel/media-app-configurator:v1.3.0@sha256:67e75dd9f5a14402b7816119a8e20189bc2465484cea077909d164687e59742b
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/config:/config
      - ${UMBREL_ROOT}/data/storage/downloads:/downloads
    environment:
      APP_ID: "readarr"
      APP_URL: "http://readarr_server_1:8787"
      TRANSMISSION_HOST: "transmission_server_1"
      TRANSMISSION_PORT: 9091
      ROOT_FOLDER: "/downloads/books"
      # optional qBittorrent download client
      QBITTORRENT_INSTALLED: ${APP_READARR_QBITTORRENT_INSTALLED:-"false"}
      QBITTORRENT_HOST: "qbittorrent_server_1"
      QBITTORRENT_PORT: 8080
      # optional SABnzbd download client
      SABNZBD_INSTALLED: ${APP_READARR_SABNZBD_INSTALLED:-"false"}
      SABNZBD_HOST: "sabnzbd_web_1"
      SABNZBD_PORT: 8080
      SABNZBD_API_KEY: ${APP_READARR_SABNZBD_API_KEY:-""}
