manifestVersion: 1.1
id: readarr
category: media
name: Readarr
version: "0.4.18.2805"
tagline: Your book collection manager
description: >-
  ⚠️ Readarr is currently in beta testing and is generally still a work in progress. Features may be broken, incomplete, or cause spontaneous combustion.


  Readarr is an ebook and audiobook collection manager for Usenet and BitTorrent users. It can monitor multiple RSS feeds for new books from your favorite authors and will grab, sort, and rename them. Note that only one type of a given book is supported. If you want both an audiobook and ebook of a given book you will need multiple instances.


  🛠️ SETUP INSTRUCTIONS

  Readarr on umbrelOS will automatically connect to download clients installed from the Umbrel App Store. Choose from Transmission, qBittorerent, and SABnzbd. Simply install your preferred client(s).


  All you need to do from there is add an indexer so Readarr can search for books. You can add indexers directly within Readarr, or install Prowlarr from the Umbrel App Store for easier management of indexers across multiple apps.
  Add your indexers to Prowlarr and they will be automatically available in Readarr.
developer: Readarr
website: https://readarr.com/
dependencies:
  - transmission
repo: https://github.com/Readarr/Readarr
support: https://github.com/Readarr/Readarr/issues
port: 8787
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: false
permissions:
  - STORAGE_DOWNLOADS
submitter: Choff3
submission: https://github.com/getumbrel/umbrel-apps/pull/712
releaseNotes: >-
  This update includes several improvements and bug fixes:
    - Fixed remote image links for book posters and covers
    - Updated translations
    - Improved performance by optimizing assembly loading
    - Fixed fullscreen automation screenshots


  Full release notes are found at https://github.com/Readarr/Readarr/releases
