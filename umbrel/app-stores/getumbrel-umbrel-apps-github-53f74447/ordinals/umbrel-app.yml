manifestVersion: 1
id: ordinals
category: bitcoin
name: Ordinals
version: "0.22.2"
tagline: Run your own index, block explorer, and command-line wallet for Ordinals
description: >
  Run your own index, block explorer, and command-line wallet for Ordinals. The app automatically connects to your Bitcoin node on umbrelOS for trustless operation. Simply install the app and wait for Ordinals to index inscriptions and runes.


  Use the block explorer to view Ordinal inscriptions. Search blocks, transactions, outputs, and individual satoshis.


  Advanced users can use the command-line to create wallets, create and manage inscriptions, and more. A future umbrelOS update will allow you to run commands from a Terminal directly within the umbrelOS interface!


  Disclaimer: The Ordinals app does not control, filter, or moderate the content hosted on the Bitcoin blockchain. Consequently, you may come across NSFW (Not Safe For Work), objectionable, or unlawful material while using the app.
releaseNotes: >-
  🚨 As usual, major version updates require ord to re-index from scratch. This process will happen automatically when you update the app. 


  ⏳ If ord is currently indexing, it may take a few minutes to safely shut down before updating. Please be patient during this process.


  This release fixes an issue where ord's cli was unable to connect to recent versions of Bitcoin Core.


  Full release notes can be found at https://github.com/ordinals/ord/releases
developer: <PERSON>
website: https://ordinals.com/
dependencies:
  - bitcoin
repo: https://github.com/casey/ord
support: https://discord.gg/ordinals
port: 4000
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: false
torOnly: false
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/458