version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: ordinals_ord_1
      APP_PORT: 80
      PROXY_AUTH_ADD: "false"

  ord:
    image: nmfretz/ord:0.22.2@sha256:95c64da92c51f0d4c4b810b5fb067a3119cc8040cbb9ae976d33c85d257450b3
    # This needs to run as root
    # user: "1000:1000"
    restart: on-failure
    stop_grace_period: 15m30s
    command: "ord server --http"
    volumes:
      - ${APP_DATA_DIR}/data/ord:/var/lib/ord
      - ${APP_BITCOIN_DATA_DIR}:/var/lib/bitcoind:ro
    environment:
      ORD_DATA_DIR: "/var/lib/ord"
      ORD_BITCOIN_DATA_DIR: "/var/lib/bitcoind"
      ORD_COOKIE_FILE: "/var/lib/bitcoind/.cookie"
      ORD_BITCOIN_RPC_USERNAME: "${APP_BITCOIN_RPC_USER}"
      ORD_BITCOIN_RPC_PASSWORD: "${APP_BITCOIN_RPC_PASS}"
      ORD_BITCOIN_RPC_URL: "${APP_BITCOIN_NODE_IP}:${APP_BITCOIN_RPC_PORT}"
      ORD_CHAIN: "${APP_BITCOIN_NETWORK}"
      ORD_INDEX_RUNES: "true"
      # TODO: consider index-sats
