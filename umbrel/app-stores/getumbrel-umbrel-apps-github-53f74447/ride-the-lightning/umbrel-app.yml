manifestVersion: 1
id: ride-the-lightning
category: bitcoin
name: Ride The Lightning
version: "0.15.4-boltz-client-2.7.1"
tagline: A powerful dashboard for the Lightning Network
description: >-
  RTL is a full function, device agnostic, web user interface to help
  manage lightning node operation. It enables full control over your lightning
  node with rich functionality for Bitcoin base layer and Lightning Network.
  Some cool features available on RTL are:


  - Rich dashboard with two layout options

  - Send/Receive funds on-chain

  - Rich channel management with advanced features like balance score, circular re-balancing etc.

  - Payments and Invoice management with QR codes

  - Routing analysis for the payments forwarded

  - Channel backups

  - Detailed reports on routing and transaction history

  - Optional Loop interface for submarine swaps

  - Customizable UI with multiple color schemes and dark/light modes
developer: <PERSON><PERSON> and Suheb
website: https://github.com/Ride-The-Lightning/RTL
dependencies:
  - lightning
repo: https://github.com/Ride-The-Lightning/RTL
support: https://github.com/Ride-The-Lightning/RTL/issues/new
port: 3001
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  This updates Boltz Client to v2.7.1. The update doesn't include new features for RTL users, but various bug fixes and stability improvements.


  The full release notes for the latest Boltz Client releases can be found at https://github.com/BoltzExchange/boltz-client/releases.

  About Boltz: https://boltz.exchange
path: ""
defaultUsername: ""
deterministicPassword: true
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/pull/336
