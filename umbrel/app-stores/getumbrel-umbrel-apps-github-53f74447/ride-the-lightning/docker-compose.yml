version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: ride-the-lightning_web_1
      APP_PORT: 3001
  
  web:
    image: shahanafarooqui/rtl:v0.15.4@sha256:f984095949b5b6c2c0c7d983e979cd34bd32a2952d106092bad0b46e6248168e
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/rtl:/data
      - ${APP_DATA_DIR}/boltz:/boltz:ro
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
      - ${APP_BITCOIN_DATA_DIR}:/bitcoin:ro
    entrypoint: /data/entrypoint.sh
    environment:
        # App config
        APP_PASSWORD: $APP_PASSWORD
        PORT: 3001
        RTL_CONFIG_PATH: "/data"
        CHANNEL_BACKUP_PATH: "/data/backup"
        LN_IMPLEMENTATION: "LND"
        BLOCK_EXPLORER_URL: "${APP_RTL_BLOCK_EXPLORER_URL}"

        # LND connection details
        LN_SERVER_URL: "https://$APP_LIGHTNING_NODE_IP:$APP_LIGHTNING_NODE_REST_PORT"
        MACAROON_PATH: "/lnd/data/chain/bitcoin/$APP_BITCOIN_NETWORK"
        CONFIG_PATH: "/lnd/umbrel-lnd.conf"

        # Boltz
        BOLTZ_SERVER_URL: "https://ride-the-lightning_boltz_1:9003"
        BOLTZ_MACAROON_PATH: "/boltz/.boltz-lnd/macaroons"

  boltz:
    image: boltz/boltz-client:2.7.1@sha256:242069c1817541f880a7d91a7aef5415924a1c867a23116326ceb810b1095b80
    user: "1000:1000"
    restart: "on-failure"
    stop_grace_period: "1m"
    environment:
      HOME: "/data"
    volumes:
      - "${APP_DATA_DIR}/boltz:/data"
      - "${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro"
    command:
      - --datadir=/data/.boltz-lnd
      - --lnd.host="$APP_LIGHTNING_NODE_IP"
      - --lnd.macaroon="/lnd/data/chain/bitcoin/$APP_BITCOIN_NETWORK/admin.macaroon"
      - --lnd.certificate="/lnd/tls.cert"
      - --rpc.rest.host="0.0.0.0"
      - --rpc.rest.port="9003"
