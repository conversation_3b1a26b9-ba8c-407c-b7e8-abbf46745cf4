manifestVersion: 1
id: octoprint
category: files
name: OctoPrint
version: "1.11.2"
tagline: A snappy web interface for your 3D printer
description: >-
  OctoPrint provides a snappy web interface for controlling consumer 3D printers.
  OctoPrint's powerful plugin system allows extending its functionality with awesome plugins from the community.


  Plug your 3D printer into your Umbrel to control and monitor every aspect of your 3D printer and your printing jobs right from within your browser.
developer: OctoPrint
website: https://octoprint.org/
repo: https://github.com/OctoPrint/OctoPrint
support: https://community.octoprint.org/
port: 5003
submitter: mateosilguero
releaseNotes: >-
  🚨 This release includes important security fixes that address file exfiltration and denial of service vulnerabilities. Update is strongly recommended.


  Key highlights in this release:
    - Fixed security vulnerabilities including file exfiltration and denial of service attacks
    - Added option to disable confirmation dialog when deleting files
    - Fixed connection issues with printers like Prusa MK3(s) when "wait for start on connect" is disabled
    - Fixed validation error causing deadlock when connecting without available serial ports
    - Disabled connect button when no serial ports are available
    - Improved security for template translations


  Full release notes are found at https://github.com/OctoPrint/OctoPrint/releases
submission: https://github.com/getumbrel/umbrel-apps/pull/449
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
