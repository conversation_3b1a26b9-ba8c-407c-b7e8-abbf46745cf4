manifestVersion: 1
id: squeakroad
category: bitcoin
name: Squeak Road
version: "0.1.14-build-2"
tagline: Run your own anonymous market
description: >-
  Squeak Road lets you run an anonymous market where anyone can buy or sell anything.

  As the admin of your market, you can curate which listings are allowed, and you can configure
  the fee rate that will be collected from sales.

  Buyers can leave ratings for each completed sale, and the ratings are used to calculate a score
  for each seller account.

  Warning: This app exposes your Lightning node pubkey. Be careful not to use any other app or invoice that links your Lightning node to your identity or your location.
developer: <PERSON>
website: https://github.com/yzernik/squeakroad
dependencies:
  - lightning
repo: https://github.com/yzernik/squeakroad
support: https://t.me/squeakroad
port: 18200
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: "umbrel"
deterministicPassword: true
releaseNotes: >-
  - Show better error message on failed user signup validation.
  - Don't fetch font assets from remote CDN.
submitter: <PERSON>
submission: https://github.com/getumbrel/umbrel-apps/pull/70