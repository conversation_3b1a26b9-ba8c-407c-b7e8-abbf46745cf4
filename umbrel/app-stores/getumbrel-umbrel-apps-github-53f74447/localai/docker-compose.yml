version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: localai_api_1
      APP_PORT: 8080
      PROXY_AUTH_WHITELIST: "/v1/*"
  api:
    image: localai/localai:v3.1.1@sha256:01a3d9cf057ae5f95370ee506da36592517a0ead7a413aadd6ee7b8c73b70417
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/readyz"]
      interval: 1m
      timeout: 20m
      retries: 5
    environment:
      - MODELS_PATH=/models
    volumes:
      - ${APP_DATA_DIR}/data/models:/models:cached
      - ${APP_DATA_DIR}/data/images/:/tmp/generated/images/
