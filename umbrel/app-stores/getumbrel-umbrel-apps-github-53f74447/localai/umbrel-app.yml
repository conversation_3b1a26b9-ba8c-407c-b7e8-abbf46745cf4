manifestVersion: 1
id: localai
category: ai
name: LocalAI
version: "v3.1.1"
tagline: Drop-in OpenAI replacement
description: >-
  LocalAI is the free, Open Source OpenAI alternative. LocalAI act as a drop-in replacement REST API that's compatible with OpenAI API specifications for local inferencing.
  
  
  It allows you to run LLMs, generate images, audio locally with consumer grade hardware, supporting multiple model families and architectures.


  ⚠️ Note

  Before running a model, make sure your device has enough free RAM to support it. Attempting to run a model that exceeds your available memory could cause your device to crash or become unresponsive. Always check the model requirements before downloading or starting it.
releaseNotes: >-
  This release includes several improvements and new features:


    - Automatically install missing backends along with models
    - Support for Gemma 3n models (text generation only)
    - Meta packages in backend galleries for easier GPU-specific setup
    - Improved backend gallery with descriptions and re-ordering
    - Various bug fixes and dependency updates


  Full release notes can be found at https://github.com/mudler/LocalAI/releases
developer: Ettore Di Giacinto
website: https://localai.io/
dependencies: []
repo: https://github.com/mudler/LocalAI
support: https://github.com/mudler/LocalAI/discussions
port: 8793
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: highghlow
submission: https://github.com/getumbrel/umbrel-apps/pull/1079
