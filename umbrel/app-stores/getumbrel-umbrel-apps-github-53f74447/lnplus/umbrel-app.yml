manifestVersion: 1
id: lnplus
category: bitcoin
name: Lightning Network+
version: "0.1.31"
tagline: Get incoming liquidity and double your channels for free
description: >-
  Lightning Network Plus (LN+) is a free app that helps lightning node operators to team up and create liquidity swaps.
  

  We will walk you through each step to make it an easy process. You will get incoming liquidity, and will be able to grow your channel count and capacity twice as fast, without any extra costs.
  

  The app will automatically log you in with your node. You can join a swap or you can create a new one. You can chat with other node operators. LN+ will help you open the channel with a single click, or you can open the channel manually with the app of your choice. You can rate swap participants, and you will receive ratings to build your node's reputation.
releaseNotes: >-
  Updated help pages and error handling.
developer: Lightning Network+
website: https://lightningnetwork.plus
dependencies:
  - lightning
repo: https://github.com/Lightning-Network-Plus/lnpclient
support: https://github.com/Lightning-Network-Plus/lnpclient/discussions
port: 3777
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: Lightning Network+
submission: https://github.com/getumbrel/umbrel-apps/pull/202