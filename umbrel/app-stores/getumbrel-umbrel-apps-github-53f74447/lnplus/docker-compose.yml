version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: lnplus_web_1
      APP_PORT: "3777"
  web:
    image: lightningnetworkplus/lnpclient:v0.1.31@sha256:35a8f299ee09c70318f93d7b3f0332215eb4e7fc0de75f527b6e49438722160c
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
    environment:
      LN_IMPLEMENTATION: "LND"
      LN_SERVER_URL: $APP_LIGHTNING_NODE_IP:$APP_LIGHTNING_NODE_GRPC_PORT/
      MACAROON_PATH: "/lnd/data/chain/bitcoin/$APP_BITCOIN_NETWORK/admin.macaroon"
      CONFIG_PATH: "/lnd/lnd.conf"
      CERTIFICATE_PATH: "/lnd/tls.cert"
      API_URL: "https://lightningnetwork.plus/api/2/"
      RAILS_ENV: "development"