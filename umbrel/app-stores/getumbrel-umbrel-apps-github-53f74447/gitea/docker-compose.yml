version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: gitea_server_1
      APP_PORT: 8085
      PROXY_AUTH_ADD: "false"

  server:
    image: gitea/gitea:1.24.2-rootless@sha256:78eccee0e28d618a2683d4ed348374395db26c363fa111d7e42694bcaf0a4d65
    user: "1000:1000"
    restart: on-failure
    ports:
      - "${APP_GITEA_SSH_PORT}:${APP_GITEA_SSH_PORT}"
    volumes:
      - ${APP_DATA_DIR}/data/gitea/data:/var/lib/gitea
      - ${APP_DATA_DIR}/data/gitea/config:/etc/gitea
    environment:
      GITEA__security__INSTALL_LOCK: "true"
      GITEA__server__DOMAIN: "${APP_DOMAIN}"
      GITEA__server__HTTP_PORT: 8085
      GITEA__server__SSH_DOMAIN: "${APP_DOMAIN}"
      GITEA__server__SSH_PORT: "${APP_GITEA_SSH_PORT}"
      GITEA__server__SSH_LISTEN_PORT: "${APP_GITEA_SSH_PORT}"
      GITEA__server__START_SSH_SERVER: "true"
      GITEA__database__DB_TYPE: "mysql"
      GITEA__database__HOST: "gitea_db_1:3306"
      GITEA__database__NAME: "gitea"
      GITEA__database__USER: "gitea"
      GITEA__database__PASSWD: "moneyprintergobrrr"

  db:
    image: mariadb:11.2.4@sha256:e9ea1dbab6783ff0281e41e3357d950a5f598b2033559b8443e03b181807910b
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/db:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: "gitea"
      MYSQL_USER: "gitea"
      MYSQL_PASSWORD: "moneyprintergobrrr"
      MYSQL_DATABASE: "gitea"
