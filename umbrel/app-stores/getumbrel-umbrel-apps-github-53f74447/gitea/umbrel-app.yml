manifestVersion: 1.1
id: gitea
category: developer
name: Gitea
version: "1.24.2"
tagline: A painless self-hosted Git service
description: >-
  Gitea is a painless self-hosted Git service. It is similar to
  GitHub, Bitbucket, and GitLab. It is a community managed lightweight code
  hosting solution written in Go. Gitea's minimal requirements allow it to run
  seamlessly on inexpensive hardware like a Raspberry Pi. Features:


    - Fully self-hosted and private
    - Issue tracker
    - Account/Organization/Repository management
    - Tor support
    - Repository Git hooks/deploy keys
    - Repository issues, pull requests and wiki
    - Add/Remove repository collaborators
    - Gravatar and custom source
    - Admin panel
developer: Gitea
website: https://gitea.io/en-us/
dependencies: []
repo: https://github.com/go-gitea/gitea
support: https://discourse.gitea.io
port: 8085
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >
  This is a quick release to resolve docker images push bug and includes several important fixes:


    - Fixed container range bugs that were affecting Docker image operations
    - Improved alignment of commit status icons on commit pages
    - Enhanced pull request creation with support for title and body parameters
    - Better package cleanup when using deletion rules
    - Fixed ghost user issues in feeds when pushing through actions
    - Prevented double markdown link brackets when pasting URLs
    - Improved form submission handling to prevent duplicate forks
    - Fixed various markdown rendering and UI display issues
    - Resolved dropdown filter problems and tag target issues


  As always, please review the full release notes for any changes that may affect your setup: https://github.com/go-gitea/gitea/releases
torOnly: false
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/commit/d62e00353917143a3a10d3b376859f51b665d150
