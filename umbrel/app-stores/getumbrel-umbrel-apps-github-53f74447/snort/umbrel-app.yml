manifestVersion: 1
id: snort
category: social
name: Snort
version: "v0.3.0"
tagline: Fast Nostr UI
description: >-
  Snort is a client for Nostr that is built using React, designed to be fast and lightweight while still offering a wide range of features. 
  Our focus is on providing users with an easy-to-use interface to interact with the Nostr network, making it accessible to all types of users. 
  With its clean and intuitive design, Snort makes it simple to navigate the Nostr network and access its features, allowing users to efficiently manage their data.
releaseNotes: >-
  # Changed


  - Drop NIP-04 support for DM's

  - Notifications chart removed

  - Profile link QR selector (npub/nprofile)

  - Re-designed relay management pages

  - Relay up time reporting (via nostr.watch NIP)

  - New note designer media attachment UI

  - Media browser via NIP-96 server list

  - NIP-89 support (App handlers for unknown events)

  - Improved profile zaps tab to show totals

  - Improved Note Rebroadcaster modal

  - WoT filter for replies

  - Expanded right bar with more widgets (trending people / articles)

  - Drop NIP-04 support for NIP-46 bunkers (NIP-44 only) 

  - NIP-55 Amber signer support


  # Fixed

  - NIP-06 login (HD-Key)

  
  https://git.v0l.io/Kieran/snort/compare/v0.2.1...v0.3.0
developer: Snort
website: https://snort.social
dependencies: []
repo: https://git.v0l.io/Kieran/snort
support: https://t.me/snort_social
port: 52027
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: Snort
submission: https://github.com/getumbrel/umbrel-apps/pull/411
