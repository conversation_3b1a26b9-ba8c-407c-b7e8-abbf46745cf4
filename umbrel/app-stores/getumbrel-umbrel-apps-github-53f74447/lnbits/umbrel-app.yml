manifestVersion: 1
id: lnbits
category: bitcoin
name: LNbits
version: "1.1.0"
tagline: Multi-user wallet management system
description: >-
  LNbits is a simple multi-user and account system for Lightning
  Network that can be used for creating separate Lightning wallets for friends
  and family members. You can also create multiple accounts for yourself to
  mitigate the risk of exposing applications to your full balance via unique API
  keys for each wallet.


  LNbits is packaged with tools to help manage funds, such as a table of transactions, line chart of spending, export to CSV, and more to come. It provides an extendable platform for expanding Lightning Network functionality via LNbits extension framework, and can also be used as a fallback wallet for the LNURL scheme.
developer: LNbits
website: https://github.com/lnbits/lnbits-legend
dependencies:
  - lightning
repo: https://github.com/lnbits/lnbits-legend
support: https://t.me/lnbits
port: 3007
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  This release brings several improvements and fixes to LNbits.


  Key highlights include:
    - Added missing default values to prevent failures
    - Resolved offset naive date errors
    - Normalized fee reserve amounts
    - Applied a mask for fiat amounts
    - Improved webhook handling on invoice payment
    - Better handling of unauthorized vs forbidden access
    - Fixed issues with handling expired/cancelled invoices
    - Added support for fetching all payments for a user
    - Improved preimage handling for incoming payments
    - Refactored invoice/payment responses in funding source
    - Reworked LND macaroon encryption for enhanced security


  ⚠️ You may need to clear your browser cache (local storage) after upgrading to see any UI changes.


  Full release notes are available at https://github.com/lnbits/lnbits/releases
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/pull/372
