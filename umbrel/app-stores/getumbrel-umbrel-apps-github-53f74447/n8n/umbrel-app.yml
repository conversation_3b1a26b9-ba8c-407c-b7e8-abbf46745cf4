manifestVersion: 1
id: n8n
category: automation
name: n8n
version: "1.100.1"
releaseNotes: >-
  This release includes new features, bug fixes and security improvements.


  Key highlights:
    - Fixed incorrect parameter names in AWS DynamoDB node
    - Resolved issues with PDF extraction and webhook edge cases
    - Prevented unauthorized workflow termination
    - Fixed keyboard shortcuts, status icons, and form validation in the editor
    - Improved error messages and expression handling
    - Ensured updated credentials are used in MongoDB Atlas Vector Store node
    - Replaced remote token encoding with local version in Token Splitter node
    - Extended user list to support projects
    - Added model selector node and OIDC paywall support
    - Improved GitHub, Google Ads, Stripe, Notion, and Telegram node behavior 


  Full release notes are found at https://github.com/n8n-io/n8n/releases
tagline: Build complex workflows, really fast
description: >-
  n8n is an extendable workflow automation tool. With a fair-code distribution model, n8n will always have visible source code, 
  be available to self-host, and allow you to add your own custom functions, logic and apps. 
  n8n's node-based approach makes it highly versatile, enabling you to connect anything to everything.


  n8n (pronounced n-eight-n) helps you to connect any app with an API with any other, and manipulate its data with little or no code.


  Customizable: highly flexible workflows and the option to build custom nodes.


  Privacy-focused: self-host n8n for privacy and security.
developer: n8n
website: https://n8n.io
dependencies: []
repo: https://github.com/n8n-io/n8n
support: https://docs.n8n.io/
port: 5678
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: Pranshu Agrawal
submission: https://github.com/getumbrel/umbrel-apps/pull/283
