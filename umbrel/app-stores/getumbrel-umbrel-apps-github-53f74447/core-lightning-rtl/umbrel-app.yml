manifestVersion: 1.1
id: core-lightning-rtl
category: bitcoin
name: Ride The Lightning (Core Lightning)
version: "0.15.4-boltz-client-2.7.1"
tagline: Manage your Core Lightning node with RTL
description: >-
  This version of RTL is specifically configured to use your Core Lightning node.

  RTL provides a full function, device agnostic, web user interface to help
  manage lightning node operation. It enables full control over your lightning
  node with rich functionality for Bitcoin base layer and Lightning Network.
  Some cool features available on RTL are:


  - Rich dashboard with two layout options

  - Send/Receive funds on-chain

  - Rich channel management with advanced features like balance score, circular re-balancing etc.

  - Payments and Invoice management with QR codes

  - Routing analysis for the payments forwarded

  - Channel backups

  - Detailed reports on routing and transaction history

  - Optional Loop interface for submarine swaps

  - Customizable UI with multiple color schemes and dark/light modes
developer: Shahana and Suheb
website: https://github.com/Ride-The-Lightning/RTL
dependencies:
  - core-lightning
repo: https://github.com/Ride-The-Lightning/RTL
support: https://github.com/Ride-The-Lightning/RTL/issues/new
port: 3100
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: true
releaseNotes: >-
  This updates Boltz Client to v2.7.1. The update doesn't include new features for RTL users, but various bug fixes and stability improvements.


  The full release notes for the latest Boltz Client releases can be found at https://github.com/BoltzExchange/boltz-client/releases.

  About Boltz: https://boltz.exchange
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/7
