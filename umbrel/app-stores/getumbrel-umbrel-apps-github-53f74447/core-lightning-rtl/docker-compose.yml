version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: core-lightning-rtl_web_1
      APP_PORT: 3000

  web:
    image: shahanafarooqui/rtl:v0.15.4@sha256:f984095949b5b6c2c0c7d983e979cd34bd32a2952d106092bad0b46e6248168e
    restart: on-failure
    environment:
      PORT: 3000
      APP_PASSWORD: $APP_PASSWORD
      LN_IMPLEMENTATION: "CLN"
      lnImplementation: "CLN"
      LN_SERVER_URL: "https://$APP_CORE_LIGHTNING_DAEMON_IP:$CORE_LIGHTNING_REST_PORT"
      RUNE_PATH: "/root/.lightning/.commando-env"
      RTL_CONFIG_PATH: "/data"
      RTL_COOKIE_PATH: "/data/.cookie"
      BLOCK_EXPLORER_URL: "${APP_CORE_RTL_BLOCK_EXPLORER_URL}"
      # Boltz
      BOLTZ_SERVER_URL: "https://core-lightning-rtl_boltz_1:9003"
      BOLTZ_MACAROON_PATH: "/boltz/.boltz/macaroons"
    volumes:
      - "${APP_DATA_DIR}/data/rtl:/data"
      - "${APP_CORE_LIGHTNING_DATA_DIR}:/root/.lightning:ro"
      - "${APP_DATA_DIR}/data/boltz:/boltz:ro"

  boltz:
    image: boltz/boltz-client:2.7.1@sha256:242069c1817541f880a7d91a7aef5415924a1c867a23116326ceb810b1095b80
    restart: "on-failure"
    stop_grace_period: "1m"
    environment:
      HOME: "/data"
    volumes:
      - "${APP_DATA_DIR}/data/boltz:/data"
      - "${APP_CORE_LIGHTNING_DATA_DIR}:/cln:ro"
    command:
      - --network=$APP_BITCOIN_NETWORK
      - --cln.datadir=/cln
      - --cln.host=$APP_CORE_LIGHTNING_DAEMON_IP
      - --cln.port=$APP_CORE_LIGHTNING_DAEMON_GRPC_PORT
      - --rpc.rest.host="0.0.0.0"
      - --rpc.rest.port="9003"
