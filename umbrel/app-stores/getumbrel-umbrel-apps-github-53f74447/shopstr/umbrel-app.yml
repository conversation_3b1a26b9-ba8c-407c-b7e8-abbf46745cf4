manifestVersion: 1
id: shopstr
category: bitcoin
name: Shopstr
version: "0.7.5"
tagline: Shop freely
description: >-
  🛒 Shopstr is an open-source decentralized marketplace built on the Nostr protocol, designed to enable global peer-to-peer trade without relying on centralized platforms or intermediaries. It allows users to buy and sell goods and services using Bitcoin, including support for Lightning payments, while maintaining full control over their data and transactions. By embracing the principles of digital sovereignty and permissionless access, Shopstr offers a censorship-resistant alternative to traditional e-commerce platforms.


  The platform makes use of various Nostr Improvement Proposals to provide features such as user identity, encrypted communication, and payment integration. Built with modern web technologies like TypeScript, Next.js, and Tailwind CSS, Shopstr delivers a fast and user-friendly experience. It can be self-hosted using Docker and is also accessible via the Tor network for enhanced privacy.


  Shopstr is ideal for anyone seeking a decentralized and open environment for commerce, where transactions are powered by Bitcoin and governed by open protocols rather than corporate entities.

  Your keys. Your shop.
releaseNotes: >-
  This update of Shopstr brings the following improvements and fixes:
    - Fixed race condition when minting new proofs at checkout
    - Revised checkout page design
    - Minor code styling fixes
developer: calvadev
website: https://calva.dev
dependencies: []
repo: https://github.com/shopstr-eng/shopstr
support: https://github.com/shopstr-eng/shopstr/issues
port: 30402
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: calvadev
submission: https://github.com/getumbrel/umbrel-apps/pull/2131
