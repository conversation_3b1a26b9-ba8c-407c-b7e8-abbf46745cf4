manifestVersion: 1
id: kimai
name: <PERSON><PERSON>
tagline: Time tracking for project-driven teams
category: files
version: "2.36.1"
port: 8734
description: >-
  🕒 <PERSON>ai is a self-hosted, open-source time-tracking application designed to capture and manage all your projects and tasks with minimal effort.


  ⏰ The main goal is to handle your freelance or corporate time-tracking needs, offering advanced features like multi-timers, detailed reporting, and secure authentication—so you can focus on what truly matters.


  👥 <PERSON><PERSON> also puts collaboration front and center, supporting multiple users, various timezones, and flexible role permissions, ensuring seamless teamwork for businesses of all sizes.


  ⚠️ To get started, log in with the provided default credentials. Make sure to change the default admin password before exposing your instance to the internet.
developer: <PERSON>
website: https://www.kimai.org/
submitter: al-lac
submission: https://github.com/getumbrel/umbrel-apps/pull/2218
repo: https://github.com/kimai/kimai
support: https://github.com/kimai/kimai/discussions
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
defaultUsername: "<EMAIL>"
defaultPassword: "changeme"
deterministicPassword: false
dependencies: []
releaseNotes: >-
  This release includes several improvements and bug fixes:
    - Fixed issue with adding new rows on weekly hours screen
    - Prevented creating empty invoices
    - Updated translations


  For full release notes, visit https://github.com/kimai/kimai/releases
path: ""
