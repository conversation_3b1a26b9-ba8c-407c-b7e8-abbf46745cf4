manifestVersion: 1
id: tallycoin-connect
category: bitcoin
name: Tallycoin Connect
version: "1.8.0-1"
tagline: Crowdfund donations directly to your Umbrel node with Tallycoin
description: Tallycoin is a crowdfunding platform with bitcoin and lightning donations. Together with the Tallycoin Connect app, you can receive donations directly to your Umbrel node. Zero fees!
developer: djbooth007
website: https://tallycoin.app/connect/
dependencies:
  - lightning
repo: https://github.com/djbooth007/tallycoin_connect
support: https://github.com/djbooth007/tallycoin_connect/issues
port: 8124 
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: true
releaseNotes: >-
  🚨 IMPORTANT: If you are using both the Tallycoin Connect and Home Assistant apps on your Umbrel,
  please update Tallycoin Connect to the latest version before updating Home Assistant. This step
  ensures a smooth operation by preventing any port conflicts between the two apps.
  

  This update changes the port that Tallycoin Connect runs on from 8123 to 8124. This is to avoid a
  port conflict with the Home Assistant app, which now runs on port 8123.


  There are no updates to the Tallycoin Connect app itself.
submitter: d11n
submission: https://github.com/getumbrel/umbrel/pull/1052
