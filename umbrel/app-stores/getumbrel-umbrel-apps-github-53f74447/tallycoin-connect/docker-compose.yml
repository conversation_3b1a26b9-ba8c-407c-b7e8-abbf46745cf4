version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: tallycoin-connect_web_1
      APP_PORT: 8123

  web:
    image: djbooth007/tallycoin_connect:v1.8.0@sha256:818868d99fcec7e0c371ff9b169b413627bc032218aa3aa0b7a99f14d0c598fd
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
      - ${APP_DATA_DIR}/data:/data
    environment:
      LND_SOCKET: "$APP_LIGHTNING_NODE_IP:$APP_LIGHTNING_NODE_GRPC_PORT"
      LND_MACAROON_PATH: "/lnd/data/chain/bitcoin/$APP_BITCOIN_NETWORK/admin.macaroon"
      LND_TLSCERT_PATH: "/lnd/tls.cert"
      CONFIG_FILE: "/data/tallycoin_api.key"
      TALLYCOIN_PASSWD_CLEARTEXT: "$APP_PASSWORD"
