manifestVersion: 1.1
id: squeaknode
category: social
name: Squeaknode
version: "0.3.3"
tagline: A peer-to-peer status feed with Lightning monetization
description: >-
  Squeaknode is a peer-to-peer microblog with posts unlocked by
  Lightning Network payments. The app allows you to create, view, buy, and sell
  squeaks. A squeak is a post that can contain up to 280 characters.


  The Squeaknode timeline is ordered by the height of the Bitcoin block hash embedded in each squeak. Each squeak must be signed with the private key of the author. Squeaks can be downloaded from any peer to any peer, but they remain locked until the downloader makes a Lightning payment to decrypt the content.
developer: <PERSON>
website: https://squeaknode.org
dependencies:
  - bitcoin
  - lightning
repo: https://github.com/squeaknode/squeaknode
support: https://github.com/squeaknode/squeaknode/discussions
port: 12994
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: umbrel
deterministicPassword: true
submitter: <PERSON>
submission: https://github.com/getumbrel/umbrel/pull/385
releaseNotes: ""