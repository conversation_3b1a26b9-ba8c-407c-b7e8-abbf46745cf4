version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: file-browser_server_1
      APP_PORT: 80

  server:
    image: filebrowser/filebrowser:v2.35.0@sha256:bd3f058cb4e88112317c1a6a3e0bc60ec3444fe056707ecd712bcbb1003b71e5
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data:/database/
      - ${APP_DATA_DIR}/data:/config/
      - ${UMBREL_ROOT}/data/storage:/data
    environment:
      - FB_PORT=80
      - FB_DATABASE=/database/filebrowser.db
      - FB_ROOT=/data
      - FB_NOAUTH=true
