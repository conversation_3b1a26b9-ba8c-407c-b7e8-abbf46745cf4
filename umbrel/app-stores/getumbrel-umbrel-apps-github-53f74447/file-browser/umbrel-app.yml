manifestVersion: 1
id: file-browser
category: files
name: File Browser
version: "2.35.0"
tagline: Browse and manage the files you download on your Umbrel
description: >-
  File Browser lets you upload, delete, preview, rename and edit your folders and files.


  Easily manage files in the Umbrel's storage folder where your downloads from apps like Transmission, Sonarr, Radarr, and Lidarr are currently held.
releaseNotes: >-
  ⚠️ Command execution feature has been disabled by default for security reasons and is now always opt-in.


  This release includes various improvements and bug fixes:
    - Long press selects item in single click mode
    - Updated documentation links
    - Improved shell command handling


  Full release notes are available at https://github.com/filebrowser/filebrowser/releases.
developer: File Browser
website: https://filebrowser.org/
dependencies: []
repo: https://github.com/filebrowser/filebrowser
support: https://github.com/filebrowser/filebrowser/issues
port: 7421
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: "/files/"
deterministicPassword: false
torOnly: false
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/169
