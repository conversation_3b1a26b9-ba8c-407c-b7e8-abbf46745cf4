manifestVersion: 1
id: snowflake
category: networking
name: Tor Snowflake Proxy
version: "v2.7.0"
tagline: Help defeat internet censorship
description: >-
  Snowflake is a system to defeat internet censorship. People who are
  censored can use Snowflake to access the internet. Their connection goes
  through Snowflake proxies, which are run by volunteers.


  If your internet access is not censored, you should consider running a Snowflake proxy to help users in censored networks. There is no need to worry about which websites people are accessing through your proxy. Their visible browsing IP address will match their Tor exit node, not yours.
developer: The Tor Project, Inc.
website: https://snowflake.torproject.org/
dependencies: []
repo: https://gitlab.torproject.org/tpo/anti-censorship/pluggable-transports/snowflake/-/tree/main
support: https://gitlab.torproject.org/tpo/anti-censorship/pluggable-transports/snowflake/-/issues
port: 3800
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: false
torOnly: false
releaseNotes: >-
  Various bugfixes and enhancements:
  
  - fix(proxy): Correctly close connection pipe when dealing with error 
  
  - Remove proxy churn measurements from broker.
  
  - fix(proxy): remove _potential_ deadlock
  
  - Maintain backward compatability with old clients
  
  - Randomly select front domain from comma-separated list
  
  - Update dependencies
  
  - chore(deps): update module github.com/xtaci/kcp-go/v5 to v5.6.3
  
  - Remove Golang 1.20 from CI Testing
  
  - Update CI targets to test android from golang 1.21
  
  - Use ShouldBeNil to check for nil values
  
  - chore(deps): update module github.com/smartystreets/goconvey to v1.8.1

  - chore(deps): update module gitlab.torproject.org/tpo/anti-censorship/pluggable-transports/goptlib to v1.5.0

  - chore(deps): update module github.com/pion/webrtc/v3 to v3.2.20

  - Update CI targets to include only Go 1.20 and 1.21

  - chore(deps): update module golang.org/x/net to v0.15.0

  - Update module golang.org/x/sys to v0.12.0

  
  Release information: https://gitlab.torproject.org/tpo/anti-censorship/pluggable-transports/snowflake/-/releases


  Compare to v2.1.0: https://gitlab.torproject.org/tpo/anti-censorship/pluggable-transports/snowflake/-/compare/v2.4.1...v2.7.0
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/pull/1284