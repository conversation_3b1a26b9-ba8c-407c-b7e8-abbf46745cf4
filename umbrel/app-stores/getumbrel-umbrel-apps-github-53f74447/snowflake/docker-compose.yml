version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: snowflake_web_1
      APP_PORT: 3800

  proxy:
    image: getumbrel/snowflake:v2.7.0@sha256:acc5713c5268d3e8c1349a69452451e4a73fe016ccf49621e36ed06f49d72948
    restart: on-failure
    stop_grace_period: 1m
    command: "-log /data/snowflake.log -verbose"
    volumes:
      - ${APP_DATA_DIR}/data:/data

  web:
    image: getumbrel/gotty:v1.0.1@sha256:100571d271cfbae25603acac216afce511eece9961218c6db427e0fa4a58716a
    restart: on-failure
    stop_grace_period: 1m
    command: --port 3800 --index "/snowflake/index.html" --title-format "Tor Snowflake Proxy" bash -c 'tail -n 10000 -f /snowflake/snowflake.log | grep "Traffic Relayed"'
    volumes:
      - ${APP_DATA_DIR}/data:/snowflake
