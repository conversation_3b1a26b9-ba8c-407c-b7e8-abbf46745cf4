manifestVersion: 1.2
id: kollider
category: bitcoin
name: Kollider
version: "1.0.7"
tagline: Lightning-fast derivative trading
description: >-
  ⚠️ Removal Notice: The Kollider project is no longer maintained and is not getting any more updates. For more information, see this update: https://x.com/kollider_trade/status/1720428682072772708


  Kollider lets you instantly trade perpetual contracts with low fees
  and up to 100x buying power.


  No need to pre-fund a trading account, each trade settles directly from your own wallet or even from your own lightning node in milliseconds.


  The Kollider Umbrel App lets you use the Lightning Network to get instant exposure to a range of products using Bitcoin, directly from your Umbrel node.


  More features coming soon!
developer: Kollider
website: https://kollider.xyz
dependencies:
  - lightning
repo: https://github.com/kolliderhq/kollider-lite-app
support: https://t.me/kolliderhq
port: 4243
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: true
releaseNotes: >
  - Update python dependency pyzmq to fix raspberry pi crash
torOnly: false
submitter: Kollider
submission: https://github.com/getumbrel/umbrel/pull/1221
disabled: true
