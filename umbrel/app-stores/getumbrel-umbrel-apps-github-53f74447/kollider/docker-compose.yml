version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: kollider_web_1
      APP_PORT: 3000

  backend:
    image: kolliderhq/kollider-lite-backend:v1.0.7@sha256:b50c548f32022cc32e3b4a10e8c686414f4b31311771ae14c4fbaea40feedb44
    init: true
    user: 1000:1000
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
      - ${APP_DATA_DIR}/data/logs:/app/logs
    environment:
      LND_IP: $APP_LIGHTNING_NODE_IP
      LND_ZMQ_SUB_ADDRESS: "tcp://kollider_ws_1:5556"

  ws:
    image: kolliderhq/kollider-ws-client:v1.0.7@sha256:8a68f15a8f15ce4957629f02346d3202e685781f2af026ab2c1c028179b42830
    init: true
    user: 1000:1000
    restart: on-failure
    stop_grace_period: 1m
    ports:
      - "4244:8080"
    environment:
      APP_PASSWORD: $APP_PASSWORD
      KOLLIDER_ZMQ_PUB_ADDRESS: "tcp://*:5556"
      KOLLIDER_ZMQ_SUB_ADDRESS: "tcp://kollider_backend_1:5557"
      KOLLIDER_ZMQ_HEDGER_ADDRESS: "tcp://kollider_backend_1:5558"
      KOLLIDER_ZMQ_HEDGER_SUB_ADDRESS: "tcp://kollider_backend_1:5559"

  web:
    image: kolliderhq/kollider-lite-app:v1.0.7@sha256:e459ae26485c52fb2967d7e9c2afeca19b51845c8f4739d02feda9e01db3037a
    init: true
    user: 1000:1000
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data/cache/images:/app/.next/cache/images
