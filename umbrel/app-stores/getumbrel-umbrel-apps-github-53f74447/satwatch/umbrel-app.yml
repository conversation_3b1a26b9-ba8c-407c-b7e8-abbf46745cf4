manifestVersion: 1.1
id: satwatch
category: bitcoin
name: sat.watch
version: "1.1.8"
tagline: Bitcoin monitoring and real-time alerts
description: >-
  sat.watch provides real-time monitoring of your Bitcoin.

  Features:
    - Monitor Bitcoin addresses, xpubs, or descriptors
    - Alerts for funds sent or received
    - Monitors the mempool and confirmed blocks
    - Total balance summary for all addresses monitored
    - Email and webhook notifications
    - PGP encrypted email
developer: <PERSON><PERSON><PERSON><PERSON>
releaseNotes: ""
dependencies:
  - bitcoin
  - electrs
website: https://sat.watch
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
repo: https://github.com/jpcummins/sat.watch
support: https://github.com/jpcummins/sat.watch/issues
port: 3883
path: "/app"
defaultUsername: "satwatch"
deterministicPassword: true
submitter: J.P. Cummins
submission: https://github.com/getumbrel/umbrel-apps/pull/2723
