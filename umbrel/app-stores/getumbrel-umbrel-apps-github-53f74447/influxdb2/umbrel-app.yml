manifestVersion: 1
id: influxdb2
name: InfluxDB 2
tagline: The leading platform for time series data
category: developer
version: "2.7.11"
port: 8886
description: >-
  Purpose built for real-time analytics at any scale.

  
  Powered by columnar analytics, optimized for cost-efficient storage, and built with open data standards.
  
  - Unmatched Performance at Scale: manage millions of time series data points per second without limits or caps.
  
  - Columnar Analytics: columnar datastore delivers faster analytic queries by orders of magnitude and reduces storage footprint.
  
  - High-Speed Ingest: ingest billions of series with fewer CPUs and less RAM at a fraction of the storage cost.
  
  - Real-Time Querying: sub-second query responses for recent and live incoming data.
  
  - Unlimited Cardinality: analyze billions of time series and data points per second without limitations or caps.
  
  - Low-Cost Object Store with Parquet: separation of compute from storage with best-in-category compression to store more data using less space.
  
  - Interoperability with Data Lakehouses: built on open data standards for direct access to data from lakehouses and warehouses via Apache Iceberg.
developer: InfluxData
website: https://www.influxdata.com/
repo: https://github.com/influxdata/influxdb
support: https://support.influxdata.com/s/
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: ""
dependencies: []
path: ""
submitter: dirstel
submission: https://github.com/getumbrel/umbrel-apps/pull/2008