manifestVersion: 1
id: libreddit
category: social
name: LibReddit
version: "0.22.9"
tagline: An alternative private front-end to Reddit
description: >-
  Libreddit is a portmanteau of "libre" (meaning freedom) and "Reddit".
  Libreddit hopes to provide an easier way to browse Reddit, without the ads, trackers, and bloat. 
  Browse r/bitcoin without being tracked.


  - 🚀 Fast: written in Rust for blazing-fast speeds and memory safety
  
  - ☁️ Light: no JavaScript, no ads, no tracking, no bloat
  
  - 🕵 Private: all requests are proxied through the server, including media
  
  - 🔒 Secure: strong Content Security Policy prevents browser requests to Reddit

developer: Spikecodes
website: https://spike.codes/
dependencies: []
repo: https://github.com/spikecodes/libreddit
support: https://matrix.to/#/#libreddit:kde.org
port: 7420
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: Jasper
submission: https://github.com/getumbrel/umbrel-apps/pull/126
releaseNotes: ""