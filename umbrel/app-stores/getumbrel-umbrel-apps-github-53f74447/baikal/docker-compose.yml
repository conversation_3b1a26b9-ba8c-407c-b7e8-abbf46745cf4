version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: baikal_web_1
      APP_PORT: 80
      PROXY_AUTH_ADD: "false"
  web:
    image: ckulka/baikal:0.10.1@sha256:2385faeb7cc165d2b080c0ed8c84cc59e84d03107f814ad897ab62684c792f7b
    # This container runs as user 33:33 and cannot be changed in compose (https://github.com/ckulka/baikal-docker/blob/master/examples/docker-compose.localvolumes.yaml)
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/config:/var/www/baikal/config
      - ${APP_DATA_DIR}/data/Specific:/var/www/baikal/Specific
