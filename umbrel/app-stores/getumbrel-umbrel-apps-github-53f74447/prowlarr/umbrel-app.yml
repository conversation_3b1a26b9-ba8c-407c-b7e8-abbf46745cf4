manifestVersion: 1
id: prowlarr
category: media
name: Prowlarr
version: "1.37.0.5076"
tagline: <PERSON>wlar<PERSON> is an indexer manager/proxy
description: >-
  Prowlarr is an indexer manager/proxy built on the popular *arr .net/reactjs base stack to integrate with your various PVR apps.
  Prowlarr supports management of both Torrent Trackers and Usenet Indexers. It integrates seamlessly with Lidarr, Mylar3, Radarr, Readarr, and Sonarr offering complete management of your indexers with no per app Indexer setup required (we do it all).


  🛠️ SETUP INSTRUCTIONS


  Prowlarr on umbrelOS will automatically:

  1.	Connect to Other Servarr Apps and Download Clients:
  
    Prowlarr will seamlessly integrate with other Servarr apps (e.g., Radarr, Sonarr, etc.) and download clients (e.g., Transmission, qBittorrent, etc.) installed from the Umbrel App Store. Simply install your preferred apps, and Prowlarr will handle the connections for you.


  2. Set up Flaresolverr:
  
    Prowlarr comes pre-configured with FlareSolverr. To use it, simply add the "flaresolverr" tag to any indexers that require it.
developer: Prowlarr
website: https://prowlarr.com/
dependencies:
  - transmission
repo: https://github.com/Prowlarr/Prowlarr
support: https://github.com/Prowlarr/Prowlarr/issues
port: 9696
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  This update includes several improvements and fixes:
    - Fixed sync for indexers with basic search to Radarr and Sonarr
    - Improved handling of jump to character functionality on Search page
    - Enhanced sync for indexers with basic search to Lidarr and Readarr
    - Updated handling of user agents without a version
    - Improved sorting of releases by time added for PTP
    - Various stability improvements and bug fixes


  Full release notes are found at https://github.com/Prowlarr/Prowlarr/releases.
torOnly: false
permissions:
  - STORAGE_DOWNLOADS
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/commit/60878f278d544b204d8e7c96240c797f43a9b319
