manifestVersion: 1
id: downtify
category: media
name: Downtify
version: "0.3.2"
tagline: Download Spotify music with album art and metadata
description: >-
  Downtify allows you to download music by copy-pasting the Spotify link for a song, album, etc. The songs are downloaded from YouTube, along with album art, lyrics, and other metadata about the songs.
  As soon as your downloads are complete you will be notified!


  **⚠️ Important note on music sourcing from the app developers:**

  Users are responsible for their actions and potential legal consequences.
  We do not support unauthorized downloading of copyrighted material and take no responsibility for user actions.
  More info on music sourcing is available at https://github.com/spotDL/spotify-downloader
developer: Henrique <PERSON>
dependencies: []
repo: https://github.com/henriquesebastiao/downtify
port: 8789
releaseNotes: >-
  This version includes improvements and bug fixes:
    - Set container web port via environment variable
    - Fix error when running ffmpeg
support: https://github.com/henriquesebastiao/downtify/issues
website: https://github.com/henriquesebastiao/downtify
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: <PERSON><PERSON>
submission: https://github.com/getumbrel/umbrel-apps/pull/2177
