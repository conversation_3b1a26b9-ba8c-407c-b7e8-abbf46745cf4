manifestVersion: 1
id: audiobookshelf
name: Audiobookshelf
tagline: Audiobook and podcast server
category: media
version: "2.25.1"
port: 13378
description: >-
  Features:


  - Fully open-source, including the android & iOS app (in beta)

  - Stream all audio formats on the fly

  - Search and add podcasts to download episodes w/ auto-download

  - Multi-user support w/ custom permissions

  - Keeps progress per user and syncs across devices

  - Auto-detects library updates, no need to re-scan

  - Upload books and podcasts w/ bulk upload drag and drop folders

  - Backup your metadata + automated daily backups

  - Progressive Web App (PWA)

  - Chromecast support on the web app and android app

  - Fetch metadata and cover art from several sources

  - Chapter editor and chapter lookup (using Audnexus API)

  - Merge your audio files into a single m4b

  - Embed metadata and cover image into your audio files (using Tone)

  - Basic ebook support and e-reader (experimental)


  Android App (beta)

  - Try it out on the Google Play Store


  iOS App (early beta)

  - Available using Test Flight: https://testflight.apple.com/join/wiic7QIW
developer: advplyr
website: https://audiobookshelf.org
repo: https://github.com/advplyr/audiobookshelf
support: https://discord.gg/pJsjuNCKRq
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  New features and improvements:
    - Added notifications for podcast episode check failures
    - Support for custom timeout settings for podcast downloads
    - Improved podcast episode matching using fuzzy search
    - Enhanced M4B encoder with better backup file handling
    - Better fallback handling for podcast episode downloads
    - Improved book library sorting with secondary sort by title
    - Updated Audible provider to return ISBN when available


  Bug fixes:
    - Fixed M4B encoder ignoring custom values
    - Fixed potential issues with podcast episode check getting stuck
    - Fixed rich text editor file attachment removal
    - Fixed alignment issues in chapters table
    - Security fix for the pathexist check
    - Various translation updates for multiple languages


  Full release notes can be found at https://github.com/advplyr/audiobookshelf/releases
submitter: Jasper
submission: https://github.com/getumbrel/umbrel-apps/pull/302
