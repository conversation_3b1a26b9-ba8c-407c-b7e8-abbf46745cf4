version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: audiobookshelf_web_1
      APP_PORT: 80
      PROXY_AUTH_ADD: "false"

  web:
    image: ghcr.io/advplyr/audiobookshelf:2.25.1@sha256:5154518d8215f8e795fde1c1561080b450af8a6bc0c0bd60c2a920084bf930d6
    user: 1000:1000
    init: true
    restart: on-failure
    stop_grace_period: 1m
    healthcheck:
      disable: true
    environment:
      - CONFIG_PATH=/home/<USER>/config
      - METADATA_PATH=/home/<USER>/metadata
      - AUDIOBOOKSHELF_UID=1000
      - AUDIOBOOKSHELF_GID=1000
    volumes:
      - ${APP_DATA_DIR}/data/config:/home/<USER>/config
      - ${APP_DATA_DIR}/data/metadata:/home/<USER>/metadata
      - ${UMBREL_ROOT}/data/storage/downloads/audiobooks:/audiobooks
      - ${UMBREL_ROOT}/data/storage/downloads/podcasts:/podcasts
