manifestVersion: 1.1
id: invidious
category: social
name: Invidious
version: "2.20250517.0"
tagline: Browse YouTube without tracking or ads
description: >-
  An open source alternative front-end to YouTube


  User Features:
   - Lightweight
   - No ads
   - No tracking
   - No JavaScript required
   - Light/Dark themes
   - Customizable homepage
   - Subscriptions independent from Google
   - Notifications for all subscribed channels
   - Audio-only mode (with background play on mobile)
   - Support for Reddit comments
   - Available in many languages, thanks to our translators

  Data Import/Export:
   - Import subscriptions from YouTube, NewPipe and Freetube
   - Import watch history from NewPipe
   - Export subscriptions to NewPipe and Freetube
   - Import/Export Invidious user data

  Technical features:
   - Embedded video support
   - Developer API
   - Does not use official YouTube APIs
   - No Contributor License Agreement (CLA)

developer: IV-Org
website: https://invidious.io/
dependencies: []
repo: https://github.com/iv-org/invidious
support: https://matrix.to/#/#invidious:matrix.org
port: 3420
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: false
releaseNotes: >-
  This release brings small improvements and bug fixes.


  For full release notes, visit https://github.com/iv-org/invidious/releases
submitter: Jasper
submission: https://github.com/getumbrel/umbrel-apps/pull/129
