version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: invidious_web_1
      APP_PORT: 3000

  web:
    image: ceramicwhite/invidious:build-2c857b5@sha256:b3164666c19e884d96d1c4df58603f5b99e3cc7b7c6554956ffd5c3d83834a05
    restart: on-failure
    stop_grace_period: 1m
    user: "1000:1000"
    # Using official images that include tini
    #init: true
    env_file:
      - ${APP_DATA_DIR}/inv.env
    healthcheck:
      disable: true
    depends_on:
      - postgres

  postgres:
    image: postgres:13.7@sha256:03652c675ae177af98ddd50f9f4b4b2cf8ad38d0e116aa68fe670fbc2cf250fc
    restart: on-failure
    user: "1000:1000"
    stop_grace_period: 1m
    init: true
    volumes:
      - ${APP_DATA_DIR}/data/db:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: invidious
      POSTGRES_USER: umbrel
      POSTGRES_PASSWORD: moneyprintergobrrr

  sig_helper:
    image: quay.io/invidious/inv-sig-helper:master-5d3c7a4@sha256:b5466c9add729e82e4e3ee5515c30b69df02d78ebb2486dbc9c63e456f29083d
    restart: on-failure
    init: true
    command: ["--tcp", "0.0.0.0:12999"]
    environment:
      - RUST_LOG=info
    cap_drop:
      - ALL
    read_only: true
    security_opt:
      - no-new-privileges:true
