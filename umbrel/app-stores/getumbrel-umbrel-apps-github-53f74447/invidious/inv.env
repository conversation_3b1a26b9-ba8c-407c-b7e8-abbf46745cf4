INVIDIOUS_CONFIG="db:\n  dbname: invidious\n  user: umbrel\n  password: moneyprintergobrrr\n  host: invidious_postgres_1\n  port: 5432\ncheck_tables: true\nexternal_port: 3420\ncaptcha_enabled: false\nsignature_server: sig_helper:12999\nvisitor_data: \"changeme\"\npo_token: \"changeme\"\nadmins: [\"umbrel\"]\ndefault_user_preferences:\n  feed_menu: [\"Trending\", \"Subscriptions\", \"Playlists\"]\n  default_home: Trending\nhmac_key: \"changeme\"\n"
