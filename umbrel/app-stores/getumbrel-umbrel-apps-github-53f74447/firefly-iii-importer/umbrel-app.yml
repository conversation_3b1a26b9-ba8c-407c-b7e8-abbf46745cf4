manifestVersion: 1.1
id: firefly-iii-importer
category: finance
name: Firefly III Importer
version: "1.7.0"
tagline: Import your transactions into Firefly III
description: >-
  Firefly III is a manager for your personal finances. The data importer is built to help you import transactions into Firefly III. It is separated from Firefly III for security and maintenance reasons.


  The data importer does not connect to your bank directly. Instead, it uses Nordigen and SaltEdge to connect to over 6000 banks worldwide. These services are free for Firefly III users, but require registration. Keep in mind these services have their own privacy and data usage policies.

  
  The data importer can import CSV files you've downloaded from your bank.

  
  You can run the data importer once, for a bulk import. You can also run it regularly to keep up with new transactions.
developer: Firefly III
website: https://www.firefly-iii.org/
dependencies:
  - firefly-iii
repo: https://github.com/firefly-iii/data-importer
support: https://docs.firefly-iii.org/references/data-importer/json/
port: 30010
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: false
torOnly: false
releaseNotes: >-
  ⚠️ Some changes in this release may unexpectedly lead to duplicate transactions. This is caused by changes in the data handling routines.


  This release includes several key improvements:
    - Added support for SimpleFIN for connecting to financial institutions
    - Better authorization header checking for users with Basic Auth
    - Enhanced data handling routines with new insights and API changes


  Full release notes can be found at https://github.com/firefly-iii/data-importer/releases
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/948
