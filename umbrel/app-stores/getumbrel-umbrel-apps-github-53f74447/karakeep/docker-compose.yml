version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: karakeep_web_1
      APP_PORT: 3000
      PROXY_AUTH_ADD: "false"

  web:
    image: ghcr.io/karakeep-app/karakeep:0.25.0@sha256:572076596ea7fdaa189694d31dc8059743773b1f908d68d7c4af3737948ff3b3
    user: 1000:1000
    restart: on-failure
    environment:
      MEILI_ADDR: http://karakeep_meilisearch_1:7700
      MEILI_MASTER_KEY: ${APP_SEED}
      BROWSER_WEB_URL: http://karakeep_chrome_1:9222
      DATA_DIR: /data
      NEXTAUTH_SECRET: ${APP_SEED}
      NEXTAUTH_URL: http://${DEVICE_DOMAIN_NAME}:8620
      KARAKEEP_VERSION: release
    env_file:
      - ${APP_DATA_DIR}/settings.env
    volumes:
      - ${APP_DATA_DIR}/data/karakeep_data:/data
    depends_on:
      - chrome
      - meilisearch

  chrome:
    image: zenika/alpine-chrome:124@sha256:5fade3fab8995c1d73e2e69632b277cb7454d8e5c7edbcabe0fd2b4f9b71c32f
    user: 1000:1000
    restart: on-failure
    command:
      - --no-sandbox
      - --disable-gpu
      - --disable-dev-shm-usage
      - --remote-debugging-address=0.0.0.0
      - --remote-debugging-port=9222
      - --hide-scrollbars

  meilisearch:
    image: getmeili/meilisearch:v1.14.0@sha256:8cd411ba5d9ec2dfce02e241305208eebacce0fd74a72bece21cadd03dc566ce
    user: 1000:1000
    restart: on-failure
    environment:
      MEILI_NO_ANALYTICS: "true"
      MEILI_MASTER_KEY: ${APP_SEED}
    volumes:
      - ${APP_DATA_DIR}/data/meilisearch_data:/meili_data
