manifestVersion: 1
id: restreamer
name: Restreamer
tagline: An easy to use open source video server
category: files
version: "2.12.0"
port: 8135
description: >-
  Restreamer is an open-source streaming server developed by datarhei, designed to simplify live video broadcasting. It provides an intuitive web-based interface that allows users to set up and manage streams without requiring advanced technical knowledge. Supporting a variety of streaming protocols, including RTMP, HLS, SRT, and WebRTC, it enables seamless video transmission across different platforms.

  
  The software can receive video from sources such as webcams, IP cameras, or other live feeds and redistribute the content to multiple destinations like YouTube, Twitch, or custom servers. This makes it a versatile tool for individuals, businesses, and organizations looking to share live content efficiently.


  Restreamer is optimized for low resource consumption, allowing it to run on a wide range of hardware, including embedded systems and energy-efficient devices. Its automatic reconnection feature ensures uninterrupted streaming, even in cases of temporary connection loss.


  With its combination of ease of use, flexibility, and reliability, Restreamer serves as a powerful solution for various live streaming scenarios, from personal broadcasts to professional media distribution.
developer: datarhei
website: https://datarhei.com/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2382
repo: https://github.com/datarhei/restreamer
support: https://github.com/datarhei/restreamer/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
releaseNotes: ""
dependencies: []
path: "/ui"
defaultUsername: ""
defaultPassword: ""
