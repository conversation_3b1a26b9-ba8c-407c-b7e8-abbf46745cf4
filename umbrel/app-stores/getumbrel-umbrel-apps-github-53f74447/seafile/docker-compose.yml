version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: seafile
      APP_PORT: 80
      PROXY_AUTH_ADD: "false"

  db:
    image: mariadb:10.11.6@sha256:20a8bd91d972c97cffded88f2ba0ab533c8988b2dc08090c57d50caf7114ed20
    container_name: seafile-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=db_dev
      - MYSQL_LOG_CONSOLE=true
    volumes:
      - ${APP_DATA_DIR}/data/db:/var/lib/mysql

  memcached:
    image: memcached:1.6.18@sha256:4ab520657d9919221f752771bb013d632c9b39cea9dfae9162244b2e39885bcd
    container_name: seafile-memcached
    entrypoint: memcached -m 256

  seafile:
    image: seafileltd/seafile-mc:12.0.14@sha256:8d0dd6b682c12cb0e1d4f1f73ba223b0176e9e3d43805408a2f631706e9c10e8
    container_name: seafile
    volumes:
      - ${APP_DATA_DIR}/data/seafile-data:/shared
    environment:
      - DB_HOST=seafile-mysql
      - DB_ROOT_PASSWD=db_dev
      - JWT_PRIVATE_KEY=${APP_SEED}
      - INIT_SEAFILE_ADMIN_EMAIL=umbrel
      - INIT_SEAFILE_ADMIN_PASSWORD=${APP_PASSWORD}
      - SEAFILE_SERVER_LETSENCRYPT=false
      - SEAFILE_SERVER_HOSTNAME=${DEVICE_DOMAIN_NAME}:8920
      - ENABLE_SEADOC=true
      - SEADOC_SERVER_URL=http://${DEVICE_DOMAIN_NAME}:8921
    depends_on:
      - db
      - memcached

  seadoc:
    image: seafileltd/sdoc-server:1.0.5@sha256:b5d3aff910be22388d329053c7dc2847a6760a6c7cfd7f0b1ef0e4f445b158b3
    volumes:
      - ${APP_DATA_DIR}/data/seadoc-data:/shared
    ports:
      - 8921:80
    environment:
      - DB_HOST=seafile-mysql
      - DB_USER=root
      - DB_PASSWORD=db_dev
      - DB_NAME=seahub_db
      - JWT_PRIVATE_KEY=${APP_SEED}
      - SEAHUB_SERVICE_URL=http://${DEVICE_DOMAIN_NAME}:8920
    depends_on:
      - db
