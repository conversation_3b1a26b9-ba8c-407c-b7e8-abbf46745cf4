version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: invoice-ninja_nginx_1
      APP_PORT: 80
      PROXY_AUTH_WHITELIST: "/api/*"

  nginx:
    image: nginx:1.21-alpine@sha256:686aac2769fd6e7bab67663fd38750c135b72d993d0bb0a942ab02ef647fc9c3
    init: true
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/config/nginx.conf:/etc/nginx/conf.d/nginx.conf:ro
      - ${APP_DATA_DIR}/data/public:/var/www/html/public:ro
      # We have to use "fake" here because otherwise /data/storage would be replaced with /home
      # See the line here: https://github.com/getumbrel/umbrel/blob/93a4b1801d1141252e73ff759a4ef52d038fff92/packages/umbreld/source/modules/apps/app.ts#L120
      - ${APP_DATA_DIR}/data/fake/../storage:/var/www/html/storage:ro
    depends_on:
      - app

  app:
    image: invoiceninja/invoiceninja-debian:5.12.6-d@sha256:52b58b570660d17e6affc7ea48094ff7fd6aa7696eaca0244f0175c24d575aff
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/public:/var/www/html/public
      # We have to use "fake" here because otherwise /data/storage would be replaced with /home
      # See the line here: https://github.com/getumbrel/umbrel/blob/93a4b1801d1141252e73ff759a4ef52d038fff92/packages/umbreld/source/modules/apps/app.ts#L120
      - ${APP_DATA_DIR}/data/fake/../storage:/var/www/html/storage
    environment:
      APP_URL: "http://${APP_DOMAIN}:8676"
      APP_KEY: "${APP_KEY:-unset}"
      APP_ENV: "production"
      APP_DEBUG: "false"
      REQUIRE_HTTPS: "false"
      DB_HOST: invoice-ninja_db_1
      DB_DATABASE: invoiceninja
      DB_USERNAME: invoiceninja
      DB_PASSWORD: moneyprintergobrrr
      IN_USER_EMAIL: "<EMAIL>"
      IN_PASSWORD: $APP_PASSWORD
      IS_DOCKER: "true"
      CACHE_DRIVER: redis
      QUEUE_CONNECTION: redis
      SESSION_DRIVER: redis
      REDIS_HOST: invoice-ninja_redis_1
      REDIS_PASSWORD: null
      REDIS_PORT: 6379
      FILESYSTEM_DISK: debian_docker
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      

  db:
    image: mariadb:11.4.5@sha256:5dfb3093333fa0ea53194ddef0a2bfa21d3b1e1353bd228b22610cd6fc0c04da
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/db:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: invoiceninja
      MYSQL_USER: invoiceninja
      MYSQL_PASSWORD: moneyprintergobrrr
      MYSQL_DATABASE: invoiceninja
      MARIADB_AUTO_UPGRADE: "true"
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
      interval: 20s
      start_period: 10s
      timeout: 10s
      retries: 5

  redis:
    image: redis:7.4.3@sha256:ee8ec723c831b815c3e2f2c6fbd1c145c68d1c04ba284f044ec1434fbca0fee8
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/redis:/data
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 5
