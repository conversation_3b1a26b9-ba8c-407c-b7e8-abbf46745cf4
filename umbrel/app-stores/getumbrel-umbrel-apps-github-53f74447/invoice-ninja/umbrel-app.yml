manifestVersion: 1.1
id: invoice-ninja
category: finance
name: Invoice Ninja
version: "5.12.6"
tagline: We'll help with the invoicing
description: >-
  Invoicing, Quotes, Expenses, Tasks.


  Invoice Ninja is an invoicing application which makes sending invoices and receiving payments simple and easy. Our latest version is a clean slate rewrite of our popular invoicing application which builds on the existing feature set and adds a wide range of features and enhancements the community has asked for.
developer: InvoiceNinja, Inc.
website: https://invoiceninja.com
dependencies: []
repo: https://github.com/invoiceninja/invoiceninja
support: https://forum.invoiceninja.com/
port: 8676
gallery:
- 1.jpg
- 2.jpg
- 3.jpg
path: ''
releaseNotes: >-
  This update includes various improvements and fixes:
    - Fixes for PDF previews
    - Improved product key selection in exports
    - Updates for chart queries
    - Various dependency updates


  Full release notes are available at https://github.com/invoiceninja/invoiceninja/releases
deterministicPassword: true
defaultUsername: <EMAIL>
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/347
