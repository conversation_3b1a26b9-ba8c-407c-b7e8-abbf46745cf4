# Umbrel Invoice-Ninja canonical nginx.conf 27-May-2025

# Invoice-Ninja recommended nginx.conf for Laravel
# https://github.com/invoiceninja/dockerfiles/blob/ed9c23d9c74a2a9f47b9dcb54946726ab816b6bc/debian/nginx/laravel.conf

# https://laravel.com/docs/master/deployment#nginx
server {
    listen 80 default_server;
    server_name _;
    root /var/www/html/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;
    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass invoice-ninja_app_1:9000;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}

# Invoice-Ninja recommended global/server directives
# https://github.com/invoiceninja/dockerfiles/blob/ed9c23d9c74a2a9f47b9dcb54946726ab816b6bc/debian/nginx/invoiceninja.conf

# https://nginx.org/en/docs/http/ngx_http_core_module.html
client_max_body_size 10M;
client_body_buffer_size 10M;
server_tokens off;

# https://nginx.org/en/docs/http/ngx_http_fastcgi_module.html
fastcgi_buffers 32 16K;

# https://nginx.org/en/docs/http/ngx_http_gzip_module.html
gzip on;
gzip_comp_level 2;
gzip_min_length 1M;
gzip_proxied any;
gzip_types *;

