manifestVersion: 1
id: uptime-kuma
category: networking
name: Uptime Kuma
version: "1.23.16"
tagline: Self-hosted uptime monitoring tool
description: >
  Uptime Kuma is a self-hosted monitoring tool like Uptime Robot.


  Features:


  - Monitoring uptime for HTTP(s) / TCP / HTTP(s) Keyword / Ping / DNS Record / Push / Steam Game Server.

  - Fancy, Reactive, Fast UI/UX.

  - Notifications via Telegram, Discord, Gotify, Slack, Pushover, Email (SMTP), and 70+ notification services.

  - 20 second intervals.

  - Multi Languages

  - Simple Status Page

  - Ping Chart

  - Certificate Info
developer: <PERSON>
website: https://uptime.kuma.pet
dependencies: []
repo: https://github.com/louislam/uptime-kuma
support: https://github.com/louislam/uptime-kuma/issues
port: 8385
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultPassword: ""
releaseNotes: >
  🚨 This release includes important security fixes for a Local File Inclusion vulnerability in the Real-Browser monitor feature.


  Full release notes can be found at https://github.com/louislam/uptime-kuma/releases
submitter: <PERSON>
submission: https://github.com/getumbrel/umbrel/pull/1148