version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: $APP_AGORA_IP
      APP_PORT: 80
      PROXY_AUTH_ADD: "false"

  filebrowser:
    image: filebrowser/filebrowser:v2.21.1@sha256:e1f43b1b8a1acb1d7cd5f934454e7a2ef571ea3bab48b0e1ed0fa97ef9df8d69
    user: 1000:1000
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/files:/srv
      - ${APP_DATA_DIR}/database/filebrowser.db:/database.db
      - ${APP_DATA_DIR}/data:/data
    environment:
      APP_PASSWORD: "$APP_PASSWORD"
    entrypoint: /data/entrypoint.sh
    networks:
      default:
        ipv4_address: $APP_AGORA_FILEBROWSER_IP

  agora:
    image: ghcr.io/agora-org/agora:sha-48d3205@sha256:35eda120a8d868c7fa3b9cbdcad7cc2245b9fe7e0c5356c8091bb0bf9a65222d
    restart: on-failure
    init: true
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/files:/files
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
    user: "1000:1000"
    environment:
      # LND environment variables
      LND_RPC_AUTHORITY: "$APP_LIGHTNING_NODE_IP:$APP_LIGHTNING_NODE_GRPC_PORT"
      TLS_CERT_PATH: "/lnd/tls.cert"
      INVOICES_MACAROON_PATH: "/lnd/data/chain/bitcoin/$APP_BITCOIN_NETWORK/invoice.macaroon"

      # App specific environment variables
      FILES_DIR: "/files"
      AGORA_PORT: 8080
    networks:
      default:
        ipv4_address: $APP_AGORA_SERVER_IP

  nginx:
    image: nginx:1.19-alpine@sha256:07ab71a2c8e4ecb19a5a5abcfb3a4f175946c001c8af288b1aa766d67b0d05d2
    init: true
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/nginx/nginx.conf.template:/etc/nginx/templates/nginx.conf.template
      - ${APP_DATA_DIR}/data/www:/usr/share/nginx/html
    environment:
      NGINX_ENVSUBST_OUTPUT_DIR: /etc/nginx/
      APP_AGORA_SERVER_IP: $APP_AGORA_SERVER_IP
      APP_AGORA_FILEBROWSER_IP: $APP_AGORA_FILEBROWSER_IP
      APP_HIDDEN_SERVICE: $APP_HIDDEN_SERVICE
    depends_on:
      - agora
      - filebrowser
    networks:
      default:
        ipv4_address: $APP_AGORA_IP
