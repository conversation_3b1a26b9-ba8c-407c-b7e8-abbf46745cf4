events { }

http {


server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://${APP_AGORA_SERVER_IP}:8080;
    }

    location /admin {
        root /usr/share/nginx/html;
        ssi on;
        set $ssihiddenservice "${APP_HIDDEN_SERVICE}";
        index index.html;
    }

    location /admin/files {
        client_max_body_size 0;
        proxy_pass http://${APP_AGORA_FILEBROWSER_IP}:8080;
    }

  }

}
