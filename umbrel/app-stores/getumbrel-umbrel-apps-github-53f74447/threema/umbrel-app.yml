manifestVersion: 1
id: threema
name: Threema Web
tagline: An intuitive and secure web client for Threema users
category: files
version: "2.6.2"
port: 8855
description: >-
  📱 Threema Web is a secure and convenient way to use the Threema messaging app directly from a web browser while maintaining full end-to-end encryption. By scanning a QR code with the Threema app, users establish a direct connection between their smartphone and computer, ensuring that no messages or metadata are stored on external servers. The communication is handled via WebRTC, which enables a private and encrypted peer-to-peer connection.


  With Threema Web, users can send and receive messages, share images and files, and manage conversations with the comfort of a full-sized keyboard and larger screen. Since all data remains on the user's devices and is never uploaded to the cloud, Threema Web offers a high level of privacy and security. This makes it an ideal solution for those who want a seamless messaging experience across devices without sacrificing confidentiality.
developer: Threema GmbH
website: https://threema.ch/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2226
repo: https://github.com/threema-ch/threema-web
support: https://github.com/threema-ch/threema-web/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  This update includes multiple improvements and bug fixes.


  You can find the full list of changes at https://github.com/threema-ch/threema-web/blob/master/CHANGELOG.md
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
