manifestVersion: 1
id: chatpad-ai
category: ai
name: Chatpad AI
# using first 6 characters of commit hash as version number (docker image tag uses full commit hash)
version: "bb5f4a"
tagline: Premium quality UI for ChatGPT
description: >-
  Chatpad AI is an alternative user interface for OpenAI's chat models. Simply add your OpenAI API key and you're ready to go!


  No tracking. No cookies. All your data is stored locally within your browser, and you can export and import conversations to safeguard against data loss.
developer: Andrei <PERSON>
website: https://github.com/deiucanta/chatpad
dependencies: []
repo: https://github.com/deiucanta/chatpad
support: https://github.com/deiucanta/chatpad/issues
port: 10102
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  This release adds GPT-4o support.
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/604
