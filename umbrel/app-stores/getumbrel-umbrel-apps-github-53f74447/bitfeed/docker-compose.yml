version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: bitfeed_web_1
      APP_PORT: 80

  web:
    image: ghcr.io/bitfeed-project/bitfeed-client:v2.3.4@sha256:5d57477e69a789d547b1c6c441e0ff49e2f5ed46bf4b5ab9ca9f5403f385e926
    restart: on-failure
    stop_grace_period: 1m
    depends_on:
      - "api"
    environment:
      TARGET: "umbrel"
      BACKEND_HOST: bitfeed_api_1
      BACKEND_PORT: 8315

  api:
    image: ghcr.io/bitfeed-project/bitfeed-server:v2.3.4@sha256:e38a2e07389cf6d0c519e856291e535fdd10a1e50143d528c82bfe0baf06894f
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    environment:
      PORT: 8315
      BITCOIN_HOST: "$APP_BITCOIN_NODE_IP"
      BITCOIN_ZMQ_RAWTX_PORT: "$APP_BITCOIN_ZMQ_RAWTX_PORT"
      BITCOIN_ZMQ_RAWBLOCK_PORT: "$APP_BITCOIN_ZMQ_RAWBLOCK_PORT"
      BITCOIN_ZMQ_SEQUENCE_PORT: "$APP_BITCOIN_ZMQ_SEQUENCE_PORT"
      BITCOIN_RPC_PORT: "$APP_BITCOIN_RPC_PORT"
      BITCOIN_RPC_USER: "$APP_BITCOIN_RPC_USER"
      BITCOIN_RPC_PASS: "$APP_BITCOIN_RPC_PASS"
      TARGET: "umbrel"
      RPC_POOLS: "1"
      RPC_POOL_SIZE: "16"
      LOG_LEVEL: "info"
    volumes:
      - ${APP_DATA_DIR}/data:/app/data
