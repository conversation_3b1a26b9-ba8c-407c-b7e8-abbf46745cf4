manifestVersion: 1.1
id: blockstream-blind-oracle
category: bitcoin
name: Blockstream Blind Oracle
version: "0.1.1"
tagline: Encrypt your wallet on Blockstream Jade
description: >-
        Run a personal blind oracle to encrypt the wallet material on your 
        Blockstream Jade.


        Your blind oracle holds the decryption key to your <PERSON> that is needed 
        to unlock your device. This allows your <PERSON> to remain protected from 
        physical key extraction while also enforcing a maximum of 3 PIN attempts. 
        Your blind oracle does not learn any sensitive information and does not 
        know anything about the wallet on Jade.


        Blind oracle needs a Tor or a Tailscale connection, if you decide to use 
        Tailscale please enable Tailscale's MagicDNS.
developer: Blockstream
website: https://blockstream.com
dependencies: []
repo: https://github.com/Blockstream/blind_pin_server
support: https://t.me/blockstream_jade
port: 3344
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ''
deterministicPassword: false
submitter: Blockstream
submission: https://github.com/getumbrel/umbrel-apps/pull/950
releaseNotes: ""