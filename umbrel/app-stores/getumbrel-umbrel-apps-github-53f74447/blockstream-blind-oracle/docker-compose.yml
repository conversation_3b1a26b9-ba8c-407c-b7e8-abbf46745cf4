version: "3.7"

services:
  app_proxy:
   environment:
     APP_HOST: blockstream-blind-oracle_web_1
     APP_PORT: $APP_PINSERVER_WEB_PORT
     PROXY_AUTH_ADD: "false"

  node:
    image: tulipan81/blind_pin_server:v0.0.7@sha256:db8ce3f8de8da6cb083d60051a8ec720d5aa311e06148b259c42c4dfb7263c63
    depends_on:
      - web
      - tor
    restart: on-failure
    stop_grace_period: 1m
    ports:
      - $APP_PINSERVER_PORT:8096
    volumes:
      - ${APP_DATA_DIR}/data/server_public_key.pub:/server_public_key.pub
      - ${APP_DATA_DIR}/data/server_private_key.key:/server_private_key.key
      - ${APP_DATA_DIR}/data/pins:/pins

  web: 
    image: tulipan81/pinserver_web:v0.0.36@sha256:a5214dbc14538478744a6a0c6bd40f03ab8ac2990abc9038139c18901064205b
    depends_on:
      - tor
    restart: on-failure
    ports:
      - $APP_PINSERVER_WEB_PORT:$APP_PINSERVER_WEB_PORT
    volumes:
      - ${APP_DATA_DIR}/data/server_public_key.pub:/app/server_public_key.pub
      - ${APP_DATA_DIR}/data/server_private_key.key:/app/server_private_key.key
      - ${APP_DATA_DIR}/data/pins:/app/pins
    environment:
      PINSERVER_URL_A: ${APP_PINSERVER_HIDDEN_SERVICE}
      PINSERVER_PORT_A: ${APP_PINSERVER_PORT}
      PINSERVER_URL_B: ${APP_TAILSCALE_URL}
      PINSERVER_PORT_B: ${APP_PINSERVER_PORT}
      PINSERVER_CERT: ""

  tor:
    image: getumbrel/tor:*******@sha256:2ace83f22501f58857fa9b403009f595137fa2e7986c4fda79d82a8119072b6a
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/torrc:/etc/tor/torrc:ro
      - ${TOR_DATA_DIR}:/data
    environment:
      HOME: "/tmp"
