manifestVersion: 1.1
id: thelounge
category: social
name: The Lounge
version: "4.4.3"
tagline: Modern web IRC client designed for self-hosting
description: >-
  The Lounge is a modern, open-source, web-based IRC (Internet Relay Chat) client that offers persistent connectivity, allowing users to remain connected to IRC servers even while offline, and provides a responsive interface compatible with desktops, smartphones, and tablets.


  It brings IRC into the 21st century with features like push notifications, link previews, and file uploads, and supports multiple user accounts, enabling server sharing among friends or team members.


  The Lounge can be installed on a continuously running server for optimal performance, granting users access through their browsers or mobile devices.

  
  It offers both private mode, combining the functionalities of a bouncer and a client for a modern chat experience, and public mode, allowing open chat access without registration.
developer: The Lounge Team
website: https://thelounge.chat
dependencies: []
repo: https://github.com/thelounge/thelounge
support: https://github.com/thelounge/thelounge/issues
port: 9003
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: "umbrel"
deterministicPassword: true
submitter: oren-z0
submission: https://github.com/getumbrel/umbrel-apps/pull/2412
