manifestVersion: 1.1
id: sonarr
category: media
name: Sonarr
version: "4.0.15.2941"
tagline: Smart PVR for newsgroup and bittorrent users
description: >-
  Sonarr is a PVR for Usenet and BitTorrent users. It can monitor multiple RSS feeds for new episodes of your favorite shows and will grab, sort and rename them. It can also be configured to automatically upgrade the quality of files already downloaded when a better quality format becomes available.


  🛠️ SETUP INSTRUCTIONS

  Sonarr on umbrelOS will automatically connect to download clients installed from the Umbrel App Store. Choose from Transmission, qBittorerent, and SABnzbd. Simply install your preferred client(s).


  All you need to do from there is add an indexer so Sonarr can search for shows. You can add indexers directly within Sonarr, or install Prowlarr from the Umbrel App Store for easier management of indexers across multiple apps.
  Add your indexers to Prowlarr and they will be automatically available in Sonarr.
developer: Sonarr
website: https://sonarr.tv/
dependencies:
  - transmission
repo: https://github.com/Sonarr/Sonarr
support: https://github.com/Sonarr/Sonarr/issues
port: 8989
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
releaseNotes: >-
  This release includes several improvements and bug fixes:
    - Fixed qBittorrent ratio limit check for float values
    - Improved tooltips for detailed error messages
    - Added release source information in history grab popup
    - Enhanced parsing of releases with year and season number in brackets
    - Added support for ZFS reflink
    - Improved handling of dangerous file extensions
    - Fixed dropdown menu flickering issues
    - Better language detection for releases with MULTI and other languages
    - Enhanced series editor functionality
    - Various UI improvements and stability fixes


  Full release notes for Sonarr can be found at https://github.com/Sonarr/Sonarr/releases
defaultUsername: ""
defaultPassword: ""
torOnly: false
permissions:
  - STORAGE_DOWNLOADS
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/commit/60878f278d544b204d8e7c96240c797f43a9b319
