version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: sonarr_server_1
      APP_PORT: 8989
      PROXY_AUTH_WHITELIST: "/api/*"

  server:
    image: linuxserver/sonarr:4.0.15@sha256:1156329d544b38bd1483add75c9b72c559f20e1ca043fd2d6376c2589d38951f
    environment:
      - PUID=1000
      - PGID=1000
    volumes:
      - ${APP_DATA_DIR}/data/config:/config
      - ${UMBREL_ROOT}/data/storage/downloads:/downloads
    restart: on-failure

  mac:
    image: getumbrel/media-app-configurator:v1.3.0@sha256:67e75dd9f5a14402b7816119a8e20189bc2465484cea077909d164687e59742b
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/config:/config
      - ${UMBREL_ROOT}/data/storage/downloads:/downloads
    environment:
      APP_ID: "sonarr"
      APP_URL: "http://sonarr_server_1:8989"
      TRANSMISSION_HOST: "transmission_server_1"
      TRANSMISSION_PORT: 9091
      ROOT_FOLDER: "/downloads/shows"
      # optional qBittorrent download client
      QBITTORRENT_INSTALLED: ${APP_SONARR_QBITTORRENT_INSTALLED:-"false"}
      QBITTORRENT_HOST: "qbittorrent_server_1"
      QBITTORRENT_PORT: 8080
      # optional SABnzbd download client
      SABNZBD_INSTALLED: ${APP_SONARR_SABNZBD_INSTALLED:-"false"}
      SABNZBD_HOST: "sabnzbd_web_1"
      SABNZBD_PORT: 8080
      SABNZBD_API_KEY: ${APP_SONARR_SABNZBD_API_KEY:-""}
