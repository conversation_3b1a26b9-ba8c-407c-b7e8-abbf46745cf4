manifestVersion: 1
id: mainsail
category: files
name: Mainsail
version: "2.14.0"
tagline: Control and monitor your printer from everywhere, from any device
description: >-
  🖨️ A modern and responsive user interface for <PERSON><PERSON>per. Control and monitor your printer from everywhere, from any device.


  **Easy to use.** The focus lies on both anticipating what users may need to do and ensuring that the user interface contains elements that are easily accessible, understandable, and user-friendly to make those actions easier.


  **Always one step ahead.** We work closely with developers of other projects so that functions can already be implemented early on.


  **100% open source.** Anyone can join, anyone can contribute.
releaseNotes: >-
  New features and improvements:
    - Improved Console with multi-line input and history
    - Added SHT3X support in Temperature Panel
    - New menu to open Settings & turn off heaters in Temperatures
    - Implemented python package entries in Update Manager
    - Added load cell gram scales in misc panel
    - Added support for hall filament width sensor
    - Added search functionality to macro settings interface


  Bug fixes:
    - Fixed various issues in Editor, History, and Timelapse panels
    - Improved Z-tilt functionality
    - Fixed G2/G3 visualisation in Gcode-Viewer
    - Various other minor fixes and improvements


  Full release notes are found at https://github.com/mainsail-crew/mainsail/releases
developer: Mainsail Crew
website: https://docs.mainsail.xyz/
dependencies: []
repo: https://github.com/mainsail-crew/mainsail
support: https://docs.mainsail.xyz/faq/getting-help
port: 8070
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: Julienpeps
submission: https://github.com/getumbrel/umbrel-apps/pull/1982
