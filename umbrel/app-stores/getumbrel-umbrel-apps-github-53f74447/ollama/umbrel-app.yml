manifestVersion: 1
id: ollama
name: Ollama
tagline: Self-host open source AI models like DeepSeek-R1, Llama, and more
category: ai
version: "0.9.3"
port: 11434
description: >-
  Ollama allows you to download and run advanced AI models directly on your own hardware. Self-hosting AI models ensures full control over your data and protects your privacy.


  ⚠️ Before running a model, make sure your device has enough free RAM to support it. Attempting to run a model that exceeds your available memory could cause your device to crash or become unresponsive. Always check the model requirements before downloading or starting it.


  **Getting Started:**

  The easiest way to get started with Ollama is to install the Open WebUI app from the Umbrel App Store. Open WebUI will automatically connect to your Ollama setup, allowing you to manage model downloads and chat with your AI models effortlessly.


  **Advanced Setup:**

  If you want to connect Ollama to other apps or devices, here's how:
    - Apps running on UmbrelOS: Use ollama_ollama_1 as the host and 11434 as the port when configuring other apps to connect to Ollama. For example, the API Base URL would be: http://ollama_ollama_1:11434.
    - Custom Integrations: Connect Ollama to third-party apps or your own code using your UmbrelOS local domain (e.g., http://umbrel.local:11434) or your device's IP address, which you can find in the UmbrelOS Settings page (e.g., http://************:11434).
developer: Ollama
website: https://ollama.com/
submitter: al-lac
submission: https://github.com/getumbrel/umbrel-apps/pull/2114
repo: https://github.com/ollama/ollama
support: https://github.com/ollama/ollama/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
defaultUsername: ""
defaultPassword: ""
dependencies: []
releaseNotes: >-
  This release includes important bug fixes and stability improvements.


  Key improvements in this release:
    - Ollama will now limit context length to what the model was trained against to avoid strange overflow behavior


  Full release notes are available at https://github.com/ollama/ollama/releases
path: ""
