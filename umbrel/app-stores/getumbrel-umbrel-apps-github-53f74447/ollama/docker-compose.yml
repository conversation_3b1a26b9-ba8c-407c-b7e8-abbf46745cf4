version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: ollama_ollama_1
      APP_PORT: 11434
      PROXY_AUTH_ADD: "false"

  ollama:
    image: ollama/ollama:0.9.3@sha256:45008241d61056449dd4f20cebf64bfa5a2168b0c078ecf34aa2779760502c2f
    environment:
      OLLAMA_ORIGINS: "*"
      OLLAMA_CONTEXT_LENGTH: 8192
    volumes:
      - ${APP_DATA_DIR}/data:/root/.ollama
    restart: on-failure
