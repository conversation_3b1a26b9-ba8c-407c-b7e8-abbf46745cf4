manifestVersion: 1
id: rotki
category: finance
name: rotki
version: "1.39.1"
tagline: Portfolio tracking, analytics, accounting and tax reporting
description: >-
  rotki is an open source portfolio tracking, analytics, accounting and tax reporting tool that protects your privacy. 
  

  The mission of rotki is to bring transparency into the crypto and financial sectors through the use of open source. 
  

  Most importantly unlike virtually every other competing service which consists of closed source SaaS onto which you are forced to hand over all your financial data, with rotki your data is stored encrypted locally in your computer. 
  

  It enables you to take ownership of your financial data!
  
  
  ⚠️ IMPORTANT ⚠️ Rotki is meant to be accessed locally. Hosting it on a publicly accessible machine should be avoided at all cost, unless you know what you're doing!
developer: Rotki
website: https://rotki.com
dependencies: []
repo: https://github.com/rotki/rotki
support: https://github.com/rotki/rotki/issues
port: 8084
releaseNotes: >-
  This release includes a lot of improvements and bug fixes to enhance your rotki experience.


  Key highlights include:
    - Onchain transactions
    - Pectra staking
    - Pendle support
    - Swap events compact view
    - Hyperliquid is now supported
    - Pectra EIP 7702 delegations
    - Dark mode


  Full release notes are found at https://github.com/rotki/rotki/releases
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: false
submitter: Xosten
submission: https://github.com/getumbrel/umbrel-apps/pull/255
