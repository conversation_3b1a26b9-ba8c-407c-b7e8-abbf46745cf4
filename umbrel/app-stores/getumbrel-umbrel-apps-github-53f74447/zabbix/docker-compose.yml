version: "3.7"

services:

  app_proxy:
    environment:
      APP_HOST: zabbix_zabbix-frontend_1
      APP_PORT: 8080

  zabbix-frontend:
    image: zabbix/zabbix-web-nginx-pgsql:7.2.9-alpine@sha256:ed4a00a2be70bdc05b40174a7e98135f690a9c00c2ef0cfd9d7db04c3604974f
    hostname: zabbix-frontend
    restart: on-failure
    environment:
      DB_SERVER_HOST: zabbix-database
      POSTGRES_USER: zabbix
      POSTGRES_PASSWORD: zabbix
      ZBX_SERVER_HOST: zabbix-server
      PHP_TZ: UTC
    depends_on:
      - zabbix-server
      - zabbix-database

  zabbix-server:
    image: zabbix/zabbix-server-pgsql:7.2.9-alpine@sha256:f1af5a501f112a33eb0634231630e9005bde88b3f62154585aa670823b79011e
    hostname: zabbix-server
    restart: on-failure
    init: true
    environment:
      DB_SERVER_HOST: zabbix-database
      POSTGRES_USER: zabbix
      POSTGRES_PASSWORD: zabbix
    ports:
      - "10050:10050"
    depends_on:
      - zabbix-database

  zabbix-agent:
    image: zabbix/zabbix-agent2:7.2.9-alpine@sha256:a26e3099e33cc59288855d64fd905f9a81b15c1ac5661d6d5072bdd96387f987
    hostname: zabbix-agent
    restart: on-failure
    init: true
    environment:
      ZBX_HOSTNAME: zabbix-agent
      ZBX_SERVER_HOST: zabbix-server

  zabbix-database:
    image: postgres:13.18-alpine3.20@sha256:77815c1f18ae761b0e503503a494475a03149d7ce544934ccecdbafdb3956c94
    hostname: zabbix-database
    restart: on-failure
    environment:
      POSTGRES_PASSWORD: zabbix
      POSTGRES_USER: zabbix
      POSTGRES_DB: zabbix
    volumes:
      - ${APP_DATA_DIR}/data/postgres:/var/lib/postgresql/data
