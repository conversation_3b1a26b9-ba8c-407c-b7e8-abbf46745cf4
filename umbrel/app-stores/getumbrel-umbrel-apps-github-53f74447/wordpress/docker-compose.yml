version: "3.7"
services:
  app_proxy:
    environment:
      APP_HOST: wordpress_app_1
      APP_PORT: 80
      PROXY_AUTH_ADD: "false"

  app:
    image: wordpress:6.8.1@sha256:626ea197cb3f17747b24de6ba8f52a5a841efbb68a11167a115a6401ccb6cd3c
    user: "1000:1000"
    environment:
      - WORDPRESS_DB_HOST=wordpress_db_1
      - WORDPRESS_DB_NAME=wordpress
      - WORDPRESS_DB_USER=umbrel
      - WORDPRESS_DB_PASSWORD=umbrelwordpress
    volumes:
      - ${APP_DATA_DIR}/data/wordpress:/var/www/html
    restart: on-failure
    depends_on:
      - db

  db:
    image: mariadb:11.5.2@sha256:2d50fe0f77dac919396091e527e5e148a9de690e58f32875f113bef6506a17f5
    user: "1000:1000"
    environment:
      - MYSQL_ROOT_PASSWORD=umbrelwordpressroot
      - MYSQL_DATABASE=wordpress
      - MYSQL_USER=umbrel
      - MYSQL_PASSWORD=umbrelwordpress
    volumes:
      - ${APP_DATA_DIR}/data/db:/var/lib/mysql
    restart: on-failure
