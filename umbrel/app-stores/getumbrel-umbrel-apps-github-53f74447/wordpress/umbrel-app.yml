manifestVersion: 1
id: wordpress
name: WordPress
tagline: The open source publishing platform of choice for millions of websites worldwide
category: social
version: "6.8.1"
port: 8567
description: >-
  📚 **Empower Your Digital Voice** - The open-source publishing platform trusted by millions. From personal blogs to enterprise portals, WordPress gives everyone the tools to share their stories.


  ✨ **Unleash Your Creativity** - Choose from thousands of themes and plugins, craft your unique design, and make your site truly your own.


  🤝 **Join a Global Community** - Connect with creators, developers, and enthusiasts worldwide. Together, shape the future of the open web.


  ⚙️ **Customize & Extend** - Seamless integrations, custom code, and unlimited flexibility—unlock endless possibilities for growth and innovation.


  **Note:** WordPress can be used locally within your network. However, if you want others outside your local network to access your instance, you'll need to make it accessible on the public internet.  
  If you have a domain name, the easiest way to make it accessible is to use the Cloudflare Tunnel or Nginx Proxy Manager apps in the Umbrel App Store and point your domain name to "http://wordpress_app_1" (port 80).


  For it to work you need to set the **WordPress Address (URL)** and **Site Address (URL)** to your domain name in the WordPress settings.
  Also make sure to set the **correct headers** in the Nginx Proxy Manager or Cloudflare Tunnel app. More information can be found here: https://developer.wordpress.org/advanced-administration/security/https/#using-a-reverse-proxy
developer: WordPress
website: https://wordpress.org/
submitter: al-lac
submission: https://github.com/getumbrel/umbrel-apps/pull/1947
repo: https://github.com/WordPress/WordPress
support: https://wordpress.org/support/forums/
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
defaultUsername: ""
defaultPassword: ""
dependencies: []
releaseNotes: >-
  ⚠️ To fully update your Wordpress instance, you need to go to the admin interface and update from there as well.


  This minor release includes fixes for 15 bugs throughout Core and the Block Editor addressing issues affecting multiple areas of WordPress including the block editor, multisite, and REST API.


  Full release notes can be found at https://wordpress.org/news/category/releases/
path: "/wp-admin"
