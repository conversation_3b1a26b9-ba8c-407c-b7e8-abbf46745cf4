version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: duplicati_duplicati_1
      APP_PORT: 8200
      PROXY_AUTH_ADD: "false"
  
  duplicati:
    image: duplicati/duplicati:*******@sha256:3dbb83aba8e714ff2d3c44fef564e55c73f4d67ff689e28a957e7b8f60925ca1
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data:/data
      - ${UMBREL_ROOT}/data/storage:/umbrel
    environment:
      DUPLICATI__WEBSERVICE_PORT: "8200"
      DUPLICATI__WEBSERVICE_INTERFACE: "any"
      DUPLICATI__WEBSERVICE_PASSWORD: "${APP_PASSWORD}"
      DUPLICATI__WEBSERVICE_ALLOWED_HOSTNAMES: "*"
