manifestVersion: 1
id: duplicati
name: Duplicati
tagline: Store securely encrypted backups in the cloud
category: files
version: "v2.1.0.5_stable_2025-03-04"
port: 38476
description: >-
  Pick your own backend and store encrypted backups of your umbrel files anywhere!


  🧩 Integrations

  Works seamlessly with standard protocols like FTP, SSH, and WebDAV, as well as a wide range of popular services like Amazon S3, OneDrive, Google Drive, Rackspace Cloud Files, HubiC, Backblaze (B2), Amazon Cloud Drive (AmzCD), Swift / OpenStack, WebDAV, SSH (SFTP), FTP, and more!


  ⏲️ Automatic backups

  You choose how often all your selected files will be safely backed up.


  🛡️ Safe & Secure

  We take your data security seriously. By using the powerful AES-256 encryption standard, we ensure that your privacy stays protected from any unauthorized access. We also support GPG encryption, giving an extra layer of strong defense to your backups.


  🖥️ Intuitive interface

  Simple interface and powerful controls at your fingertips.
developer: Duplicati
website: https://duplicati.com/
submitter: Sharknoon
submission: https://github.com/getumbrel/umbrel-apps/pull/1179
repo: https://github.com/duplicati/duplicati
support: https://forum.duplicati.com/
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  This update fixes critical issues reported in previous versions. It is highly recommended to upgrade as soon as possible if you are running version ******* or earlier.


  Key highlights:
    - Fixed issue where failed uploads were ignored in certain cases
    - Increased default timeout for large backup sets
    - Improved handling of source URL paths
    - Enhanced support for various zip file formats
    - Updated icons for dark/light mode on MacOS
    - Improved signing process for MacOS and Windows


  Full release notes can be found at https://github.com/duplicati/duplicati/releases
dependencies: []
permissions:
  - STORAGE_DOWNLOADS
path: ""
deterministicPassword: true
