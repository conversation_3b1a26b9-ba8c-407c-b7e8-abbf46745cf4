version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: zoraxy_server_1
      APP_PORT: 8000
  server:
    image: zoraxydocker/zoraxy:v3.2.4@sha256:244065b1e5d09234e81939f36a5a7d03d39436a853b3bcf00362854cfa7cc2e7
    restart: on-failure
    ports:
      - 41080:80
      - 41443:443
    volumes:
      - ${APP_DATA_DIR}/data/config/:/opt/zoraxy/config
      - /etc/localtime:/etc/localtime:ro
    environment:
      FASTGEOIP: "false"
      ZEROTIER: "false"
      DOCKER: "false"
      NOAUTH: "true"
