manifestVersion: 1
id: zoraxy
name: <PERSON>orax<PERSON>
tagline: An efficient reverse proxy server with automated SSL management
category: networking
version: "3.2.4"
port: 8400
description: >-
  Expose your apps to the internet easily and securely. 
  
  
  ⚠️ Be cautious when exposing apps to the public internet. Ensure they have proper security, such as login protection, and avoid exposing sensitive apps without adequate safeguards. 
    
  
  🔧 Zoraxy Proxy Manager uses port 41080 for HTTP (unsecured) traffic and port 41443 for HTTPS (secured) traffic. 
  To make your apps accessible from the public internet, you will need to set up port forwarding on your router. 
  Forward external port 80 (HTTP) to internal port 41080 and external port 443 (HTTPS) to internal port 41443. 
  
  
  Zoraxy is a powerful, user-friendly reverse proxy server and forwarding tool, designed to provide a straightforward, flexible, and secure method for managing web traffic within home labs and developer environments. Written in Go, Zoraxy caters to both novice and experienced users by simplifying the process of routing HTTP requests to backend servers, enabling efficient and reliable service management without relying on traditional, more complex servers like NGINX or Apache.


  Key Features:
  

  🔀 Reverse Proxy & WebSocket Support: <PERSON><PERSON>y handles HTTP/2 connections and automatically supports WebSocket proxying, making it ideal for real-time applications that need a stable, low-latency connection, such as chat services or dashboards. It also enables reverse proxying across virtual directories and supports alias hostnames and custom headers for enhanced flexibility.


  🤖 TLS/SSL with ACME Automation: Zoraxy simplifies the setup of HTTPS using ACME, which enables automatic renewal of SSL certificates through providers like Let's Encrypt. This feature supports both SNI (Server Name Indication) and SAN (Subject Alternative Name) certificates, helping users serve multiple domains securely with minimal manual intervention.


  📍 Geo-IP Blacklisting and Whitelisting: Security is enhanced by offering geo-IP and IP-based blacklisting and whitelisting, allowing users to restrict access based on location or specific IP addresses. This feature is especially useful for protecting applications and services from specific regions or untrusted IPs.


  🌐 Global Area Network Controller & ZeroTier Integration: For distributed networks, Zoraxy provides a built-in controller interface for managing ZeroTier networks, facilitating secure and scalable connections across multiple nodes. This feature is particularly valuable for homelab enthusiasts and developers creating private network setups with limited external access​ Zoraxy DEV Community.


  🛠️ Built-in Utilities: Zoraxy goes beyond traditional reverse proxy functionality, including utilities like CIDR IP conversion, an mDNS scanner for local network discovery, and tools for debugging proxy connections. A Web-SSH terminal allows remote management of connected devices within the network, and its Wake-on-LAN support further extends Zoraxy's usability for network administrators.


  📊 Stream Proxying & Monitoring: Beyond HTTP/HTTPS, Zoraxy supports TCP and UDP stream proxying, making it a versatile choice for handling various data streams. Integrated uptime monitoring and non-personalized traffic analytics provide insights into network activity without cookies, enhancing privacy and enabling users to track service performance directly within Zoraxy.


  🖥️ Simple UI & Integration: The Zoraxy interface is designed to be intuitive, with in-depth setup instructions for less experienced users. Advanced configurations can disable authentication to integrate with existing infrastructures that use permission management. Additionally, a single-admin management mode makes it easy to operate within private networks, similar to managing a home router.


  Zoraxy is particularly popular in the homelab and developer communities for its extensive feature set, reliability, and open-source nature. It can be run on low-power devices, making it suitable for a variety of hosting environments, from personal setups to larger, distributed network architectures. Its flexibility, combined with a robust feature set, makes it a compelling choice for anyone looking to streamline their network management setup or host web services securely and efficiently.

developer: Toby Chui
website: https://zoraxy.aroz.org/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/1751
repo: https://github.com/tobychui/zoraxy
support: https://github.com/tobychui/zoraxy/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
releaseNotes: >-
  This release includes several new features and improvements.


  Key highlights:
    - New user interface
    - Improved OIDC/OAuth2 redirection
    - Updated UDP forwarding default address
    - Reorganized SSO settings UI
    - Various bug fixes and performance improvements


  Full release notes can be found at https://github.com/tobychui/zoraxy/releases
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
