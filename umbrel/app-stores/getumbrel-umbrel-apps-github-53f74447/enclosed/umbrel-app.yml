manifestVersion: 1
id: enclosed
category: files
name: Enclosed
version: "1.15.0"
tagline: Send private and secure notes
description: >-
  Enclosed is a minimalistic web application designed for sending private and secure notes. Whether you need to share sensitive information or just want a simple way to send encrypted messages, Enclosed provides a user-friendly interface with strong security features to ensure your data remains confidential.


  The name Enclosed is inspired by the idea of keeping your notes safe and secure, like a sealed envelope.

  
  🛠️ SET-UP INSTRUCTIONS 🛠️

  Enclosed relies on native browser crypto APIs to encrypt and decrypt notes with security and great performance. These APIs are only available in secure contexts (HTTPS).
  If you are running the app in a non-secure context (HTTP), you will see a warning in the app.


  You can set up https using the Cloudflare Tunnel app available in the app store.
  Detailed instructions for configuring the tunnel can be found here: https://github.com/Radiokot/umbrel-cloudflared/wiki/How-to-set-up-Cloudflare-Tunnel-on-your-Umbrel
releaseNotes: >-
  This update contains various improvements and bug fixes.


  Notable Changes:
    - Added a configuration to add prefix to the view note URL
    - Improved UI by conditionally displaying the "New Note" button based on authentication status
    - Updated Simplified Chinese translations
    - Prevented note password auto-completion from auth password


  Full release notes are found at https://github.com/CorentinTh/enclosed/releases
developer: Corentin Thomasset
website: https://enclosed.cc/
dependencies: []
repo: https://github.com/CorentinTh/enclosed
support: https://github.com/CorentinTh/enclosed/discussions
port: 8788
gallery:
- 1.jpg
- 2.jpg
- 3.jpg
path: ""
submitter: Zoobdude
submission: https://github.com/getumbrel/umbrel-apps/pull/1563
