manifestVersion: 1
id: lobe-chat
name: Lobe Chat
tagline: An open-source, modern-design AI chat framework
category: ai
version: "1.96.13"
port: 7455
description: >-
  💬 An open-source, modern-design ChatGPT/LLMs UI/Framework.


  🗣️ Supports speech-synthesis, multi-modal, and extensible (function call) plugin system.


  🤖 One-click deployment of your private OpenAI ChatGPT/Claude/Gemini/Groq/Ollama chat application.


  🔑 To get started, add your OpenAI API key in the settings or configure one of the many other providers.
developer: LobeHub
website: https://lobehub.com/
submitter: al-lac
submission: https://github.com/getumbrel/umbrel-apps/pull/1901
repo: https://github.com/lobehub/lobe-chat
support: https://github.com/lobehub/lobe-chat/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
defaultUsername: ""
defaultPassword: ""
dependencies: []
releaseNotes: >-
  Some of the key highlights in this release include:
    - Updated internationalization and language support
    - Various bug fixes and performance improvements


  Full release notes are available at https://github.com/lobehub/lobe-chat/releases
path: ""
