version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: ln-visualizer_web_1
      APP_PORT: 80

  web:
    image: maxkotlan/ln-visualizer-web:v0.0.28@sha256:d207e2e474453090d8c0ebec568c94536f79c9ae0d691818068861bbc2abb25d
    init: true
    restart: on-failure
    stop_grace_period: 1m
    environment:
      LN_VISUALIZER_API_URL: "http://ln-visualizer_api_1:5647"

  api:
    image: maxkotlan/ln-visualizer-api:v0.0.28@sha256:7724b02e0b5f91a8a88559458434731d9767d0893982b8a013d39cb24f8b9d94
    init: true
    restart: on-failure
    stop_grace_period: 1m
    user: 1000:1000
    volumes:
      - "${APP_LIGHTNING_NODE_DATA_DIR}/tls.cert:/lnd/tls.cert:ro"
      - "${APP_LIGHTNING_NODE_DATA_DIR}/data/chain/bitcoin/${APP_BITCOIN_NETWORK}/readonly.macaroon:/lnd/data/chain/bitcoin/${APP_BITCOIN_NETWORK}/readonly.macaroon:ro"
    environment:
      LND_CERT_FILE: "/lnd/tls.cert"
      LND_MACAROON_FILE: "/lnd/data/chain/bitcoin/${APP_BITCOIN_NETWORK}/readonly.macaroon"
      LND_SOCKET: "${APP_LIGHTNING_NODE_IP}:${APP_LIGHTNING_NODE_GRPC_PORT}"
