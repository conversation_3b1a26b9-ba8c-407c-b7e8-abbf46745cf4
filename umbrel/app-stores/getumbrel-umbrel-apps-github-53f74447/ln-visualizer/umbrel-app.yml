manifestVersion: 1
id: ln-visualizer
category: bitcoin
name: LnVisualizer
version: "0.0.28"
releaseNotes: >-
  What's Changed

  - Feature/table

  - initial work to search for channels in rendered graph

  - Enable table search

  - Feature/sphere nodes v2 and orthographic cam

  - disable pod status server

  Full release notes here: https://github.com/MaxKotlan/LN-Visualizer/releases/tag/v0.0.28
tagline: View the Lightning Network from your node's perspective
description:
  Your Lightning node is continuously receiving, storing, and transmitting graph information.
  LnVisualizer takes this data and transforms it into an interactive, 3D graph.
  Search for nodes, filter the graph, and write custom queries to help understand your position in the network.

developer: <PERSON>
website: https://lnvisualizer.com
dependencies:
  - lightning
repo: https://github.com/MaxKotlan/LN-Visualizer
support: mailto:<EMAIL>
port: 5646
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: false
torOnly: false
submitter: <PERSON>
submission: https://github.com/getumbrel/umbrel-apps/pull/19
