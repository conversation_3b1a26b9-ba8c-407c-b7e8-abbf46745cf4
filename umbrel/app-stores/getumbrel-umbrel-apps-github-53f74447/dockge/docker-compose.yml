version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: dockge_dockge_1
      APP_PORT: 5001

  docker:
    image: docker:24.0.5-dind@sha256:3c6e4dca7a63c9a32a4e00da40461ce067f255987ccc9721cf18ffa087bcd1ef
    privileged: true
    network_mode: host
    stop_grace_period: 1m
    restart: on-failure
    environment:
      DOCKER_ENSURE_BRIDGE: "dind0:*********/16"
    entrypoint: /entrypoint.sh
    command: >
      dockerd
        --bridge dind0
        --data-root /data/data
        --exec-root /data/exec
        --host unix:///data/docker.sock
        --pidfile /data/docker.pid
    volumes:
      - ${APP_DATA_DIR}/entrypoint.sh:/entrypoint.sh
      - ${APP_DATA_DIR}/data/docker:/data

  dockge:
    image: louislam/dockge:1.5.0@sha256:335c6368b880ecc203236ed89e6e5232e0d6578e8ef5920e4a502390451502bf
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/docker:/var/run
      - ${APP_DATA_DIR}/data/dockge-data:/app/data
      - ${APP_DATA_DIR}/data/dockge-stacks:/opt/stacks
    environment:
      DOCKGE_STACKS_DIR: /opt/stacks
      DOCKGE_ENABLE_CONSOLE: "true"
