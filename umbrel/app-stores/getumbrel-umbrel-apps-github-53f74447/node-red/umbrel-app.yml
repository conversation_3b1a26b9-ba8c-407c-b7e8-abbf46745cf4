manifestVersion: 1
id: node-red
category: automation
name: "Node-RED (Bitcoin)"
version: "4.0.9"
tagline: Wire together the Internet of Things
description: >-
  Node-RED is a visual programming tool for wiring together hardware
  devices, APIs and online services in new and interesting ways.


  It provides a browser-based editor that makes it easy to wire together flows using the wide range of nodes in the palette that can be deployed to its runtime in a single-click. A built-in library allows you to save useful functions, templates or flows for re-use.


  The flows created in Node-RED are stored using JSON which can be easily imported and exported for sharing with others. An online flow library allows you to share your best flows with the world.

  
  Note: If you would like your 'HTTP In' nodes to be accessible without authentication, then prepend your url with '/public/'. E.g. /public/do-something


  Warning: Node-RED (Bitcoin) on Umbrel is able to connect directly to Bitcoin Core, LND and Electrum, due to this you should be careful when installing third-party Node-RED nodes.
developer: OpenJS Foundation
website: https://nodered.org
dependencies:
  - bitcoin
  - electrs
  - lightning
repo: https://github.com/node-red/node-red
support: https://nodered.org/about/community/slack/
port: 1880
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
releaseNotes: >-
  This release includes improvements and bug fixes:
    - Improved handling of context variables and environment access
    - Enhanced sidebar and library interface improvements
    - Better handling of node groups and flow management
    - Various other improvements and bug fixes


  Full release notes are found at https://github.com/node-red/node-red/releases
defaultUsername: umbrel
defaultPassword: moneyprintergobrrr
torOnly: false
submitter: Oren Zomer
submission: https://github.com/getumbrel/umbrel/pull/958
