[{"id": "f50232d5dd80c5b3", "type": "tab", "label": "Flow 1", "disabled": false, "info": ""}, {"id": "54f43e91fb562ca8", "type": "debug", "z": "f50232d5dd80c5b3", "name": "msg.payload (see sidebar debug tab)", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 710, "y": 420, "wires": []}, {"id": "fcef7a58987b1ae5", "type": "http request", "z": "f50232d5dd80c5b3", "name": "", "method": "POST", "ret": "obj", "paytoqs": "body", "url": "", "tls": "", "persist": false, "proxy": "", "authType": "", "x": 450, "y": 420, "wires": [["54f43e91fb562ca8"]]}, {"id": "2679e2b0b4a4e5f2", "type": "inject", "z": "f50232d5dd80c5b3", "name": "getblockchaininfo request", "props": [{"p": "payload"}, {"p": "url", "v": "\"http://\" & $env(\"BITCOIN_IP\") & \":\" & $env(\"BITCOIN_RPC_PORT\")", "vt": "jsonata"}, {"p": "headers", "v": "{\t  \"Authorization\": \"Basic \" & $base64encode($env(\"BITCOIN_RPC_USER\") & \":\" & $env(\"BITCOIN_RPC_PASS\"))\t}", "vt": "jsonata"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "{\"jsonrpc\":\"1.0\",\"id\":\"test\",\"method\":\"getblockchaininfo\",\"params\":[]}", "payloadType": "json", "x": 230, "y": 420, "wires": [["fcef7a58987b1ae5"]]}, {"id": "44b30c110ae098b0", "type": "function", "z": "f50232d5dd80c5b3", "name": "build admin-credentials.json", "func": "msg.payload = JSON.stringify({\n    username: \"umbrel\",\n    password: bcryptjs.hashSync(msg.payload, 8),\n    permissions: \"*\",\n});\nreturn msg;\n", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "bcryptjs", "module": "bcryptjs"}], "x": 400, "y": 180, "wires": [["4da8c916b9891e5b"]]}, {"id": "7d4f1cdb5d0f6875", "type": "inject", "z": "f50232d5dd80c5b3", "name": "Password", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "moneyprintergobrrr", "payloadType": "str", "x": 180, "y": 180, "wires": [["44b30c110ae098b0"]], "info": "hello"}, {"id": "34b08a2abb3c4418", "type": "comment", "z": "f50232d5dd80c5b3", "name": "Welcome to Node RED", "info": "# This is an experimental app.\n## Use at your own risk.\n", "x": 160, "y": 40, "wires": []}, {"id": "4da8c916b9891e5b", "type": "file", "z": "f50232d5dd80c5b3", "name": "write /data/admin-credentials.json", "filename": "/data/admin-credentials.json", "appendNewline": true, "createDir": false, "overwriteFile": "true", "encoding": "none", "x": 520, "y": 240, "wires": [["325641493682ca2a"]]}, {"id": "9a14303ee07169ab", "type": "comment", "z": "f50232d5dd80c5b3", "name": "Here is an example flow to change the password - double-click the Password node to choose a new password.", "info": "", "x": 480, "y": 100, "wires": []}, {"id": "11440c02fac4477f", "type": "comment", "z": "f50232d5dd80c5b3", "name": "Don't forget to hit the deploy button to save changes.", "info": "", "x": 300, "y": 140, "wires": []}, {"id": "325641493682ca2a", "type": "debug", "z": "f50232d5dd80c5b3", "name": "status", "active": true, "tosidebar": false, "console": false, "tostatus": true, "complete": "payload", "targetType": "msg", "statusVal": "\"Updated at \" & $now()", "statusType": "jsonata", "x": 750, "y": 240, "wires": []}, {"id": "f546e2778c4622fd", "type": "comment", "z": "f50232d5dd80c5b3", "name": "It is recommended to delete the password flow after you've updated the password.", "info": "", "x": 390, "y": 300, "wires": []}, {"id": "8eb5f7f9889a3c98", "type": "comment", "z": "f50232d5dd80c5b3", "name": "Here is an example that calls getblockchaininfo:", "info": "", "x": 280, "y": 380, "wires": []}]