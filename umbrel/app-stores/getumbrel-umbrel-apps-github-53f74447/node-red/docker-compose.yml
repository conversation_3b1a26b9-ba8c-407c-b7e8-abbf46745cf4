version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: node-red_web_1
      APP_PORT: 1880
      # Used to allow (HTTP In) flows to be publicly exposed
      PROXY_AUTH_WHITELIST: "/public/*"
  
  web:
    image: nodered/node-red:4.0.9@sha256:7a3e18f29578feb271bb8daab4379e4ebd355b87ea64b699ce74e6ff49d907aa
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data:/data
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
    environment:
      PORT: 1880
      BITCOIN_NETWORK: $APP_BITCOIN_NETWORK
      BITCOIN_IP: $APP_BITCOIN_NODE_IP
      BITCOIN_RPC_PORT: $APP_BITCOIN_RPC_PORT
      BITCOIN_RPC_USER: $APP_BITCOIN_RPC_USER
      BITCOIN_RPC_PASS: $APP_BITCOIN_RPC_PASS
      LND_IP: $APP_LIGHTNING_NODE_IP
      LND_GRPC_PORT: $APP_LIGHTNING_NODE_GRPC_PORT
      ELECTRUM_IP: $APP_ELECTRS_NODE_IP
      ELECTRUM_PORT: $APP_ELECTRS_NODE_PORT
