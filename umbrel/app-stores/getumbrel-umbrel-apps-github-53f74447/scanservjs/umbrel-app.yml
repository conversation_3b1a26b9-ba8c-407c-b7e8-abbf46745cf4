manifestVersion: 1
id: scanservjs
category: files
name: scanservjs
version: "3.0.3"
tagline: Web-based interface for your SANE-compatible scanners
description: >-
  scanservjs is a web UI frontend for your scanner. It allows you to share one or more scanners (using SANE) on a network without the need for drivers or complicated installation.
developer: sbs20
website: https://sbs20.github.io/scanservjs/
dependencies: []
repo: https://github.com/sbs20/scanservjs
support: https://github.com/sbs20/scanservjs/issues
port: 2903
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: highghlow
submission: https://github.com/getumbrel/umbrel-apps/pull/1094
