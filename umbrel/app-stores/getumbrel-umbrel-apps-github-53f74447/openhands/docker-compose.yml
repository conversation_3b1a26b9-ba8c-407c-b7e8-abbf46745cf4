version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: openhands_web_1
      APP_PORT: 3000

  docker:
    image: docker:28.0.4-dind@sha256:ddb0033088b4fab74881ade341a582e3c6c8021b82377703ba1a6106bd3ded44
    privileged: true
    network_mode: host
    stop_grace_period: 1m
    restart: on-failure
    environment:
      DOCKER_ENSURE_BRIDGE: "dind0:*********/16"
    entrypoint: /entrypoint.sh
    command: >
      dockerd
        --bridge dind0
        --data-root /data/data
        --exec-root /data/exec
        --host unix:///data/docker.sock
        --pidfile /data/docker.pid
    volumes:
      - ${APP_DATA_DIR}/entrypoint.sh:/entrypoint.sh
      - ${APP_DATA_DIR}/data/docker:/data

  web:
    image: ghcr.io/all-hands-ai/openhands:0.47.0@sha256:28ab47be706191e343cae499dbe09425e1d754155e5b9b88754801dea3e3b141
    stdin_open: true
    tty: true
    pull_policy: always
    healthcheck:
      test: timeout 10s bash -c ':> /dev/tcp/127.0.0.1/3000' || exit 1
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 90s
    environment:
      - SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.47.0-nikolaik@sha256:3956b564a9dae21cccb6b0d981559df812496bb02898af9c27a82d8b822f7fe4
      - WORKSPACE_MOUNT_PATH=/opt/workspace_base
      - LOG_ALL_EVENTS=true
    volumes:
      - ${APP_DATA_DIR}/data/docker:/var/run:rw
      - ${APP_DATA_DIR}/data/openhands:/.openhands-state:rw
      - ${APP_DATA_DIR}/data/workspace:/opt/workspace_base:rw
    extra_hosts:
      - host.docker.internal:host-gateway
      - ${APP_DOMAIN}:host-gateway
    restart: on-failure
