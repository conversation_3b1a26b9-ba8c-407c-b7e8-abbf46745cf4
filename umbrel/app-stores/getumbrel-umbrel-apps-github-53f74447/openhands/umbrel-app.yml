manifestVersion: 1
id: openhands
name: OpenHands
tagline: A local AI agent for writing and improving code
category: ai
version: "0.47.0"
port: 3993
description: >-
  👐 <PERSON>Hands is a local-first AI development assistant that helps developers automate repetitive programming tasks and concentrate on more meaningful work. It runs entirely on your own Umbrel server and respects your privacy by keeping all operations offline and under your control. OpenHands understands, edits, and improves code independently by using intelligent software agents that analyze your repositories, execute commands, work with files, and follow instructions written in natural language.


  By having full access to your local codebase and development tools, OpenHands becomes a powerful companion throughout your software projects. Whether you want to refactor existing code, fix bugs, or develop new features, the assistant can suggest and apply changes while always keeping you in the loop. Its modern web interface offers a smooth experience with an integrated code editor and terminal, all running directly on your own hardware without any external dependencies.


  Bringing OpenHands to Umbrel gives developers a fully autonomous AI assistant that does not rely on cloud services. It is a great solution for privacy-focused individuals, hobby developers, and professionals who want to explore the future of software development with AI.


  ⚠️ OpenHands needs to download an image when starting the first session, this takes some minutes. Please be patient.
developer: All Hands AI, Inc
website: https://www.all-hands.dev/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2527
repo: https://github.com/All-Hands-AI/OpenHands
support: https://github.com/All-Hands-AI/OpenHands/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
releaseNotes: >-
  This release brings several new features and improvements:


    - Added new feedback options for better user interaction
    - Added GitLab alternative directory support for microagents
    - Changed terminal output truncation to trim the middle of long outputs instead of the end
    - Improved Bitbucket integration with better pagination and sorting to fetch all repositories
    - Added custom model names during CLI model selection
    - Introduced optional vi-style keybindings for navigating menus in the command-line interface
    - Added support for customizable safety settings for Mistral AI and Gemini models
    - Added ability to override the system prompt
    - Improved timeout behavior for long-running commands
    - Enhanced feedback capabilities, even when the agent encounters errors


  Full release notes can be found at https://github.com/All-Hands-AI/OpenHands/releases
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
