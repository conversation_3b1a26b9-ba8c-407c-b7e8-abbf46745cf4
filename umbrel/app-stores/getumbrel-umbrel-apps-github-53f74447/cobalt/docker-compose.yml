version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: cobalt_web_1
      APP_PORT: 9001
      PROXY_AUTH_ADD: "false"
  web:
    image: dennysubke/cobalt:10.9.4@sha256:1d3700f63d913065e584f5db99e1997152789d39deafc9e27ff1e72d4972e4bf
    read_only: true
    restart: on-failure
    user: "1000:1000"
    ports:
      - 9013:9000
    environment:
      API_URL: "http://${DEVICE_DOMAIN_NAME}:9013/"
      API_AUTH_REQUIRED: 0
      DURATION_LIMIT: 86400

