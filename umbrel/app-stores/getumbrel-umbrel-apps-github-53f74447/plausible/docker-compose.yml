services:
  app_proxy:
    environment:
      APP_HOST: plausible_plausible_1
      APP_PORT: 8000

  plausible_db:
    # Plausible v2.1.1 was tested against PostgreSQL versions 15 and 16
    # https://github.com/plausible/analytics/blob/v2.1.1/.github/workflows/elixir.yml#L21-L32
    image: postgres:16-alpine@sha256:492898505cb45f9835acc327e98711eaa9298ed804e0bb36f29e08394229550d
    restart: on-failure
    user: "1000:1000"
    volumes:
      - ${APP_DATA_DIR}/db-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_PASSWORD=postgres

  plausible_events_db:
    image: clickhouse/clickhouse-server:24.12-alpine@sha256:cd450891db46cc6ffe313ca2b0fb7dbfb897a6873ca74a724cbe050a2cf62621
    restart: on-failure
    user: "1000:1000"
    volumes:
      - ${APP_DATA_DIR}/event-data:/var/lib/clickhouse
      - ${APP_DATA_DIR}/event-logs:/var/log/clickhouse-server
      - ${APP_DATA_DIR}/clickhouse/clickhouse-config.xml:/etc/clickhouse-server/config.d/logging.xml:ro
      - ${APP_DATA_DIR}/clickhouse/clickhouse-user-config.xml:/etc/clickhouse-server/users.d/logging.xml:ro
    ulimits:
      nofile:
        soft: 262144
        hard: 262144
    environment:
      - CLICKHOUSE_SKIP_USER_SETUP=1

  plausible:
    image: ghcr.io/plausible/community-edition:v3.0.1@sha256:14c1afde21d6555a41fad0e8db9445d835f5722238f7d58bbc4dc8c76d7aa499
    restart: on-failure
    user: "1000:1000"
    command: sh -c "sleep 10 && /entrypoint.sh db createdb && /entrypoint.sh db migrate && /entrypoint.sh run"
    depends_on:
      - plausible_db
      - plausible_events_db
    environment:
      - BASE_URL=http://${DEVICE_DOMAIN_NAME}:9092
      - SECRET_KEY_BASE=${APP_SEED}
      - TOTP_VAULT_KEY=${APP_PLAUSIBLE_VAULT_KEY}
