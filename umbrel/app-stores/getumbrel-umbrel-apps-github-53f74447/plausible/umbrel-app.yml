manifestVersion: 1
id: plausible
category: developer
name: Plausible Analytics
version: "3.0.1"
tagline: Easy to use and privacy-friendly Google Analytics alternative
description: >-
  Plausible is intuitive, lightweight and open source web analytics.
  No cookies and fully compliant with GDPR, CCPA and PECR.


  ⚙️ You may need to expose Plausible to the web in order to use the app in its intended way with websites.
  The easiest way to do this is to use the 'Cloudflare Tunnel' app from the Umbrel app store, and expose Plausible to the internet using your own domain.
releaseNotes: >-
  This release introduces several major features and improvements:


    - Create teams, manage users and permissions
    - Scroll depth, engagement time and reworked time on page metrics
    - Segments feature: group filters into persistent, named presets
    - New acquisition channels report
    - Support for case-insensitive searches in Stats API V2 filters
    - Improved report performance for sites with many unique pathnames
    - Changed top bar filter menu and applied filter wrapping
    - Revenue now shows with currency symbol in main graph


  Full release notes are available at https://github.com/plausible/analytics/releases
developer: Plausible Community Edition
website: https://plausible.io/
repo: https://github.com/plausible/analytics
support: https://github.com/plausible/community-edition/issues
port: 9092
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
dependencies: []
submitter: ~dibref-labter
submission: https://github.com/getumbrel/umbrel-apps/pull/1371
