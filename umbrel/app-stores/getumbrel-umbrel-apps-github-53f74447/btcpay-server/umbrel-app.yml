manifestVersion: 1
id: btcpay-server
category: bitcoin
name: BTCPay Server
version: "2.1.5"
tagline: Accept Bitcoin payments with 0 fees & no 3rd party
description: >-
  BTCPay Server is a payment processor that allows you to receive
  payments in Bitcoin (and altcoins) directly, with no fees, transaction cost or
  a middleman. It is a non-custodial invoicing system which eliminates the
  involvement of a third-party.


  Payments with BTCPay Server go directly to your wallet, which increases the privacy and security. Your private keys are never uploaded to your Umbrel. There is no address re-use, since each invoice generates a new address deriving from your xpubkey.


  You can not only attach an unlimited number of stores and use the Lightning Network but also become a payment processor for others. Thanks to the apps built on top of it, you can use BTCPay to receive donations, start a crowdfunding campaign or have an in-store Point of Sale.


  Please note: Due to your BTCPay instance running on your local network connecting remote applications, such as Shopify or WordPress for example, will fail to connect, unless you configure the dynamic DNS feature (see: https://docs.btcpayserver.org/Deployment/DynamicDNS).
developer: BTCPay Server Foundation
website: https://btcpayserver.org
dependencies:
  - bitcoin
  - lightning
repo: https://github.com/btcpayserver/btcpayserver
support: https://chat.btcpayserver.org
port: 3003
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  This update of BTCPay Server includes new features, improvements and bug fixes.


  New features:
    - Enhanced manual coin selection with advanced filters and improved user experience
    - Added "Clear All" filter to invoices
    - Point of Sale improvements including tax rates applied to items with breakdown in checkout and receipts
    - New webhooks for expired partial payments and payments after expiration
    - Added Coinmate rate provider for CZK currency
    - Ability to replace-by-fee sweeping transactions
    - Admin can change default store templates
    - Store owners can configure fallback rate source
    - Phoenixd Lightning support


  Bug fixes:
    - Fixed connection failure with phoenixd on mainnet
    - Fixed Point of Sale custom amount and payment issues
    - Fixed tax application and receipt display in Print View
    - Fixed keypad crashes and free item handling
    - Fixed rate lookup failures and RBF transaction labeling


  Full release notes can be found at https://github.com/btcpayserver/btcpayserver/releases
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/pull/353
