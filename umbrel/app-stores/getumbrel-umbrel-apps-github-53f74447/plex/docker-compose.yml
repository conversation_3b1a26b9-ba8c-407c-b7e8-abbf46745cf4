version: "3.7"

services:
  server:
    image: linuxserver/plex:1.41.8@sha256:7794c25926494a63da421fabf2898dd38c4d6190f9146450fc0e29da8025ed2c
    restart: on-failure
    hostname: "${DEVICE_HOSTNAME}"
    network_mode: host
    environment:
      - PUID=1000
      - PGID=1000
      - VERSION=docker
    # Currently running in host network mode to allow for UPnP discovery (Plex DLNA server) and so that <PERSON><PERSON> does not complain about port 1900/udp being in use (can be shared, for example with Home Assistant).
    # ports:
    #   - 32400:32400
    #   # Plex Companion
    #   - 3005:3005/tcp
    #   # Roku via Plex Companion
    #   - 8324:8324/tcp
    #   # Plex DLNA Server
    #   - 32469:32469/tcp 
    #   - 1900:1900/udp
    #   # Network discovery
    #   - 32410:32410/udp
    #   - 32412:32412/udp
    #   - 32413:32413/udp
    #   - 32414:32414/udp
    volumes:
      - ${APP_DATA_DIR}/data/config:/config
      - ${APP_DATA_DIR}/data/transcode:/transcode
      - ${UMBREL_ROOT}/data/storage/downloads:/downloads
