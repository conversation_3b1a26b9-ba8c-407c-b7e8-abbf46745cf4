manifestVersion: 1
id: plex
category: media
name: Plex
version: "1.41.8.9834"
tagline: Stream Movies & TV Shows
description: >-
  Stream movies and TV shows, plus 300+ channels of live TV, instantly, without a subscription. Watch live TV and movies anywhere, from any device, with Plex.


  Plex uses a server to house your media library and player apps to playback the media. Add, access, and share all the entertainment that matters to you, on almost any device.


  ⚠️ If you encounter a "not authorized error" when attempting to access your Plex app, please follow the steps in this guide: https://community.umbrel.com/t/fixing-plexs-not-authorized-error/10523
developer: Plex
website: https://www.plex.tv/
dependencies: []
repo: ""
support: https://www.plex.tv/
port: 32400
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: /web
defaultUsername: ""
defaultPassword: ""
torOnly: false
permissions:
  - STORAGE_DOWNLOADS
releaseNotes: >-
  This update to Plex includes several enhancements and bug fixes to improve your streaming experience.


  Fixes:
    - Fixed issue where transcode request bandwidth wasn't tracked
    - Resolved potential failures in cleanup tasks during server startup
    - Optimized database queries to reduce movie detail page load times


  Full release notes are available at https://www.videohelp.com/software/Plex
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/commit/60878f278d544b204d8e7c96240c797f43a9b319
