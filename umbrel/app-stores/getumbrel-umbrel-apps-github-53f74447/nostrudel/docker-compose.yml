version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: nostrudel_web_1
      APP_PORT: 80
      PROXY_AUTH_ADD: "false"

  cors:
    image: ghcr.io/hzrd149/docker-cors-anywhere:0.4.5@sha256:641f5ea3265acbc339a03a72b79cd942c4bedab9b1c8b3fc13e5f3f087f649e6
    environment:
      CORSANYWHERE_REQUIRE_HEADERS: "host"
  web:
    image: ghcr.io/hzrd149/nostrudel:0.42.0@sha256:2c399be9ba816519d2b3d68b19c6b1da81c5d3390491e6a6ec6578a6fa0110dd
    restart: on-failure
    stop_grace_period: 1m
    init: true
    depends_on:
      - cors
    environment:
      CORS_PROXY: cors:8080
