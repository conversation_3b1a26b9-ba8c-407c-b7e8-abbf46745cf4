manifestVersion: 1
id: nostrudel
category: social
name: noStrudel
version: "0.42.0"
tagline: A sandbox for exploring nostr
description: >-
  noStrudel is a nostr web client that is focused on exploring the nostr network.

  The focus is on power users or any other users who want to learn the inner workings of nostr.
releaseNotes: |
  ### Minor Changes

  - Move core logic out into applesauce packages
  - Add support for olas media posts and NIP-22 comments
  - Add tools menu under thread post
  - Add favorite DVM feeds
  - Add templates to event publisher
  - Add validation messages to profile edit view
  - Add unknown notifications toggle
  - Add option to hide noStrudel logo in nav bar
  - Support nostr links in markdown
  - Update timelines to use applesauce
  - Unclutter notifications view
  - Use applesauce for NIP-28 channels
  - Add open and share button to stream view
  - Add "Proactively authenticate to relays" option to privacy settings, defaults to off
  - Add option for debug API
  - Remove support for legacy password account
  - Add insert gif button
  - Add top zappers support page
  - Support searching local relay
  - Add support for cashu v4 tokens
  - Add "q" tags for quoted notes
  - Remove legacy npub1 bunker URI format
  - Add edit button to event debug modal
  - Cleanup zap parsing
  - Remove old community trending view

  ### Patch Changes

  - Fix delete events not getting published to outbox
  - Fix page changing from RTL when viewing some profiles
  - Refresh relay info on relay page
  - Improve list background loading
  - Fix bug with removing "about" in profile editor
  - Fix automatically disconnecting from authenticated relays

developer: hzrd149
website: https://github.com/hzrd149/nostrudel
dependencies: []
repo: https://github.com/hzrd149/nostrudel
support: https://github.com/hzrd149/nostrudel/issues
port: 3149
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: hzrd149
submission: https://github.com/getumbrel/umbrel-apps/pull/901
