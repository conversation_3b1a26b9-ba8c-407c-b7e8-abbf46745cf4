manifestVersion: 1
id: mealie
category: files
name: <PERSON><PERSON><PERSON>
version: "2.8.0"
tagline: A Place for All Your Recipes
description: >-
  <PERSON><PERSON><PERSON> is a self hosted recipe manager and meal planner. Easily add recipes by providing
  the url and <PERSON><PERSON><PERSON> will automatically import the relevant data or add a family recipe with the UI editor. <PERSON><PERSON><PERSON> also
  provides an API for interactions from 3rd party applications.


  Key Features:
  
  - Recipe imports: Create recipes, by importing from a URL or entering data manually

  - Meal Planner: Use the Meal Planner to plan your what you'll cook for the next week

  - Shopping List: Put the necessary ingredients on your Shopping List, organised into sections of your local supermarket

  - Cookbooks: Group recipes into Cookbooks based on your own criteria

  - Localisation: Translations for 35+ languages
developer: Hayden
website: https://docs.mealie.io/
dependencies: []
repo: https://github.com/hay-kot/mealie
support: https://github.com/hay-kot/mealie/issues
port: 9925
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: false
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/608
releaseNotes: >-
  🎉 Highlights:
    - New Ingredient Parser for easier future development and language support
    - Improved recipe scraper with better nutritional data extraction


  ✨ New features:
    - Migrated to a new Ingredient Parser package
    - Enhanced recipe scraping with more accurate metadata parsing
    - Improved OIDC support


  🐛 Bug fixes:
    - Fixed issues with OpenAI response handling
    - Improved case-insensitive query filters
    - Resolved PostgreSQL capitalization problems


  Full release notes are found at https://github.com/mealie-recipes/mealie/releases
