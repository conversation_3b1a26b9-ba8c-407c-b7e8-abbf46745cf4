version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: qbittorrent_server_1
      APP_PORT: 8080
      PROXY_AUTH_ADD: "false"

  server:
    image: hotio/qbittorrent:release-5.1.0@sha256:2bbd7040de5db0bd1e80960add505ca6884c17b39e5319199b4db887ed8bc087
    environment:
      - PUID=1000
      - PGID=1000
      # - UMASK=002
    volumes:
      - ${APP_DATA_DIR}/data/config:/config
      # /app/qBittorrent/downloads is the default path set in qBittorrent
      - ${UMBREL_ROOT}/data/storage/downloads:/app/qBittorrent/downloads
    restart: on-failure
