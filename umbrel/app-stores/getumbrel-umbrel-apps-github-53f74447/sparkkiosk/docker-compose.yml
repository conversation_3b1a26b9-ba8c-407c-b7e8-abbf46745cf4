version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: sparkkiosk_web_1
      APP_PORT: 21214
      PROXY_AUTH_ADD: "false"

  web:
    image: jensgertsen/sparkkiosk:v1.0.0@sha256:d49500ce9c47d04436e64dae7e336416aa32f83673058154aa8a983e1420008c
    user: 1000:1000
    init: true
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data:/data
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
    environment:
      # LND
      LND_GRPC_ENDPOINT: $APP_LIGHTNING_NODE_IP
      LND_GRPC_PORT: $APP_LIGHTNING_NODE_GRPC_PORT
      LND_GRPC_CERT: "/lnd/tls.cert"
      LND_GRPC_MACAROON: "/lnd/data/chain/bitcoin/$APP_BITCOIN_NETWORK/admin.macaroon"

      # App
      APP_HIDDEN_SERVICE: http://$APP_HIDDEN_SERVICE
      APP_PASSWORD: $APP_PASSWORD
      APP_DOMAIN: $APP_DOMAIN
