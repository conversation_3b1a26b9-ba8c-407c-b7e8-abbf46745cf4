manifestVersion: 1
id: sparkkiosk
category: bitcoin
name: Sparkkiosk
version: "1.0.0-build-2"
tagline: Minimal LNURL kiosk
description: >-
  Use sparkkiosk to enable lightning payments from printed QR codes
  in selfservice kiosks / cafés or other places where POS interaction is not
  needed or an option.


  Log into the dashboard, create a new LNURL, set currency and amount and download QR to print. Monitor latest invoices.
developer: <PERSON><PERSON>
website: https://github.com/jensgertsen/sparkkiosk
dependencies:
  - lightning
repo: https://github.com/jensgertsen/sparkkiosk
support: https://github.com/jensgertsen/sparkkiosk/discussions
port: 21214
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: true
torOnly: false
submitter: <PERSON><PERSON>
submission: https://github.com/getumbrel/umbrel/pull/1312
releaseNotes: ""