version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: wanderer_web_1
      APP_PORT: 3000
      
  web:
    image: flomp/wanderer-web:v0.17.1@sha256:2d8c0e94725d45f5bf8fda888f862bb9f01300dbd140e5d0e547b8df186507c3
    user: "1000:1000"
    depends_on:
      search:
        condition: service_healthy
      db:
        condition: service_started
    environment:
      ORIGIN: http://${DEVICE_DOMAIN_NAME}:3111
      BODY_SIZE_LIMIT: Infinity
      PUBLIC_POCKETBASE_URL: http://wanderer_db_1:8090
      PUBLIC_DISABLE_SIGNUP: "false"
      UPLOAD_FOLDER: /app/uploads
      UPLOAD_USER: admin
      UPLOAD_PASSWORD: ${APP_PASSWORD}
      PUBLIC_VALHALLA_URL: https://valhalla1.openstreetmap.de
      PUBLIC_NOMINATIM_URL: https://nominatim.openstreetmap.org
      MEILI_URL: http://wanderer_search_1:7700
      MEILI_MASTER_KEY: ${APP_SEED}
    volumes:
      - ${APP_DATA_DIR}/data/uploads:/app/uploads
    restart: on-failure

  search:
    image: getmeili/meilisearch:v1.14.0@sha256:8cd411ba5d9ec2dfce02e241305208eebacce0fd74a72bece21cadd03dc566ce
    environment:
      MEILI_NO_ANALYTICS: "true"
      MEILI_URL: http://wanderer_search_1:7700
      MEILI_MASTER_KEY: ${APP_SEED}
    volumes:
      - ${APP_DATA_DIR}/data/data.ms:/meili_data/data.ms
    restart: on-failure
    healthcheck:
      test: curl --fail http://localhost:7700/health || exit 1
      interval: 15s
      retries: 10
      start_period: 20s
      timeout: 10s

  db:
    image: flomp/wanderer-db:v0.17.1@sha256:7491478294f18ea9aacea67b60b9c3af227bb62fadc89e4acdbacb43d46b937c
    user: "1000:1000"
    depends_on:
      search:
        condition: service_healthy
    environment:
      POCKETBASE_ENCRYPTION_KEY: ${APP_WANDERER_ENCRYPTION_KEY}
      MEILI_URL: http://wanderer_search_1:7700
      MEILI_MASTER_KEY: ${APP_SEED}
      ORIGIN: http://${DEVICE_DOMAIN_NAME}:3111
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/pb_data:/pb_data
