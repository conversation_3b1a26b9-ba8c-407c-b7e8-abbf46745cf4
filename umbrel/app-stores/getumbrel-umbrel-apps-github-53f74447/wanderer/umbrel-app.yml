manifestVersion: 1
id: wanderer
name: <PERSON><PERSON><PERSON>
tagline: A self-hosted trail database
category: files
version: "0.17.1"
port: 3111
description: >-
  🗺️ Wanderer is a self-hosted, open-source platform designed for outdoor enthusiasts who want to manage, explore, and share hiking and biking routes on their own terms. It provides a clean and modern interface for uploading GPX files, drawing new routes on an interactive map, and organizing them with detailed metadata such as difficulty, duration, and elevation gain. <PERSON><PERSON><PERSON> supports full-text search and powerful filtering to help users find exactly the route they are looking for based on various criteria like region, length, or type of activity.


  The app places a strong emphasis on user privacy and data ownership, offering a fully offline experience that can be run on your own server without any third-party involvement. It uses open map layers from providers like OpenStreetMap and allows for GPX export so routes can be used with common navigation devices.


  Wanderer is especially popular among people who enjoy organizing their personal collection of trails or sharing curated outdoor experiences with a group, club, or local community. It is easy to deploy on self-hosting platforms such as YunoHost and supports collaborative features that make it useful for small teams or families planning outdoor activities together. Its clean design, strong focus on privacy, and independence from commercial platforms make it an ideal solution for anyone who values control over their outdoor data.
developer: Flomp
website: https://wanderer.to/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2955
repo: https://github.com/Flomp/wanderer
support: https://github.com/Flomp/wanderer/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
  - 6.jpg
  - 7.jpg
releaseNotes: ""
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
