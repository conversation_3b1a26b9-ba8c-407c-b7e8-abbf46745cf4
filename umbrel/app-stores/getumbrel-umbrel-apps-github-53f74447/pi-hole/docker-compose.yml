version: "3.7"

services:
  server:
    image: pihole/pihole:2025.06.2@sha256:706fa18daedc85b73684b57468b7e20ec213cfaf98be735aadee183825188ea0
    # Pi-hole doesn't currently support running as non-root
    # https://github.com/pi-hole/docker-pi-hole/issues/685
    # user: "1000:1000"
    restart: on-failure
    network_mode: host
    volumes:
      - ${APP_DATA_DIR}/data/pihole:/etc/pihole/
      - ${APP_DATA_DIR}/data/dnsmasq:/etc/dnsmasq.d/
    environment:
      - FTLCONF_webserver_api_password=${APP_PASSWORD}
      - FTLCONF_webserver_port=8082,[::]:8082
      # Listen on all interfaces, permit all origins
      - FTLCONF_dns_listeningMode=all
    cap_add:
      - NET_ADMIN
