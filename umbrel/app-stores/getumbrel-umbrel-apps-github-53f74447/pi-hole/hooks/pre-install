#!/usr/bin/env bash
set -euo pipefail

RESOLVED_SERVICE_NAME="systemd-resolved"
RESOLVED_CONF_FILE="/etc/systemd/resolved.conf"

# Check if the 'systemd-resolved' service is running
if ! systemctl is-active --quiet "${RESOLVED_SERVICE_NAME}.service"; then
	echo "Service '${RESOLVED_SERVICE_NAME}' is not running"
	exit
fi

# Check if the DNS Stub Listener is running
if cat "${RESOLVED_CONF_FILE}" | grep --quiet "^DNSStubListener=no$"; then
	echo "DNSStubListener already turned off"
	exit
fi

echo "Turning off the DNS Stub Listener"

# Turn off DNSStubListener within config file
sed -r -i.orig 's/#?DNSStubListener=yes/DNSStubListener=no/g' "${RESOLVED_CONF_FILE}"

# Remove active resolv.conf which would have
# localhost (systemd-resolved's DNS listener) as the DNS resolver
rm -f /etc/resolv.conf

# Create a symlink between systemd-resolved's upstream resolv.conf
# and the OS's resolv.conf
ln -s /run/systemd/resolve/resolv.conf /etc/resolv.conf

echo "Restart service '${RESOLVED_SERVICE_NAME}'"
systemctl restart "${RESOLVED_SERVICE_NAME}"