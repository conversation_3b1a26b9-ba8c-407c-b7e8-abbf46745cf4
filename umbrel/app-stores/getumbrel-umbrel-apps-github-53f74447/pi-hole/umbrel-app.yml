manifestVersion: 1.1
id: pi-hole
category: networking
name: Pi-hole
version: "2025.06.2"
tagline: Block ads on your entire network
description: >-
  Instead of browser plugins or other software on each computer,
  install Pi-hole® on your Umbrel and your entire network is protected.
  Network-level blocking allows you to block ads in non-traditional places such
  as mobile apps and smart TVs, regardless of hardware or OS. Since
  advertisements are blocked before they are downloaded, network performance is
  improved and will feel faster. 


  In addition to blocking advertisements, Pi-hole® has an informative Web interface that shows stats on all the domains being queried on your network. Pi-hole® works fine with an existing DHCP server, but you can use Pi-hole®'s to keep your network management in one place.


  Pi-hole® and the Pi-hole® logo are registered trademarks of Pi-hole. Umbrel is not sponsored, endorsed by, or associated with Pi-hole®.
developer: Pi-hole®
website: https://pi-hole.net
dependencies: []
repo: https://github.com/pi-hole/docker-pi-hole
support: https://discourse.pi-hole.net
port: 8082
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: "/admin/"
defaultUsername: ""
deterministicPassword: true
torOnly: false
releaseNotes: >-
  ⚠️ If you have set up your router to use Pi-hole as its only DNS server, umbrelOS may be unable to resolve domain names during the update, preventing it from downloading the update files.
  To avoid issues, temporarily set a backup DNS server (e.g., 1.1.1.1) on your router before updating— or configure a permanent fallback DNS to prevent future issues.


  This release includes important bug fixes and improvements:
    - Fixed logging issues where types were missing from logs
    - Fixed crashes in DNS filtering functionality
    - Improved session cookie handling for better web interface access
    - Enhanced DNS cache performance with better CNAME handling
    - Updated embedded components for better stability


  Full release notes can be found at https://github.com/pi-hole/docker-pi-hole/releases/tag/2025.06.2
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/commit/9ca55a25e043dcd50d5cb92c6ec756d368bb4794
