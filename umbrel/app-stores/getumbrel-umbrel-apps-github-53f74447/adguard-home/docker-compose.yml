version: "3.7"

services:
  server:
    image: adguard/adguardhome:v0.107.63@sha256:320ab49bd5f55091c7da7d1232ed3875f687769d6bb5e55eb891471528e2e18f
    # override the default command to set the web address to port 8095 to avoid conflict with Thunderhub
    # command from Dockerfile: https://github.com/AdguardTeam/AdGuardHome/blob/master/docker/Dockerfile
    command: ["--no-check-update", "-c", "/opt/adguardhome/conf/AdGuardHome.yaml", "-w", "/opt/adguardhome/work", "--web-addr", "0.0.0.0:8095"]
    restart: on-failure
    network_mode: host
    volumes:
      - ${APP_DATA_DIR}/data/work:/opt/adguardhome/work
      - ${APP_DATA_DIR}/data/conf:/opt/adguardhome/conf
    cap_add:
      - NET_ADMIN
