manifestVersion: 1
id: adguard-home
category: networking
name: AdGuard Home
version: "0.107.63"
tagline: Network-wide software for blocking ads and tracking
description: >-
  ⚠️ See below for important set-up instructions.

  
  AdGuard Home is a comprehensive solution designed to block ads and tracking across all devices in your home network. As the world increasingly embraces the Internet of Things,
  with a growing number of connected devices, maintaining control over your entire network is crucial. AdGuard Home empowers you to manage this with ease, ensuring a secure and
  clutter-free browsing experience for every device under your roof.


  🛠️ SET-UP INSTRUCTIONS

  In the setup wizard, it is important to set the "Port" of the Admin Web Interface to 8095.
  This ensures that the app will not conflict with other apps from the Umbrel App Store, and that the app will be accessible
  when clicking on the app icon in the umbrelOS homescreen.
releaseNotes: >-
  This release includes security improvements and bug fixes:
    - Updated Go version to address security vulnerabilities
    - Various bug fixes and stability improvements

  Full release notes can be found at https://github.com/AdguardTeam/AdGuardHome/releases
developer: Adguard
website: https://adguard.com/fr/adguard-home/overview.html
dependencies: []
repo: https://github.com/AdguardTeam/AdGuardHome
support: https://github.com/AdguardTeam/AdGuardHome/issues
port: 8095
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: Julienpeps
submission: https://github.com/getumbrel/umbrel-apps/pull/1038
