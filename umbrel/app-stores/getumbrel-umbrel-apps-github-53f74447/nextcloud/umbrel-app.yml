manifestVersion: 1.1
id: nextcloud
category: files
name: Nextcloud
version: "31.0.6"
tagline: Productivity platform that keeps you in control
description: >-
  Nextcloud puts your data at your fingertips, under your control.
  Store your documents, calendar, contacts and photos on your Umbrel instead of
  some company's data center. Features:


  - Mobile and desktop sync 

  - Versioning and undelete 

  - Galleries and activity feed 

  - File editing and preview support for PDF, images, text files, Open Document, Word files and more. 

  - Smooth performance and easy user interface. 

  - Fine-grained control over access to data and sharing capabilities by user and by group.


  Note: After logging in to Nextcloud please change the password to something secure before sharing the address with anyone.
releaseNotes: >-
  This update includes various improvements and bug fixes:

    - Added commands for renaming files, clearing photo caches, listing orphan objects, checking files_external dependencies, and getting storage info
    - Improved encryption info reporting, metadata handling, file versioning, and error messages
    - Fixed various issues in sharing, previews, calendars, user status, and file drop handling
    - Optimized background jobs, webhooks, previews, and dashboard performance
    - Added OIDC support, task processing checks, and event logging
    - Updated modals, dropdowns, translations, and UI feedback across apps
    - Updated dependencies, workflows, and CI configuration
    - Enhanced accessibility, security, and admin controls

  Full release notes are found at https://github.com/nextcloud-releases/server/releases
developer: Nextcloud GmbH
website: https://nextcloud.com
dependencies: []
repo: https://github.com/nextcloud/server
support: https://help.nextcloud.com/categories
port: 8081
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: umbrel
deterministicPassword: true
torOnly: false
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/commit/dcaae95daa8ee3d7e8044d55d13887d8ff353030
