version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: nextcloud_web_1
      APP_PORT: 80
      PROXY_AUTH_ADD: "false"
  
  db:
    image: mariadb:10.6.21@sha256:ec79aa7a81a7667885cb69b6dc0415e032f22520bd5aca77927faffca4329924
    user: "1000:1000"
    command: --transaction-isolation=READ-COMMITTED --binlog-format=ROW
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/db:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=moneyprintergobrrr
      - MYSQL_PASSWORD=moneyprintergobrrr
      - MYSQL_DATABASE=nextcloud
      - MYSQL_USER=nextcloud
      - MARIADB_AUTO_UPGRADE=1
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
      start_period: 10s
      interval: 10s
      timeout: 5s
      retries: 3

  redis:
    image: redis:6.2.2-buster@sha256:e10f55f92478715698a2cef97c2bbdc48df2a05081edd884938903aa60df6396
    user: "1000:1000"
    restart: on-failure
    volumes:
      - "${APP_DATA_DIR}/data/redis:/data"

  web:
    image: nextcloud:31.0.6-apache@sha256:f77b2e823713ecc8b3dab20db4273269719439445170a380f57a9ebf9aef624c
    # Currently needs to be run as root, if we run as uid 1000 this fails
    # https://github.com/nextcloud/docker/blob/05026b029d37fc5cd488d4a4a2a79480e39841ba/21.0/apache/entrypoint.sh#L53-L77
    # user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/nextcloud:/var/www/html
    environment:
      - MYSQL_HOST=nextcloud_db_1
      - REDIS_HOST=nextcloud_redis_1
      - MYSQL_PASSWORD=moneyprintergobrrr
      - MYSQL_DATABASE=nextcloud
      - MYSQL_USER=nextcloud
      - TRUSTED_PROXIES=${NETWORK_IP}/16
      - NEXTCLOUD_ADMIN_USER=umbrel
      - NEXTCLOUD_ADMIN_PASSWORD=${APP_PASSWORD}
      - NEXTCLOUD_TRUSTED_DOMAINS=${APP_DOMAIN}:${APP_NEXTCLOUD_PORT} ${APP_HIDDEN_SERVICE} ${DEVICE_HOSTNAME}:${APP_NEXTCLOUD_PORT} ${APP_NEXTCLOUD_LOCAL_IPS} nextcloud_web_1
      - PHP_UPLOAD_LIMIT=1024G
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started

  cron:
    image: nextcloud:31.0.6-apache@sha256:f77b2e823713ecc8b3dab20db4273269719439445170a380f57a9ebf9aef624c
    # Currently needs to be run as root, if we run as uid 1000 this fails
    # https://github.com/nextcloud/docker/blob/05026b029d37fc5cd488d4a4a2a79480e39841ba/21.0/apache/entrypoint.sh#L53-L77
    # user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/nextcloud:/var/www/html
    entrypoint: /cron.sh
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
