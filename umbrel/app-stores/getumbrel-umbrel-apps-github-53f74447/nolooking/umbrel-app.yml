manifestVersion: 1
id: nolooking
category: bitcoin
name: nolooking
version: "0.2.2-alpha"
tagline: Open all your channels in one transaction
description: >-
  Nolooking leverages Pay-to-Endpoint (payjoin) to negotiate a channel open for your lightning node, initiated by any BIP78 supporting wallet.


  You can open multiple lightning channels in a single payment, improving privacy, saving on transaction fees and wait time.


  Read the article about Lightning Powered PayJoin: https://chaincase.app/words/lightning-payjoin to hear all the benefits!


  Please note that nolooking is still in early development. By using nolooking, you hold all responsibility for any loss of funds or other grievances which may arise. We are rapidly iterating on this product, feedback and contributions are strongly appreciated.
releaseNotes: >-
  - Recommend outbound nodes to open channels with.

  - Display payjoin errors in app.
developer: nolooking
website: https://nolooking.chaincase.app
dependencies:
  - lightning
repo: https://github.com/chaincase-app/nolooking
support: https://github.com/chaincase-app/nolooking/issues
port: 4444
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: "/"
defaultUsername: ""
defaultPassword: ""
submitter: nolooking
submission: https://github.com/getumbrel/umbrel-apps/pull/229
