manifestVersion: 1
id: romm
category: media
name: RomM
version: "3.10.2"
tagline: A beautiful, powerful, self-hosted rom manager
description: >-
  👾 RomM (ROM Manager) allows you to scan, enrich, browse and play your game collection with a clean and responsive interface.


  🎮 With support for multiple platforms, various naming schemes, and custom tags, RomM is a must-have for anyone who plays on emulators.


  **Features**
    - Scans your existing games library and enhances it with metadata from IGDB and MobyGames
    - Supports a large number of platforms
    - Play games directly from the browser using EmulatorJS and RuffleRS
    - Share your library with friends while limiting access and permissions
    - Supports MAME, Nintendo Switch, and Sony Playstation naming schemes
    - Detects and groups multifile games (e.g. PS1 games with multiple CDs)
    - Can parse tags in filenames (e.g. (E), (USA), (rev v1), etc.)
    - View, upload, update, and delete games from any modern web browser
developer: rommapp
website: https://github.com/rommapp/romm
repo: https://github.com/rommapp/romm
support: https://github.com/rommapp/romm/issues
port: 4002
submitter: <PERSON>
submission: https://github.com/getumbrel/umbrel-apps/pull/2020
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  This update includes several improvements and bug fixes:


  New features and enhancements:
    - Added support for doom and acpc in emulatorjs
    - RetroAchievements links now available on game and achievement badges
    - Achievements earned in hardcore mode are now displayed
    - Search state is now preserved when navigating between results and games
    - Pagination resets when changing search field values


  Bug fixes:
    - Fixed fallback for homepage when library is empty
    - Corrected RetroAchievements platform IDs for metadata matching
    - Action bar icons now consistently appear white for better readability
    - Parent folders are now created on game upload when not in the filesystem
    - Fixed IGDB age ratings
    - Resolved issue with 3D effect default value in localStorage


  Full release notes can be found at https://github.com/rommapp/romm/releases
