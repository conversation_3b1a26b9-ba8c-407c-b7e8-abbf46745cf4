manifestVersion: 1
id: lndg
category: bitcoin
name: LNDg
version: "1.10.1"
tagline: Analyze and automate your Lightning node management
description: LNDg is your command center for running a profitable and efficient
  routing node. From quickly viewing your node's health, automated rebalancing,
  selecting new potential peers and much more.
developer: cryptosharks131
website: https://github.com/cryptosharks131
dependencies:
  - lightning
repo: https://github.com/cryptosharks131/lndg
support: https://t.me/+-RxoZdi7snk2ZGYx
port: 8889
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: lndg-admin
deterministicPassword: true
releaseNotes: >-
  - Updated AutoFees with Inbound automation (enable with AF-InboundFees)
  
  - Improved performance for nodes with large forwards history
  
  - New metrics page added to identify unprofitable or stuck channels
  
  - And more... Full details can be found here: https://github.com/cryptosharks131/lndg/releases/tag/v1.10.1
submitter: cryptosharks131
submission: https://github.com/getumbrel/umbrel/pull/1189
