version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: $APP_LNDG_IP
      APP_PORT: $APP_LNDG_PORT

  web:
    image: ghcr.io/cryptosharks131/lndg:v1.10.1@sha256:c4f12e9222d81b74b665e61feab2b28d0e32e08d144b2cb1334e517eab01c9ad
    restart: on-failure
    stop_grace_period: 1m
    init: true
    volumes:
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/root/.lnd:ro
      - ${APP_DATA_DIR}/data/db:/app/data
      - ${APP_DATA_DIR}/data/logs/lndg-controller.log:/var/log/lndg-controller.log
    command:
      - sh
      - -c
      - python initialize.py -net '${APP_BITCOIN_NETWORK}' -rpc '${APP_LIGHTNING_NODE_IP}:${APP_LIGHTNING_NODE_GRPC_PORT}' -pw '${APP_PASSWORD}' -wn && python controller.py runserver 0.0.0.0:${APP_LNDG_PORT} > /var/log/lndg-controller.log 2>&1
    networks:
      default:
        ipv4_address: ${APP_LNDG_IP}
