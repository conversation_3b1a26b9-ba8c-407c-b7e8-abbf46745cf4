manifestVersion: 1
id: alby-nostr-wallet-connect
name: Nostr Wallet Connect
tagline: The power of the zaps at the tip of your fingers
category: finance
version: "0.4.2"
port: 58000
description: >-
  Connect your LND node over Nostr to zap directly from within supported apps like Damus & Amethyst.

  
  🛠️ Instructions

  - Amethyst step-by-step guide: https://blog.getalby.com/native-zapping-in-amethyst

  - Damus video guide: https://streamable.com/bareih
developer: Alby
website: http://getalby.com/
submitter: Alby
submission: https://github.com/getumbrel/umbrel-apps/pull/636
repo: https://github.com/getAlby/nostr-wallet-connect
support: https://github.com/getAlby/nostr-wallet-connect/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  - This release fixes a number of performance issues: High CPU and slow fetching for users with a large number of apps.
  
  - It also fixes a discrepancy in the timestamp format in the responses of extension methods.
dependencies:
  - lightning
path: ""
defaultUsername: ""
defaultPassword: ""
