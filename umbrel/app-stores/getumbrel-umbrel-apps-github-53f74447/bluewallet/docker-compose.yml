version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: bluewallet_lndhub_1
      APP_PORT: 3008
      PROXY_AUTH_ADD: "false"

  redis:
    image: "redis:6.2.2-buster@sha256:e10f55f92478715698a2cef97c2bbdc48df2a05081edd884938903aa60df6396"
    user: "1000:1000"
    command: "redis-server --requirepass moneyprintergobrrr"
    restart: "on-failure"
    stop_grace_period: "1m"
    init: true
    volumes:
      - "${APP_DATA_DIR}/data/redis:/data"

  lndhub:
    image: "bluewalletorganization/lndhub:v1.4.1@sha256:db673a8d360982984d05f97303e26dc0e5a3eea36ba54d0abdae5bbbeef31d3a"
    user: "1000:1000"
    depends_on: [ "redis" ]
    restart: "on-failure"
    stop_grace_period: "1m"
    init: true
    volumes:
      - "${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro"
    environment:
      PORT: 3008
      TOR_URL: "${APP_HIDDEN_SERVICE}"
      LND_CERT_FILE: "/lnd/tls.cert"
      LND_ADMIN_MACAROON_FILE: "/lnd/data/chain/bitcoin/${APP_BITCOIN_NETWORK}/admin.macaroon"
      CONFIG: '{ "rateLimit": 10000, "postRateLimit": 10000, "redis": { "port": 6379, "host": "bluewallet_redis_1", "family": 4, "password": "moneyprintergobrrr", "db": 0 }, "lnd": { "url": "$APP_LIGHTNING_NODE_IP:$APP_LIGHTNING_NODE_GRPC_PORT", "password": ""}}'
