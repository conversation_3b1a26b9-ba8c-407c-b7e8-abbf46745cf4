manifestVersion: 1.1
id: radarr
category: media
name: Radarr
version: "5.26.2.10099"
tagline: Your movie collection manager
description: >-
  <PERSON>r is a movie collection manager for Usenet and BitTorrent users. It can monitor multiple RSS feeds for new movies and will interface with clients and indexers to grab, sort, and rename them. It can also be configured to automatically upgrade the quality of existing files in the library when a better quality format becomes available. Note that only one type of a given movie is supported. If you want both an 4k version and 1080p version of a given movie you will need multiple instances.


  🛠️ SETUP INSTRUCTIONS

  Radarr on umbrelOS will automatically connect to download clients installed from the Umbrel App Store. Choose from Transmission, qBittorerent, and SABnzbd. Simply install your preferred client(s).


  All you need to do from there is add an indexer so Radarr can search for movies. You can add indexers directly within Radarr, or install Prowlarr from the Umbrel App Store for easier management of indexers across multiple apps.
  Add your indexers to Prowlarr and they will be automatically available in Radarr.
developer: Radarr
website: https://radarr.video/
dependencies:
  - transmission
repo: https://github.com/Radarr/Radarr
support: https://github.com/Radarr/Radarr/issues
port: 7878
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
releaseNotes: >-
  This update includes various improvements and new features:
    - Improved network drive types support for disk space monitoring
    - Enhanced movie metadata refresh handling
    - Better wording when removing root folders
    - Fixed Discord notification formatting issues
    - Improved quality definition limits endpoint
    - Added ability to clone import lists
    - Enhanced release year naming token support
    - Better handling of movies with empty IMDB IDs
    - Improved calendar page with release type options
    - Enhanced parsing for various release formats
    - Better error handling and stability improvements


  Full release notes for Radarr are available at https://github.com/Radarr/Radarr/releases
defaultUsername: ""
defaultPassword: ""
torOnly: false
permissions:
  - STORAGE_DOWNLOADS
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/commit/60878f278d544b204d8e7c96240c797f43a9b319
