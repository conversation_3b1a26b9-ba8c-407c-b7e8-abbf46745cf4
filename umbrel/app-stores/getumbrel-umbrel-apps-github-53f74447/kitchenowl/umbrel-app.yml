manifestVersion: 1
id: kitchenowl
name: KitchenOwl
tagline: A grocery list and recipe manager
category: files
version: "0.7.1"
port: 8474
description: >-
  👨‍🍳 KitchenOwl is a self-hosted, open-source application designed to simplify household management, particularly when it comes to organizing groceries, recipes, meal planning, and shared expenses. Built with privacy and usability in mind, KitchenOwl empowers individuals, families, and shared households to collaboratively manage kitchen-related tasks through a sleek and intuitive interface available on mobile, desktop, and the web.


  The app allows users to create and maintain dynamic shopping lists that can be shared and edited in real time. As users add items to the list, KitchenOwl automatically categorizes them and even learns from shopping habits to improve future suggestions. This makes weekly shopping more efficient and less prone to forgetting essential items. Items can be checked off as they're purchased, and multiple lists can be maintained simultaneously to accommodate different stores or household needs.


  Another key feature of KitchenOwl is its recipe management functionality. Users can manually add their own recipes or import them from popular websites using a built-in recipe parser. Recipes are stored in a centralized digital cookbook and can be searched or filtered by tags, ingredients, or categories. When planning meals, users can simply add a recipe’s ingredients to their shopping list with a single click, streamlining the preparation process.


  Meal planning is seamlessly integrated, allowing users to schedule meals for upcoming days or weeks. This feature not only helps in maintaining a balanced diet but also reduces food waste by encouraging the use of ingredients already on hand. KitchenOwl even nudges users to revisit recipes they haven’t used in a while, bringing forgotten favorites back into rotation.


  For shared living situations, KitchenOwl includes expense tracking capabilities. Household members can log expenses related to grocery shopping or other communal costs, and the app helps divide those expenses fairly among the group. This reduces the friction often associated with shared finances and ensures transparency within the household.


  KitchenOwl is actively developed and maintained by a community of contributors who prioritize open access and user feedback. The project welcomes new users and developers alike to participate, offering detailed documentation and support to help anyone get started. With its thoughtful design and robust feature set, KitchenOwl provides a comprehensive solution for modern, collaborative kitchen management.
developer: Tom Bursch
website: https://kitchenowl.org/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2638
repo: https://github.com/TomBursch/kitchenowl
support: https://github.com/TomBursch/kitchenowl/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
  - 6.jpg
releaseNotes: ""
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
