manifestVersion: 1
id: activepieces
category: automation
name: Activepieces
version: "0.64.2"
releaseNotes: >-
  This release brings various bug fixes and improvements.


  For full release notes, visit https://github.com/activepieces/activepieces/releases
tagline: Your friendliest open source AI automation tool
description: >-
  🤯 Welcome to Activepieces


  Your friendliest open source all-in-one automation tool, designed to be extensible through a type-safe pieces framework written in Typescript.



  🔥 Why Activepieces is Different:
    - 💖 Loved by Everyone: Intuitive interface and great experience for both technical and non-technical users with a quick learning curve.

    - 🌐 Open Ecosystem: All pieces are open source and available on npmjs.com, 60% of the pieces are contributed by the community.

    - 🛠️ Pieces are written in Typescript: Pieces are npm packages in TypeScript, offering full customization with the best developer experience, including hot reloading for local piece development on your machine. 😎

    - 🤖 AI-Ready: Native AI pieces let you experiment with various providers, or create your own agents using our AI SDK, and there is Copilot to help you build flows inside the builder.

    - 🏢 Enterprise-Ready: Developers set up the tools, and anyone in the organization can use the no-code builder. Full customization from branding to control.

    - 🔒 Secure by Design: Self-hosted and network-gapped for maximum security and control over your data.

    - 🧠 Human in the Loop: Delay execution for a period of time or require approval. These are just pieces built on top of the piece framework, and you can build many pieces like that. 🎨

    - 💻 Human Input Interfaces: Built-in support for human input triggers like "Chat Interface" 💬 and "Form Interface" 📝
developer: Activepieces
website: https://www.activepieces.com/
dependencies: []
repo: https://github.com/activepieces/activepieces/
support: https://community.activepieces.com/
port: 8146
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: al-lac
submission: https://github.com/getumbrel/umbrel-apps/pull/2481
