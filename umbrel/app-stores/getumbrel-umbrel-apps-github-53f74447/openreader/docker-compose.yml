version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: openreader_web_1
      APP_PORT: 3003

  web:
    image: ghcr.io/richardr1126/openreader-webui:0.3.1@sha256:5478b3003abd03c35b2219af561ff105a0e482f057435a5f0845d384b322fad2
    user: "1000:1000"
    environment:
      - API_BASE=http://kokoro_web_1:8880/v1 #Set the TTS API_BASE URL and/or API_KEY as the default for all devices. These values can also be overridden in the UI.
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/docstore:/app/docstore
