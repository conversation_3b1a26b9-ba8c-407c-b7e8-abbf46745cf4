manifestVersion: 1
id: vaultwarden
category: files
name: Vaultwarden
version: "1.34.1"
tagline: Unofficial Bitwarden® compatible server
description: >-
  Vaultwarden is an alternative
  implementation of the Bitwarden® server API that is compatible with
  Bitwarden® clients*. Vaultwarden is perfect for self-hosting all of
  your passwords in an encrypted vault on your Umbrel. Vaultwarden is a full
  implementation of Bitwarden® API that includes:


  - Organizations support

  - Attachments

  - Vault API support

  - Serving the static files for Vault interface

  - Website icons API

  - Authenticator and U2F support

  - Yu<PERSON><PERSON>ey and Duo support

  - Emergency Access


  🛠️ SET-UP INSTRUCTIONS

  Vaultwarden's web interface is not accessible over http, meaning that to access it for initial set-up you need to use Tor. Here's how to do it:

  - Ensure your Umbrel is accessible over Tor by going to Settings in your dashboard and toggling "Remote Tor access" to enabled. You will need to restart your Umbrel if Remote Tor access was previously disabled.
  
  - Access your umbrel through a Tor browser using the .onion address shown in Settings under "Remote Tor access". 

  - In your Tor browser, open the Vaultwaden app and create an account.

  - Once you've created an account, you can connect your Vaultwarden server from any Bitwarden app (mobile, desktop, browser, etc) by adding your server IP details.

  - For the most robust connection from anywhere, install Tailscale on your Umbrel and your devices, and use the Tailscale IP address to connect to your Vaultwarden server.


  *Please note that Vaultwarden is not associated with the Bitwarden® project nor 8bit Solutions LLC. When using this app, please report any bugs or suggestions to us directly, regardless of whatever clients you are using (mobile, desktop, browser, etc), and do not use Bitwarden®'s official support channels.

releaseNotes: >-
  Key highlights in this release:
    - Updated web-vault to v2025.5.0
    - New registration flow with email verification
    - Added support for mutual TLS and attachment export
    - CLI now supports uploading send files with truncated filenames
    - IP addresses now logged on invalid email 2FA attempts
    - TOTP menu and web-client email 2FA support added
    - Resolved CVE-2025-25188 and other dependency issues
    - Enhanced admin interface and CLI usability
    - Fixed an issue with admin diagnostics


  Full release notes are available at https://github.com/dani-garcia/vaultwarden/releases
developer: Daniel García
website: https://github.com/dani-garcia
dependencies: []
repo: https://github.com/dani-garcia/vaultwarden
support: https://vaultwarden.discourse.group/
port: 8089
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: true
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/commit/44c842bddafffa558569f730cb0d5442ee073bf5
