version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: vaultwarden_server_1
      APP_PORT: 8089
      PROXY_AUTH_ADD: "false"

  server:
    image: vaultwarden/server:1.34.1@sha256:48267ea14d8649b2e553a5fe290c40b5dd94d54e9a24b26ae7134a75a659695f
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data:/data
    environment:
      ROCKET_PORT: 8089
