version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: tdex_caddy_1
      APP_PORT: 8080

  oceand:
    image: ghcr.io/vulpemventures/oceand:v0.1.17@sha256:37b813b43b068e32d918b9f42539c33287308cc81d00909c742b7164dfefc0c4
    tty: true
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/ocean-data:/home/<USER>/.oceand
    environment:
      OCEAN_LOG_LEVEL: 5
      OCEAN_DB_TYPE: "badger"
      OCEAN_NO_TLS: "true"
      OCEAN_NO_PROFILER: "true"
      OCEAN_UTXO_EXPIRY_DURATION_IN_SECONDS: 240

  tdexd:
    image: ghcr.io/tdex-network/tdexd:v1.0.1@sha256:eef1f31e98208e9a35ddf308317bc1df71065e189fa0915c769c7a01394ef79a
    tty: true
    restart: on-failure
    depends_on:
      - oceand
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/tdexd:/home/<USER>/.tdex-daemon
    ports:
     - ${APP_TDEX_PORT}:${APP_TDEX_PORT}
    environment:
      TDEX_LOG_LEVEL: 5
      TDEX_OPERATOR_LISTENING_PORT: "${APP_TDEX_PORT}"
      TDEX_TRADE_LISTENING_PORT: "${APP_TDEX_PORT}"
      TDEX_NO_OPERATOR_TLS: "true"
      TDEX_WALLET_ADDR: oceand:18000
      # these vars are used to generate the connect URL
      # we assume to always show the external endpoint
      # reachable remotely by other dashboards via Tor
      TDEX_CONNECT_ADDR: "${APP_TDEX_DAEMON_HIDDEN_SERVICE}"
      TDEX_CONNECT_PROTO: http

  dashboard:
    image: ghcr.io/tdex-network/dashboard:v1.0.2@sha256:71d598afb0d5ec9966306f4ef0d5f6839a9d7dc50726a935f3d0bb38f1925d78
    restart: on-failure
    environment:
      USE_PROXY: "false"
      IS_PACKAGED: "true"
      TRADER_HIDDEN_SERVICE: "${APP_TDEX_DAEMON_HIDDEN_SERVICE}"

  caddy:
    image: caddy:2.6.4-alpine@sha256:4dfec6c3b22c36b63ea4a3633c7cdbdaa9926d1324c27db2b0e2b70ef9cd105a
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/caddy-data/Caddyfile:/etc/caddy/Caddyfile

  tor:
   image: getumbrel/tor:0.4.7.8@sha256:2ace83f22501f58857fa9b403009f595137fa2e7986c4fda79d82a8119072b6a
   user: "1000:1000"
   restart: on-failure
   volumes:
     - ${APP_DATA_DIR}/torrc:/etc/tor/torrc:ro
     - ${TOR_DATA_DIR}:/data
   environment:
    HOME: "/tmp"
