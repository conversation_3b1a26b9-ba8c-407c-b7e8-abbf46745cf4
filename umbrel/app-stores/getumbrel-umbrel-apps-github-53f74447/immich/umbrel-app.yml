manifestVersion: 1.1
id: immich
category: files
name: Immich
version: "v1.135.3"
tagline: High-performance photo and video backup solution
description: >-
  An open-source and high-performance self-hosted backup solution for the videos and photos on your mobile device


  ⚠️ Immich is under very active development. Expect bugs and changes. Do not use it as the only way to store your photos and videos!


  Features:

  - Upload and view videos and photos

  - Auto backup when the app is opened

  - Selective album(s) for backup

  - Download photos and videos to a local device

  - Multi-user support

  - Album and Shared albums

  - Scrubbable/draggable scrollbar

  - Support RAW (HEIC, HEIF, DNG, Apple ProRaw)

  - Metadata view (EXIF, map)

  - Search by metadata, objects and image tags

  - Administrative functions (user management)

  - Background backup

  - Virtual scroll

  - OAuth support

  - LivePhoto backup and playback

  - User-defined storage structure
releaseNotes: >-
  ⚠️ As usual, please check that your mobile app is compatible with this release of Immich.


  This release includes important bug fixes and improvements:
    - Fixed database migration issues on some instances
    - Fixed timezone-related issues affecting web timeline display
    - Fixed navigation button disappearing in detail view when opened from albums
    - Fixed local network permission prompt not showing on iOS
    - Improved people sorting and display
    - Better contrast for checkmark indicators
    - iOS widgets now support iOS 17 and above


  Full release notes are found at https://github.com/immich-app/immich/releases
developer: <PERSON>
website: https://www.immich.app
dependencies: []
repo: https://github.com/immich-app/immich
support: https://github.com/immich-app/immich/discussions
port: 2283
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: levma
submission: https://github.com/getumbrel/umbrel-apps/pull/239
