manifestVersion: 1
id: oak-node
category: bitcoin
name: Oak Node
version: "0.3.8"
tagline: Do more with your LND node
description: >-
  Oak Node lets you schedule recurring tips to your favorite projects or content creators. All they need is a Lightning Address.


  Are you on Nostr? Oak Node's Nostr bot has all sorts of goodies. NWC support? Sure. A way to DM your bot from anywhere
  and get it to send or receive sats for you? Yes, it's there. Perhaps even ... a way to zap notes on Damus? Included
  as well, check out the bot's Shadowy LN Tips module.
  
  
  Do you want to access your LN wallet from your phone, without having to use tor? The Email bot lets you check your balance,
  create and pay invoices directly from your phone. Works well even when your phone has slow or poor data reception.
releaseNotes: >-
  This update brings dark mode and improved NWC support.
developer: Carlos
website: https://oak-node.net
dependencies:
  - lightning
repo: https://oak-node.net
support: https://oak-node.net/forum
port: 8100
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: ok300
submission: https://github.com/getumbrel/umbrel-apps/pull/60