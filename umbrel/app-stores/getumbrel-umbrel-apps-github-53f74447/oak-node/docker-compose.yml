version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: oak-node_web_1
      APP_PORT: 8100

  web:
    image: oak-node.net/oak:v0.3.8@sha256:226e4b91f753babbf166893dfa044ed80eafd00a13e75033c374bd11824ef961
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - $APP_LIGHTNING_NODE_DATA_DIR:/lnd-dir:ro
      - ${APP_DATA_DIR}/data:/data
      - ${APP_DATA_DIR}/log:/oak/log
    environment:
      ROCKET_PORT: 8100
      ROCKET_ADDRESS: 0.0.0.0
      OAK_DATA_DIR: /data
      OAK_LND_REST_API_URL: https://$APP_LIGHTNING_NODE_IP:$APP_LIGHTNING_NODE_REST_PORT
      OAK_LND_MACAROON_PATH: /lnd-dir/data/chain/bitcoin/$APP_BITCOIN_NETWORK/admin.macaroon
      OAK_LND_CERT_PATH: /lnd-dir/tls.cert
      OAK_ONION_SOCKS5_HOST: $TOR_PROXY_IP
      OAK_ONION_SOCKS5_PORT: $TOR_PROXY_PORT
