version: '3.7'
services:
  app_proxy:
    environment:
      APP_HOST: adventurelog_web_1
      APP_PORT: 3000
      PROXY_AUTH_ADD: "false"

  web:
    image: ghcr.io/seanmorley15/adventurelog-frontend:v0.10.0@sha256:7c86f835d8dd8adb6c5c9aa3eef3969eaac3003306bee27df946625429694e43
    restart: on-failure
    environment:
      PUBLIC_SERVER_URL: "http://adventurelogserver1:8000"
      ORIGIN: "http://${DEVICE_DOMAIN_NAME}:8015"
      BODY_SIZE_LIMIT: "Infinity"
    depends_on:
      - server
  db:
    image: ghcr.io/baosystems/postgis:15-3.5@sha256:ac619116c8ee81a386eb878956950d798b57c502985c12eb6e7be1598aa8d02f
    restart: on-failure
    environment:
      POSTGRES_DB: database
      POSTGRES_USER: adventure
      POSTGRES_PASSWORD: adventure
    volumes:
      - ${APP_DATA_DIR}/data/db:/var/lib/postgresql/data/

  server:
    image: ghcr.io/seanmorley15/adventurelog-backend:v0.10.0@sha256:211b05be806df66c33e964aeba698f2553faf125513eed7d9e6472a05fcb5b31
    container_name: adventurelogserver1 # needs to be set for the frontend to be able to connect (underline not supported)
    restart: on-failure
    environment:
      - PGHOST=adventurelog_db_1
      - PGDATABASE=database
      - PGUSER=adventure
      - PGPASSWORD=adventure
      - SECRET_KEY=adventure
      - DJANGO_ADMIN_USERNAME=admin
      - DJANGO_ADMIN_PASSWORD=admin
      - DJANGO_ADMIN_EMAIL=<EMAIL>
      - PUBLIC_URL=http://${DEVICE_DOMAIN_NAME}:8016
      - CSRF_TRUSTED_ORIGINS=http://${DEVICE_DOMAIN_NAME}:8015
      - DEBUG=False
      - FRONTEND_URL=http://${DEVICE_DOMAIN_NAME}:8015
    ports:
      - '8016:80'
    depends_on:
      - db
    volumes:
      - ${APP_DATA_DIR}/data/app:/code/media/
