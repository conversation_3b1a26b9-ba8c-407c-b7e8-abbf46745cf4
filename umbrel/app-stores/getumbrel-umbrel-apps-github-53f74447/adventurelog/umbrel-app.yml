manifestVersion: 1.1
id: adventurelog
category: files
name: AdventureLog
version: "v0.10.0"
tagline: Self-hostable travel tracker and trip planner
description: >-
  🗺️ Starting from a simple idea of tracking travel locations (called adventures), AdventureLog has grown into a full-fledged travel companion. With AdventureLog, you can log your adventures, keep track of where you've been on the world map, plan your next trip collaboratively, and share your experiences with friends and family.


  AdventureLog was created to solve a problem: the lack of a modern, open-source, user-friendly travel companion. Many existing travel apps are either too complex, too expensive, or too closed-off to be useful for the average traveler. AdventureLog aims to be the opposite: simple, beautiful, and open to everyone.
releaseNotes: >-
  This release of AdventureLog brings timezone-aware planning, smoother maps, and simpler deployment. This release refines many of the core features and addresses community feedback.


  Key Highlights:
    - Added timezone-aware travel planning with chronological map and timeline views
    - Improved location tools with Google Maps autocomplete and better geocoding
    - Enhanced mobile experience with full-width maps and responsive design
    - Refined forms, layouts, and navigation for smoother user experience
    - Expanded language support and smarter back/forward navigation
    - Improved Immich integration with image copy options and duplication prevention
    - Switched Docker to supervisord for better startup and logging


  Full release notes can be found at https://github.com/seanmorley15/AdventureLog/releases/
developer: <PERSON>
website: https://adventurelog.app/
repo: https://github.com/seanmorley15/AdventureLog
support: https://github.com/seanmorley15/AdventureLog/issues
port: 8015
submitter: Mateo Silguero
submission: https://github.com/getumbrel/umbrel-apps/pull/2222
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
