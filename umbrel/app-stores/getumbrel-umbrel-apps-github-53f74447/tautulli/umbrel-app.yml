manifestVersion: 1
id: tautulli
category: media
name: tautulli
version: "2.15.2"
tagline: Monitor your Plex Media Server
description: >-
  Tautulli is a 3rd party application that you can run alongside your Plex Media Server to monitor activity and track various statistics.
  Most importantly, these statistics include what has been watched, who watched it, when and where they watched it, and how it was watched.
  The only thing missing is "why they watched it", but who am I to question your 42 plays of Frozen.


  🛠️  SET-UP INSTRUCTIONS
  
  During initial set-up, you will need to input your Umbrel device's IP address to connect to your Plex Media Server.
  You can find your device's IP address by visiting your router's admin dashboard or by using an IP scanning tool like Angry IP Scanner.
developer: Tautulli
website: https://tautulli.com/
dependencies: []
repo: https://github.com/Tautulli/Tautulli
support: https://github.com/Tautulli/Tautulli/wiki
port: 8212
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: false
releaseNotes: >-
  This update includes several improvements and fixes:


    - Added link to library by clicking media type icon
    - Added stream count to tab title on homepage
    - Improved history tracking and notifications
    - Enhanced graphs with new Total line for daily streams
    - Various UI improvements and bug fixes


  Full release notes and detailed information are available at https://github.com/Tautulli/Tautulli/releases
submitter: Pranshu Agrawal
submission: https://github.com/getumbrel/umbrel-apps/pull/673
