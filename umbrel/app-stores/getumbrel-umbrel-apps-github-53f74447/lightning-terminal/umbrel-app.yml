manifestVersion: 1
id: lightning-terminal
category: bitcoin
name: Lightning Terminal
version: "0.15.0-alpha"
tagline: The easiest way to manage channel liquidity
description: >-
  Lightning Terminal is the easiest way to manage inbound and
  outbound liquidity on the Lightning Network. Keep your channels open and the
  funds flowing. It provides a visual interface for interacting with your
  channels and balances using Loop.


  🚨 CRITICAL REMINDER FOR TAPROOT ASSETS USERS:

  Uninstalling this app permanently erases application data, which includes Taproot Assets and Bitcoin funds.
  Before uninstalling or re-installing this app, you must backup the application's .tapd data folder in order to restore your taproot assets upon reinstallation.
  Failure to do so will result in permanent loss of funds. We recommend that you make regular backups.
  Follow this guide to backup your tapd data:
  https://community.umbrel.com/t/backing-up-taproot-assets-tapd-data-in-lightning-terminal/14360


  Why use Pool?

  - Earn a return on Lightning capital

  - Rent a channel and start accepting payments instantly

  - Open channels for less with transaction batching


  Why use Loop?

  - Add "inbound liquidity" to receive payments

  - Reduce transaction fees by recycling and reusing Lightning channels

  - Send funds to and from users or services that aren't yet Lightning enabled

  - Configurable wait times and "batching" allow for further fee savings

  - Refill and offload funds from any number of Lightning channels in a single on-chain transaction
developer: Lightning Labs
website: https://lightning.engineering
dependencies:
  - lightning
repo: https://github.com/lightninglabs/lightning-terminal
support: https://github.com/lightninglabs/lightning-terminal/issues/new
port: 3004
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
path: ""
defaultUsername: ""
deterministicPassword: true
releaseNotes: >-
  ⚠️ Please update your Lightning Node app to the latest version available in the app store to ensure compatibility with Lightning Terminal.


  This release of Lightning Terminal (LiT) ships with `lnd v0.19.1-beta`, `loop v0.31.2-beta`, `faraday v0.2.16-alpha`,
  `pool v0.6.6-beta` and `tapd v0.6.0`, which comes with full group key support for Taproot Assets Channels!


  IMPORTANT NOTE: To avoid loss of funds, it's imperative that you read the
  Operational Safety Guidelines
  (https://github.com/lightninglabs/taproot-assets/blob/main/docs/safety.md)
  before using tapd on mainnet!


  🚨 CRITICAL REMINDER FOR TAPROOT ASSETS USERS:

  Uninstalling this app permanently erases application data, which includes Taproot Assets and Bitcoin funds.
  Before uninstalling or re-installing this app, you must backup the application's .tapd data folder in order to restore your taproot assets upon reinstallation.
  Failure to do so will result in permanent loss of funds. We recommend that you make regular backups.
  Follow this guide to backup your tapd data:
  https://community.umbrel.com/t/backing-up-taproot-assets-tapd-data-in-lightning-terminal/14360


  The Taproot Assets daemon is still in alpha state, which means there can
  still be bugs and not all desired data safety and backup mechanisms have been
  implemented yet. Releasing on mainnet mainly signals that there will be no
  breaking changes in the future and that assets minted with v0.3.0 will be
  compatible with later versions.

  ## Breaking changes
  
  The configuration value (taproot-assets.universe.public-access) and command line 
  flag (--taproot-assets.universe.public-access) now needs a value and is no longer a boolean. The value now controls 
  whether the node's universe database can be accessed over RPC and either read (value r) or written to (value w) or 
  both (value rw). So existing nodes with the configuration file value taproot-assets.universe.public-access=true need 
  to change the value to taproot-assets.universe.public-access=rw. Users specifying the command line flag 
  --taproot-assets.universe.public-access just need to append a value, for example 
  --taproot-assets.universe.public-access=rw.

  We'll be continuously working to improve the user experience based on
  feedback from the community.


  This release packages LND v0.19.1-beta, Taproot Assets Daemon v0.6.0-alpha,
  Loop v0.31.2-beta, Pool v0.6.6-beta and Faraday v0.2.16-alpha.
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/pull/348