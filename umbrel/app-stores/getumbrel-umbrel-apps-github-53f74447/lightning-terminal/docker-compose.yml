version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: lightning-terminal_web_1
      APP_PORT: 3004

  web:
    image: lightninglabs/lightning-terminal:v0.15.0-alpha@sha256:0a56e4cb34dd76265c568835c88501b626065a8d78d41ace757ccdbebdd8b205
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data:/data
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
    environment:
      HOME: "/data"
      APP_PASSWORD: "$APP_PASSWORD"
    command:
        - --uipassword_env=APP_PASSWORD
        - --insecure-httplisten=0.0.0.0:3004
        - --network="$APP_BITCOIN_NETWORK"
        - --lnd-mode="remote"
        - --remote.lnd.rpcserver=$APP_LIGHTNING_NODE_IP:$APP_LIGHTNING_NODE_GRPC_PORT
        - --remote.lnd.macaroonpath="/lnd/data/chain/bitcoin/$APP_BITCOIN_NETWORK/admin.macaroon"
        - --remote.lnd.tlscertpath="/lnd/tls.cert"
