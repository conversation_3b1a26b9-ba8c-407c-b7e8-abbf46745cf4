manifestVersion: 1
id: nostr-relay
category: social
name: Nostr Relay
version: "1.2.0"
tagline: Backup all your Nostr activity with your private relay
description: >
  Introducing Nostr Relay — an official app by Umbrel.

  
  Step 1. Connect your Nostr client (e.g., <PERSON><PERSON>, Amethys<PERSON>) to your private relay for seamless backup of all Nostr activity. In Damus, add your Relay URL via Menu > Relays.

  
  Tip: Install Tailscale on your Umbrel and your devices for an uninterrupted connection between your clients and your relay, even when you're away from your home network. Enable Tailscale's MagicDNS and use ws://umbrel:4848 as your Relay URL.

  
  Step 2. Tap the globe icon on the top to back up past Nostr activity from your public relays and ensure uninterrupted future backups, even if the connection between your private relay and Nostr client is disrupted.


  That's it! Your past and future Nostr activity will be now backed up to your private relay.

  
  Nostr Relay is powered by the open source nostr-rs-relay project — a Rust implementation of Nostr relay. It currently supports the entire relay protocol, including NIP-01, NIP-02, NIP-03, NIP-05, NIP-09, NIP-11, NIP-12, NIP-15, NIP-16, NIP-20, NIP-22, NIP-26, NIP-28, and NIP-33.
releaseNotes: >-
  🎉 Widgets are here for umbrelOS 1.0.


  This update brings two new widgets for Nostr Relay, allowing you to see your latest notes and latest event kinds at a glance right from your Umbrel's home screen:


  - Latest Notes: A widget that displays the latest notes that are synced to your Nostr Relay.


  - Latest Events: A widget that shows the latest event kinds that are synced to your Nostr Relay, including posts, reactions, profile updates, and more.


  After updating to umbrelOS 1.0, you can add widgets by right-clicking on the home screen and selecting "Edit widgets", or by clicking on Widgets in the Dock.
developer: Umbrel
website: https://umbrel.com
dependencies: []
repo: https://github.com/getumbrel/umbrel-nostr-relay
support: https://community.getumbrel.com
port: 4848
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: false
torOnly: false
widgets:
  - id: "latest-kind1"
    type: "list"
    refresh: "5s"
    endpoint: "relay-proxy:80/relay-proxy/widgets/latest-kind1"
    link: ""
    example:
      type: "list"
      link: ""
      items:
        - text: "one-click app restarts, arriving in umbrelOS 1.0"
          subtext: "01:11 Mar 02, 2024"
        - text: "woop! glad it arrived before christmas! welcome aboard 🫡"
          subtext: "03:07 Dec 28, 2023"
        - text: "Congrats! Can’t wait for you to get your hands on it 🫶"
          subtext: "11:40 Nov 24, 2023"
  - id: "latest-events"
    type: "list-emoji"
    refresh: "5s"
    endpoint: "relay-proxy:80/relay-proxy/widgets/latest-events"
    link: ""
    example:
      type: "list-emoji"
      link: ""
      count: "503"
      items:
        - emoji: "💭"
          text: "Post"
        - emoji: "🔏"
          text: "Encrypted DM"
        - emoji: "🤙"
          text: "Reaction"
        - emoji: "🤙"
          text: "Reaction"
        - emoji: "📝"
          text: "Profile Update"
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/398
