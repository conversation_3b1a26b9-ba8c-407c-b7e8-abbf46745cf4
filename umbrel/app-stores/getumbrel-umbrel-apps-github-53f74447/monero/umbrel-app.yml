manifestVersion: 1.1
id: monero
category: finance
name: Monero Node
version: "********"
tagline: Run a monero node
description: >-
  Run your monero node and independently store and validate every single Monero transaction with it.
  This is a full node that will download the entire Monero blockchain and store it on your Umbrel. 
  This is the most secure way to run a Monero node, but it will take a long time to sync and will require a lot of storage space.
developer: deverickapollo
website: https://www.getmonero.org/
dependencies: []
repo: https://github.com/deverickapollo/umbrel-monero
support: https://github.com/deverickapollo/umbrel-monero/issues
port: 9976
submitter: deverickapollo
submission: https://github.com/getumbrel/umbrel-apps/pull/690
gallery: 
  - 1.jpg 
  - 2.jpg 
  - 3.jpg 
path: ""
defaultPassword: ""
releaseNotes: >-
  This is a recommended release that fixes multiple daemon-related (monerod) network vulnerabilities.


  Some highlights of this release are:
    - reduce disk writes from 2 to 1 per transaction
    - fix temp fails causing alt blocks to be permanently invalid
    - fix daemon connection speed throttling incorrectly
    - fix --anonymous-inbound data leak
    - update seed nodes
    - minor bug fixes and improvements

  Further details can be found at https://www.getmonero.org/2025/04/05/monero-********-released.html
