version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: monero_server_1
      APP_PORT: 9976

  server:
    image: deverickapollo/umbrel-monero:v1.0.8@sha256:4cfb0a9b852175b73c9c3cb37771eb28c20fd21140bb85a98726dd6d09ee03f9
    depends_on: [monerod]
    restart: on-failure
    expose:
      - "9976"
    volumes:
      - ${APP_DATA_DIR}/data/app:/data # volume to persist advanced settings json
      - ${APP_MONERO_DATA_DIR}:/monero/.monero # volume to persist bitmonero.conf
    environment:
      PORT: "9976"
      MONERO_HOST: "${APP_MONERO_NODE_IP}"
      MONERO_P2P_PORT: "${APP_MONERO_P2P_PORT}"
      MONERO_RPC_PORT: "${APP_MONERO_RPC_PORT}"
      MONERO_RESTRICTED_RPC_PORT: "${APP_MONERO_RESTRICTED_RPC_PORT}"
      MONERO_DEFAULT_NETWORK: "${MONERO_DEFAULT_NETWORK}"
      MONERO_RPC_USER: "${APP_MONERO_RPC_USER}"
      MONERO_RPC_PASSWORD: "${APP_MONERO_RPC_PASS}"
      MONERO_RPC_HIDDEN_SERVICE: "${APP_MONERO_RPC_HIDDEN_SERVICE}"
      MONERO_P2P_HIDDEN_SERVICE: "${APP_MONERO_P2P_HIDDEN_SERVICE}"
      DEVICE_DOMAIN_NAME: "${DEVICE_DOMAIN_NAME}"
      MONEROD_IP: "${APP_MONERO_NODE_IP}"
      TOR_PROXY_IP: "${APP_MONERO_TOR_PROXY_IP}"
      TOR_PROXY_PORT: "9050"
      TOR_PROXY_CONTROL_PORT: "9051"
      TOR_PROXY_CONTROL_PASSWORD: "moneyprintergobrrr"
      I2P_DAEMON_IP: "${APP_MONERO_I2P_DAEMON_IP}"
      I2P_DAEMON_PORT: "7656"
    networks:
      default:
        ipv4_address: $APP_MONERO_IP

  monerod:
    user: "1000:1000"
    # "unless-stopped" ensures monerod restarts via UI-triggered daemon restarts, not just on failure
    restart: unless-stopped
    stop_grace_period: 15m
    command: "${APP_MONERO_COMMAND}"
    image: ghcr.io/sethforprivacy/simple-monerod:v0.18.4.0@sha256:eb143b50b427348f74b94d9342aaa6c9a1b4e74310cf1bfb1aa8623361ec20a1
    ports:
      - "${APP_MONERO_P2P_PORT}:${APP_MONERO_P2P_PORT}"
      - "${APP_MONERO_RPC_PORT}:${APP_MONERO_RPC_PORT}"
    volumes:
      - "${APP_MONERO_DATA_DIR}:/home/<USER>/.bitmonero"
    networks:
      default:
        ipv4_address: $APP_MONERO_NODE_IP

  tor:
    image: getumbrel/tor:*******@sha256:2ace83f22501f58857fa9b403009f595137fa2e7986c4fda79d82a8119072b6a
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/torrc:/etc/tor/torrc:ro
      - ${TOR_DATA_DIR}:/data
    environment:
      HOME: "/tmp"
    networks:
      default:
        ipv4_address: "${APP_MONERO_TOR_PROXY_IP}"
  
  # i2pd_daemon:
  #   image: purplei2p/i2pd:release-2.44.0@sha256:d154a599793c393cf9c91f8549ba7ece0bb40e5728e1813aa6dd4c210aa606f6
  #   user: "root"
  #   command: --sam.enabled=true --sam.address=0.0.0.0 --sam.port=7656 --loglevel=error
  #   restart: on-failure
  #   volumes:
  #     - ${APP_DATA_DIR}/data/i2pd:/home/<USER>/data
  #   networks:
  #     default:
  #       ipv4_address: "${APP_MONERO_I2P_DAEMON_IP}"
