manifestVersion: 1
id: morphos
category: files
name: Morphos server
version: "0.6.0"
tagline: Self-hosted file converter
description: >-
  Today we are forced to rely on third party services to convert files to other formats. This is a serious threat to our privacy, if we use such services to convert files with  highly sensitive personal data. It can be used against us, sooner or later.


  Morphos server aims to solve the problem mentioned above, by providing a self-hosted server to convert files privately. The project provides an user-friendly web UI.


  Features:

  - Convert images to other image formats

  - Convert PDF to images and vice versa

  - Convert DOCX to PDF and vice versa

  - Convert XLSX to CSV and vice versa
releaseNotes: >-
  New features:
    - Add dark mode
    - Add ffmpeg to handle images
    - Add support for ebooks
    - Add an API so other systems can interact with morphos

  Bug fixes:
    - Fix zip files preview
    - Add non-root user to the dockerfile 
developer: <PERSON>ergara
website: https://github.com/danvergara/morphos
submitter: danvergara
submission: https://github.com/getumbrel/umbrel-apps/pull/1020
dependencies: []
repo: https://github.com/danvergara/morphos
support: https://github.com/danvergara/morphos/issues
port: 3778
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
