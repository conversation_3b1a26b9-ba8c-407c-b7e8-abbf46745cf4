manifestVersion: 1
id: excalidraw
name: Excalidraw
tagline: An open source virtual hand-drawn style whiteboard
category: files
version: "0.18.0"
port: 4422
description: >-
  ✏️ Excalidraw is a free and open-source virtual whiteboard that allows users to create diagrams, sketches, and visual concepts in a hand-drawn style. Designed to simulate the look of informal sketches, it offers a playful yet functional way to visualize ideas, plan projects, and collaborate with others. Unlike traditional diagramming tools that aim for polished, rigid layouts, Excalidraw embraces rough edges and fluid lines, making it ideal for creative thinking, brainstorming sessions, and rapid prototyping.


  At its core, Excalidraw is built for simplicity and ease of use. Users can draw shapes like rectangles, circles, and arrows, add text, and use a freehand tool to sketch ideas. The interface is intuitive and minimalist, allowing even those with no prior design experience to jump in and start drawing immediately. It supports real-time collaboration, enabling multiple people to work together on the same canvas simply by sharing a link. This feature makes it a popular choice for remote teams, educators, and facilitators who need a shared visual space for interaction.


  One of the standout features of Excalidraw is its offline capability. As a progressive web app, it works directly in the browser and saves data locally, so users can keep working even without an internet connection. It also supports exporting drawings in multiple formats, including PNG, SVG, and JSON, which makes it easy to use content created in Excalidraw elsewhere.


  Because it is open source, Excalidraw has gained a strong developer following. The codebase is available on GitHub, allowing contributors to extend its features or integrate it into other platforms. This flexibility has led to its adoption in tools like Obsidian, Visual Studio Code, and various project management environments, where visual collaboration is a valuable asset.


  Overall, Excalidraw stands out as a lightweight, accessible, and community-driven alternative to more complex design and diagramming software. Its focus on clarity, collaboration, and the freedom to draw without friction makes it a beloved tool for thinkers, tinkerers, and teams around the world.
developer: Excalidraw Team
website: https://excalidraw.com/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/1538
repo: https://github.com/ozencb/excalidraw-persist
support: https://github.com/ozencb/excalidraw-persist/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: ""
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
