manifestVersion: 1
id: linkstack
name: LinkStack
tagline: A highly customizable link sharing platform
category: files
version: "4.2.3"
port: 8190
description: >-
  LinkStack is a unique platform that provides an efficient solution for managing and sharing links online. Our platform offers a website similar to Linktree, which allows users to overcome the limitation of only being able to add one link on social media platforms.


  With LinkStack, users can easily link to their own custom page and provide their followers with access to all the links they need in one convenient location. What sets LinkStack apart from other link management platforms is its flexibility, which allows users to host their links on their own web server or web hosting provider. This provides users with complete control over their online presence and ensures that their links are easily accessible.


  Additionally, LinkStack allows other users to register and create their own links, making it an ideal solution for businesses and organizations looking to manage multiple links. With our user-friendly Admin Panel, managing and accessing other users links is easy.


  ☁️ You will need to expose the Linkstack app to the web in order to share your Linkstack webpage with others. The easiest way to do this is to use the 'Cloudflare Tunnel' app from the Umbrel App Store, and expose the Linkstack app using your own domain.
  
  
  Why choose LinkStack?


  💡 Feature rich: LinkStack offers more features than any other comparable solution out there.


  ⚙️ Easy to set up: LinkStack is user-friendly and easier to set up compared to similar web applications.


  👐 Open Source: LinkStack is free to use, distribute, and modify. The source code is available on GitHub (https://github.com/LinkStackOrg).
developer: Julian Prieber
website: https://linkstack.org/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/1537
repo: https://github.com/LinkStackOrg/LinkStack
support: https://github.com/LinkStackOrg/LinkStack/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
releaseNotes: ""
dependencies: []
path: ""
