version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: linkstack_linkstack_1
      APP_PORT: 80
      PROXY_AUTH_ADD: "false"
      
  linkstack:
    hostname: 'linkstack'
    # This image has no version specific tags
    image: 'linkstackorg/linkstack:latest@sha256:abd691b4293b020a317de8794737671e0315159efcb868e8a4124d6f0611f7ae'
    restart: on-failure
    # This image doesn't support a custom user. It must be apache (100:101)
    # user: '1000:1000'
    volumes:
      - linkstack_data:/htdocs

# https://docs.linkstack.org/docker/setup/#docker-bind-mounts
# This gets around the requirement to download the latest release of linkstack and place it in the bind mounted directory in order to use a bind mount. 
volumes:
  linkstack_data:
    driver: local
    driver_opts:
      o: bind
      type: none
      device: ${APP_DATA_DIR}/data
