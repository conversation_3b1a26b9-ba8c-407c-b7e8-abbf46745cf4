manifestVersion: 1
id: chatbot-ui
category: ai
name: ChatBot UI
version: "2023.04.20"
tagline: ChatGPT but better
description: >-
  ChatBot UI is an advanced chatbot kit for OpenAI's chat models aiming to mimic ChatGPT's interface and functionality.


  Simply add your OpenAI API key and start chatting!


  This version of ChatBot UI supports both GPT-3.5 and GPT-4 models. Conversations are stored locally within your browser.
  You can export and import conversations to safeguard against data loss.
developer: <PERSON><PERSON><PERSON> Wrigley
website: https://github.com/mckaywrigley/chatbot-ui
dependencies: []
repo: https://github.com/mckaywrigley/chatbot-ui
support: https://github.com/mckaywrigley/chatbot-ui/issues
port: 10101
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  New Features:


  Added support for Azure OpenAI, with temperature now available as a parameter. The chat window has been improved with a sticky Model Name Bar at the top and enhanced mobile layout, along with a blinking typing cursor.


  Enhancements:


  A major refactor has been carried out for better code maintainability alongside an improved hotkey support for plugins. README now better documents various settings. Enhancements were made to existing translations, and new ones have been added, encompassing Romanian, Polish, Catalan, Bangla, Turkish, and Finnish languages. The app now sorts folders alphabetically and features an updated demo image, as well as performance improvements.


  Bug Fixes:


  Several Google plugin issues have been fixed, along with issues related to hiding API Key and Plugin Key buttons if set server-side. Other fixes include addressing the copy button on code block, modal scrolling on small devices, search box disappearing in Sidebar component, message input not updating on chat list switch, and site not opening if the imported JSON is invalid. Chat messages disappearing on edit, temperature font color, scroll-down button overlapping chat input, prompt selection, and import issues have all been resolved.


  Others:


  Spelling, brevity, and wording improvements have been made throughout the app, and a Security.md file was created to address vulnerability reporting.


  This version is up-to-date as of commit fa3f6e9 here: github.com/mckaywrigley/chatbot-ui/commit/fa3f6e9
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/472