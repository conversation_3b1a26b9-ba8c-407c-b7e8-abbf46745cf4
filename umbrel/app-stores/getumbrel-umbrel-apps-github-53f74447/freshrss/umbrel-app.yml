manifestVersion: 1
id: freshrss
category: social
name: FreshRSS
version: "1.26.3"
tagline: A free, self-hostable aggregator for rss feeds
description: >-
  FreshRSS is an RSS aggregator and reader. It enables you to seamlessly read and follow content from multiple websites at
  a glance without the need to browse from one website to another.


  FreshRSS has a lot of features including:
  
  - RSS and Atom aggregation
  
  - Mark article as favorite if you liked it or if you want to read it later
  
  - Filter and search functionality helps to easily find articles
  
  - Statistics to show you the publishing frequency all the websites you follow
  
  - Import/export of your feeds into OPML format (more information)
  
  - and more!
releaseNotes: >-
  This release includes several bug fixes and improvements:
    - Keep sort and order criteria during navigation
    - Implement loading spinner for marking as favourite/read
    - Fix SQL request for user labels with custom sort
    - Fix regression for favicon in GReader and Fever APIs
    - Fix newest articles (within last second) not shown
    - Fix important articles on reader view
    - Fix remove last share method
    - Fix API handling of default category
    - Fix user self-deletion
    - Improve Indonesian and Polish translations


  Full release notes are available at https://github.com/FreshRSS/FreshRSS/releases.
developer: FreshRSS Org
website: https://freshrss.org/
dependencies: []
repo: https://github.com/FreshRSS/FreshRSS
support: https://github.com/FreshRSS/FreshRSS/issues
port: 3432
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: false
torOnly: false
submitter: Pranshu Agrawal
submission: https://github.com/getumbrel/umbrel-apps/pull/700
