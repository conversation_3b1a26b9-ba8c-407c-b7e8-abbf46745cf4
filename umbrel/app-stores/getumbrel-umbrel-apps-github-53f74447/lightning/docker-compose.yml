version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: $APP_LIGHTNING_IP
      APP_PORT: 3006

  app:
    image: getumbrel/umbrel-lightning:v1.2.0@sha256:4effadadce390959a742d806273d42a20da08280e4f409e9a30658fbd8400854
    restart: on-failure
    volumes:
      - "${APP_LIGHTNING_NODE_DATA_DIR}:/data/.lnd"
      - "${APP_DATA_DIR}/data/lightning:/data"
    environment:
      PORT: "3006"
      TOR_PROXY_IP: "${TOR_PROXY_IP}"
      TOR_PROXY_PORT: "${TOR_PROXY_PORT}"
      BITCOIN_HOST: "${APP_BITCOIN_NODE_IP}"
      RPC_PORT: "${APP_BITCOIN_RPC_PORT}"
      RPC_USER: "${APP_BITCOIN_RPC_USER}"
      RPC_PASSWORD: "${APP_BITCOIN_RPC_PASS}"
      LND_NETWORK: "${APP_BITCOIN_NETWORK}"
      LND_HOST: "${APP_LIGHTNING_NODE_IP}"
      LND_REST_HIDDEN_SERVICE: "${APP_LIGHTNING_REST_HIDDEN_SERVICE}"
      LND_GRPC_HIDDEN_SERVICE: "${APP_LIGHTNING_GRPC_HIDDEN_SERVICE}"
      TLS_FILE: "/data/.lnd/tls.cert"
      MACAROON_DIR: "/data/.lnd/data/chain/bitcoin/${APP_BITCOIN_NETWORK}/"
      CHANNEL_BACKUP_FILE: "/data/.lnd/data/chain/bitcoin/${APP_BITCOIN_NETWORK}/channel.backup"
      BACKUP_STATUS_FILE: "/statuses/backup-status.json"
      JSON_STORE_FILE: "/data/state.json"
      JSON_SETTINGS_FILE: "/data/settings.json"
      UMBREL_LND_CONF_FILEPATH: "/data/.lnd/umbrel-lnd.conf"
      LND_CONF_FILEPATH: "/data/.lnd/lnd.conf"
      LND_INITIALIZE_WITH_TOR_ONLY: "${LND_INITIALIZE_WITH_TOR_ONLY:-unset}"
      DEVICE_DOMAIN_NAME: "${DEVICE_DOMAIN_NAME}"
      EXPLORER_PORT: "${APP_MEMPOOL_PORT}"
      EXPLORER_HIDDEN_SERVICE: "${APP_MEMPOOL_HIDDEN_SERVICE}"
    networks:
      default:
        ipv4_address: $APP_LIGHTNING_IP

  lnd:
    hostname: "${DEVICE_DOMAIN_NAME}" # Needed so LND can generate a valid cert
    image: lightninglabs/lnd:v0.19.1-beta@sha256:00143ec1b19ee8be186948e75517a4111bcba8cfc6bf2c4a89732fa4efd998a5
    command: "${APP_LIGHTNING_COMMAND}"
    user: 1000:1000
    restart: unless-stopped
    ports:
      - "$APP_LIGHTNING_NODE_PORT:$APP_LIGHTNING_NODE_PORT"
      - "$APP_LIGHTNING_NODE_REST_PORT:$APP_LIGHTNING_NODE_REST_PORT"
      - "$APP_LIGHTNING_NODE_GRPC_PORT:$APP_LIGHTNING_NODE_GRPC_PORT"
    volumes:
      - "${APP_LIGHTNING_NODE_DATA_DIR}:/data/.lnd"
    environment:
      HOME: /data
    networks:
      default:
        ipv4_address: $APP_LIGHTNING_NODE_IP
  
  tor:
    image: getumbrel/tor:*******@sha256:2ace83f22501f58857fa9b403009f595137fa2e7986c4fda79d82a8119072b6a
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/torrc:/etc/tor/torrc:ro
      - ${TOR_DATA_DIR}:/data
    environment:
      HOME: "/tmp"
