manifestVersion: 1.1
id: lightning
category: bitcoin
name: Lightning Node
version: "0.19.1-beta"
tagline: Run your personal Lightning Network node
description: >-
  Run your personal Lightning Network node, and join the future of Bitcoin today.


  The Lightning Network allows ultra cheap and almost instant Bitcoin transactions. By running a Lightning node, you can not only self-custody your Bitcoin on Lightning, but also earn sats by routing payments on the network.


  Connect Zeus Wallet, Zap, or any other wallet that supports lndconnect to remotely manage and access your node from anywhere.


  With the Advanced Settings feature, you can take control of your node and customize it to your needs. Personalize it with a name, enable larger channels, limit channel sizes, set your routing fees, optimize your routing strategy, add watchtower services, fine-tune its performance, and much more.


  Powered by LND.


  An official app from Umbrel.
releaseNotes: >-
  This release updates the underlying Lightning Network Daemon (LND) that powers this app to v0.19.1-beta. The update includes new feature additions, important bug fixes, stability enhancements, and performance improvements for LND.
  In addition, Testnet4 support has been added.


  🚨 There are no breaking changes to the Umbrel app itself; however, if you are a power user who uses automated scripts to interact with your node via the command line,
  there are breaking changes to the "lncli listchannels" and "lncli closedchannels" outputs that you should consult the release notes for.


  Full release notes can be found at https://github.com/lightningnetwork/lnd/releases
developer: Umbrel
website: https://umbrel.com
dependencies:
  - bitcoin
repo: https://github.com/getumbrel/umbrel-lightning
support: https://community.getumbrel.com/c/bitcoin-and-lightning
port: 2101
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
path: ""
defaultPassword: ""
widgets:
  - id: "bitcoin-wallet"
    type: "text-with-buttons"
    refresh: "5s"
    endpoint: "app:3006/v1/lnd/widgets/bitcoin-wallet"
    link: ""
    example:
      type: "text-with-buttons"
      link: ""
      title: "Bitcoin Wallet"
      text: "1,845,894"
      subtext: "sats"
      buttons:
        - text: "Withdraw"
          icon: "arrow-up-right"
          link: "?action=send-bitcoin"
        - text: "Deposit"
          icon: "arrow-down-right"
          link: "?action=receive-bitcoin"
  - id: "lightning-wallet"
    type: "text-with-buttons"
    refresh: "2s"
    endpoint: "app:3006/v1/lnd/widgets/lightning-wallet"
    link: ""
    example:
      type: "text-with-buttons"
      link: ""
      title: "Lightning Wallet"
      text: "762,248"
      subtext: "sats"
      buttons:
        - text: "Send"
          icon: "arrow-up-right"
          link: "?action=send-lightning"
        - text: "Receive"
          icon: "arrow-down-right"
          link: "?action=receive-lightning"
  - id: "lightning-stats"
    type: "four-stats"
    refresh: "5s"
    endpoint: "app:3006/v1/lnd/widgets/lightning-stats"
    link: ""
    example:
      type: "four-stats"
      link: ""
      items:
        - title: "Connections"
          text: "5"
          subtext: "peers"
        - title: "Active channels"
          text: "3"
          subtext: "channels"
        - title: "Max send"
          text: "90K"
          subtext: "sats"
        - title: "Max receive"
          text: "45K"
          subtext: "sats"
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/commit/576ecd2bef8d625abceed0f67ec9c487da9b2b1b
