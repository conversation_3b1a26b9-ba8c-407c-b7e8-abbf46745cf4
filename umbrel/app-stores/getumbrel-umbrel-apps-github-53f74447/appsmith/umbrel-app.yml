manifestVersion: 1
id: appsmith
name: A<PERSON><PERSON>
tagline: Platform to build admin panels, internal tools, and dashboards
category: developer
version: "v1.79"
port: 8654
description: >-
  Appsmith is an open-source, low-code development platform designed to help developers and teams build internal tools and business applications quickly and efficiently. Whether you need dashboards, admin panels, database GUIs, or approval workflows, Appsmith provides a powerful yet flexible environment to create robust applications without the overhead of traditional software development.


  At its core, Appsmith offers a drag-and-drop interface featuring over 45 customizable widgets, seamless integration with more than 18 data sources—including PostgreSQL, MongoDB, REST APIs, and GraphQL—and a built-in JavaScript editor for advanced logic and interactivity. This combination empowers both developers and non-technical users to build dynamic, data-driven applications with ease.


  Appsmith supports Git-based version control, allowing teams to manage application changes effectively. Its architecture embraces a model-view-controller (MVC) pattern, promoting clean code organization and scalability. Additionally, Appsmith offers both cloud-hosted and self-hosted deployment options, providing flexibility and control over your applications.


  Trusted by over 10,000 teams across 150 countries—including organizations like Dropbox, Twilio, and GSK—Appsmith is a proven solution for building internal tools that enhance productivity and streamline operations.


  Whether you're a startup looking to accelerate development or an enterprise seeking customizable internal solutions, <PERSON><PERSON><PERSON> offers the tools and flexibility to meet your needs.
developer: Appsmith
website: https://www.appsmith.com/
submitter: al-lac
submission: https://github.com/getumbrel/umbrel-apps/pull/2634
repo: https://github.com/appsmithorg/appsmith
support: https://discord.com/invite/rBTTVJp
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
defaultUsername: ""
defaultPassword: ""
dependencies: []
releaseNotes: ""
path: ""
