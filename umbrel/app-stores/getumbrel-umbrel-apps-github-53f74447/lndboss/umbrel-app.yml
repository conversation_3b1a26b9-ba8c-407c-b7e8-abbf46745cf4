manifestVersion: 1
id: lndboss
category: bitcoin
name: LndBoss
version: "2.19.1"
tagline: A GUI for BalanceOfSatoshis
description: >-
  ⚠️ Deprecation notice: This app has been deprecated and therefore will no longer receive any app updates.

  
  LndBoss is a GUI for BalanceOfSatoshis.
  It is a tool that makes it easy to run your favorite
  bos commands and helps manage your lightning node.
  You can schedule jobs to automatically rebalance channels,
  auto update fees based on policies, open balanced/group channels with other nodes,
  integration with amboss to post updates and much more.
developer: <PERSON><PERSON><PERSON>
website: https://github.com/niteshbalusu11
dependencies:
  - lightning
repo: https://github.com/niteshbalusu11/lndboss
support: https://t.me/lndboss
port: 8055
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >
  - 2.19.1 (2023-06-23)
    Fixed issues where App router is failing in nextjs 13.
    Fixed issues where batch open was failing due to breaking change in dependency.

  - 2.18.1 (2023-04-10)
    Bump bos dependency that fixes a bug in send command when keysending to unannounced nodes.
    Migrate to nextjs13

  - 2.18.0 (2023-03-10)
    Added options to call command: getChainAddresses, getBlock, verifyChainAddressMessage, signChainAddressMessage

submitter: Nitesh Balusu
submission: https://github.com/getumbrel/umbrel-apps/pull/65
disabled: true