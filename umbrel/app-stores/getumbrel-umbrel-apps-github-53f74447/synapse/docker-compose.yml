version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: synapse_server_1
      APP_PORT: $APP_SYNAPSE_PORT
      PROXY_AUTH_ADD: "false"

  server:
    image: matrixdotorg/synapse:v1.132.0@sha256:3036ec25dfb5fcc5120942465788c1e1f2bb3671e28b04d7b3a1db4400ac84f4
    restart: on-failure
    stop_grace_period: 1m
    entrypoint: "bash"
    command: "-c './start.py generate && ./start.py migrate_config && echo -e \"\nenable_registration_without_verification: true\" >> /data/homeserver.yaml && exec ./start.py'"
    volumes:
      - ${APP_DATA_DIR}/data/synapse:/data
    environment:
      UID: "1000"
      GID: "1000"
      SYNAPSE_HTTP_PORT: "${APP_SYNAPSE_PORT}"
      SYNAPSE_SERVER_NAME: "${APP_HIDDEN_SERVICE}"
      SYNAPSE_REPORT_STATS: "yes"
      SYNAPSE_ENABLE_REGISTRATION: "yes"
      SYNAPSE_NO_TLS: "yes"
