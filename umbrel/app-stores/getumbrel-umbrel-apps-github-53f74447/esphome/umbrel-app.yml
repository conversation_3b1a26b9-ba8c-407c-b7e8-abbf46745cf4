manifestVersion: 1
id: esphome
category: automation
name: ESPHome
version: "2025.6.2"
tagline: Intelligently manage all your ESP8266/ESP32 devices
description: >-
  ESPHome is a system to control your ESP8266/ESP32 by simple yet powerful configuration files and control them remotely through Home Automation systems.
developer: ESPHome
website: https://esphome.io
dependencies: []
repo: https://github.com/esphome/esphome
support: https://community.home-assistant.io/c/esphome
port: 6052
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: false
releaseNotes: >-
  This release includes several bug fixes and improvements to enhance stability and performance.


  Key updates in this release include:
    - Fixed issues with LVGL qrcode and audio handling
    - Improved timeout handling for audio components
    - Enhanced voice assistant support for streaming TTS responses
    - Changed default MDNS query settings for ESP32
    - Fixed pin interrupts for MCP23XXX devices
    - Disabled I2C scan on certain IDF versions


  Full release notes can be found at https://github.com/esphome/esphome/releases
submitter: ShonP40
submission: https://github.com/getumbrel/umbrel-apps/pull/43
