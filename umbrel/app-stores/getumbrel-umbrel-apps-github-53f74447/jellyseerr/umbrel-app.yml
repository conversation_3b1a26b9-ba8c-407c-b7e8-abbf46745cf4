manifestVersion: 1
id: jellyseerr
category: media
name: Jellyseerr
version: "2.7.0"
tagline: Beautiful media discovery for Jellyfin users
description: >-
  Jellyseerr is a request management and media discovery tool built to work with your existing Jellyfin ecosystem.

  Jellyseerr scans your Jellyfin libraries at regular intervals, so it knows which items are already available on your server.
  It also integrates with the popular DVR applications Radarr and Sonarr, and supports activity monitoring within Jellyseerr itself.

  🛠️ SET-UP INSTRUCTIONS

  During initial set-up, you will need to input your Umbrel device's IP address to connect to Jellyfin (and optional services such as Radarr and Sonarr).
  You can find your device's IP address by visiting your router's admin dashboard or by using an IP scanning tool like Angry IP Scanner.
developer: Fallenbagel
website: https://github.com/Fallenbagel/jellyseerr
dependencies: []
repo: https://github.com/Fallenbagel/jellyseerr
support: https://github.com/Fallenbagel/jellyseerr/issues
port: 5056
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  Key highlights in this release:

    - Added a setting to force IPv4 resolution
    - Resolved issues with blacklisted items in MediaSliders
    - Fixed proxy settings for axios
    - Corrected issues with the 'Request' button

  Full release notes are available at https://github.com/fallenbagel/jellyseerr/releases
torOnly: false
submitter: johnpc
submission: "https://github.com/getumbrel/umbrel-apps/pull/924"
