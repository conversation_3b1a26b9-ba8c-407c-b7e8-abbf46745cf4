manifestVersion: 1
id: jackett
category: media
name: Jackett
version: "0.22.2101"
tagline: API support for your favorite torrent trackers
description: >-
  <PERSON><PERSON> works as a proxy server: it translates queries from apps (Sonarr, Radarr, SickRage, CouchPotato, Mylar3, Lidarr, DuckieTV, qBittorrent, Nefarious, etc) into tracker-site-specific http queries,
  parses the html or json response, and then sends results back to the requesting software. This allows for getting recent uploads (like RSS) and performing searches.
  Jackett is a single repository of maintained indexer scraping & translation logic - removing the burden from other apps.


  🛠️ SET-UP INSTRUCTIONS


  1. Base URL Configuration: In <PERSON><PERSON>, fill in the "Base URL override" with "http://[the-ip-of-your-umbrel]:9117" without the quotes and replace with the IP of your Umbrel device.
  You can find your device's IP address by visiting your router's admin dashboard or by using an IP scanning tool like Angry IP Scanner.
  This ensures that functionalities like copying a Torznab feed URL to your clipboard work correctly for integration with apps like Sonarr and Radarr.


  2. Blackhole Directory: If you want to download .torrent files directly from <PERSON><PERSON> and have a torrent client watch this folder, set the "Blackhole directory" in <PERSON><PERSON> to "/downloads" without the quotes. This will allow you to download .torrent files from <PERSON><PERSON> to your Umbrel's downloads folder.


  3. App Integration: Follow the instructions within Jackett to integrate Jackett as an indexer source for apps like Sonarr and Radarr.
developer: Jackett
website: https://github.com/Jackett/Jackett/
dependencies: []
repo: https://github.com/Jackett/Jackett/
support: https://github.com/Jackett/Jackett/
port: 9117
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
releaseNotes: >-
  This release includes various improvements and updates:
    - Fixed issue with expired certificate for peeratiko and frozenlayer indexers
    - Improved Sonarr compatibility and added support for new releases from Anilibria
    - Minor code cleanup, improved API docs, and ensured unique release IDs
    - Updated `rudub.yml`


  Full release notes are found at https://github.com/Jackett/Jackett/releases
permissions:
  - STORAGE_DOWNLOADS
torOnly: false
submitter: Pranshu Agrawal
submission: "https://github.com/getumbrel/umbrel-apps/pull/665"
