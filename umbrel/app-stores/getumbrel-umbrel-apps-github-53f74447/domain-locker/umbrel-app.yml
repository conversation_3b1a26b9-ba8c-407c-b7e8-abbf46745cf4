manifestVersion: 1
id: domain-locker
name: Domain Locker
tagline: An all-in-one tool for keeping track of your domain name portfolio
category: networking
version: "0.0.7"
port: 8839
description: >-
  Domain Locker is a powerful, open-source platform designed for managing and monitoring domain names across multiple registrars and services. It provides users with a unified interface to organize, track, and safeguard their domain assets with a strong emphasis on automation, security, and data privacy. The tool automatically collects comprehensive information about each domain, including DNS records, SSL certificates, WHOIS data, subdomains, and related IP addresses. This real-time asset tracking ensures users are always aware of the current state and health of their domains.


  Integrated alerting mechanisms notify users about critical changes such as impending domain expirations, DNS modifications, and potential security issues. These alerts can be delivered via email, webhooks, or messaging platforms like Telegram and Signal, helping users respond quickly to emerging threats or operational changes. Domain Locker also features detailed analytics and visualizations, including interactive charts and timelines that highlight domain performance and configuration history over time. This level of insight makes it easier for users to optimize domain usage, identify anomalies, and make informed decisions.


  For those concerned with security, Domain Locker includes built-in tools to audit domain configurations and suggest improvements, making it ideal for developers, businesses, and IT professionals who want to ensure robust domain hygiene. Additionally, it offers cost tracking capabilities, enabling users to record purchase prices, monitor ongoing expenses, and estimate the current market value of their domain portfolios.


  The platform supports internationalization, a customizable dark mode interface, and flexible layout options to suit different user preferences. 
developer: <PERSON>
website: https://domain-locker.com/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2633
repo: https://github.com/Lissy93/domain-locker
support: https://github.com/Lissy93/domain-locker/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
releaseNotes: ""
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
