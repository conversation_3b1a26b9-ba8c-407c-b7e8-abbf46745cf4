manifestVersion: 1
id: affine
name: Affine
tagline: Open source alternative to Notion, Miro, and Airtable
category: files
version: "0.22.4"
port: 3013
description: >-
  A privacy-focused, local-first, open-source, and ready-to-use alternative for Notion & Miro.
  One hyper-fused platform for wildly creative minds.


  To Shape, not to adapt. Tools can impact your lifestyle. AFFiNE is built for individual & teams who care their data, who refuse vendor lock-in, and who want to have control over their essential tools.


  🛠️ SET-UP INSTRUCTIONS

  In order to save your data, you need to sign in to your self-hosted Affine instance:


  1. When you first open Affine, it will walk you through setting up an admin account.

  2. After setting up your admin account, close the browser window and open up the Affine app again from the umbrelOS homescreen.

  3. In the Affine app select the Demo Workspace from the sidebar and then select "Create cloud workspace"

  4. Follow the steps to create your self-hosted cloud workspace. You are now ready to use Affine with your self-hosted instance!
developer: toeverything
website: https://affine.pro
submitter: Jasper
submission: https://github.com/getumbrel/umbrel-apps/pull/990
repo: https://github.com/toeverything/AFFiNE
support: https://docs.affine.pro
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  With enhanced AI capabilities, more integrated features, and extra optimizations, AFFiNE becomes an even more valuable part of your workflow. Plus, this release brings important user experience improvements and adds some of the most requested features


  Some of the key highlights in this release include:
    - AI workspace embedding
    - AI search & reason
    - New all doc & collection
    - Embed doc with alias
    - Calendar integrations
    - Database
    - Clipboard blob & loading block
    - Android Test

  Full release notes are found at https://github.com/toeverything/AFFiNE/releases
dependencies: []
path: ""
defaultUsername: ""
