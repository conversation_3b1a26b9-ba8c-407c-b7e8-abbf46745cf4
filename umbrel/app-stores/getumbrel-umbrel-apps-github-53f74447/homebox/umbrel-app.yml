manifestVersion: 1
id: homebox
name: HomeBox
tagline: An inventory and organization system built for the home user
category: files
version: "0.20.1"
port: 7745
description: >-
  Homebox is an inventory and organization system built for the home user! With a focus on simplicity and ease of use, Homebox is the perfect solution for your home inventory, organization, and management needs. 
  

  Homebox is currently in beta and undergoing active development, meaning that it is not yet feature complete and may contain bugs.
developer: SysAdmins Media
website: https://homebox.software
repo: https://github.com/sysadminsmedia/homebox
support: https://github.com/sysadminsmedia/homebox/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
dependencies: []
path: ""
torOnly: false
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  ⚠️ It is highly recommend to keep backups of your database and stored attachments for this update.


  Key improvements and changes:
    - New attachment storage system for better file deduplication
    - Updated password hashing to Argon2id
    - Added support for thumbnails (existing attachments need manual update)
    - Improved UI with custom colored labels
    - Enhanced table sorting and pagination
    - Various bug fixes and performance improvements


  Full release notes can be found at https://github.com/sysadminsmedia/homebox/releases
submitter: Xosten
submission: https://github.com/getumbrel/umbrel-apps/pull/501
