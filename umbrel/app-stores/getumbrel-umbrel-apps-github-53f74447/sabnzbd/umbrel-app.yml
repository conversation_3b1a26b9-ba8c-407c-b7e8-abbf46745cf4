manifestVersion: 1.1
id: sabnzbd
category: networking
name: SABnzbd
version: "4.5.1"
tagline: The automated Usenet download tool
description: >-
  SABnzbd makes Usenet as simple and streamlined as possible by automating everything we can. All you have to do is add an .nzb.
  SABnzbd takes over from there, where it will be automatically downloaded, verified, repaired, extracted and filed away with zero human interaction. 
  SABnzbd offers an easy setup wizard and has self-analysis tools to verify your setup.


  If you want to know more you can head over to our website: https://sabnzbd.org.


  🛠️ SET-UP INSTRUCTIONS


  SABnzbd on umbrelOS is set up to work without any additional configuration needed. It will automatically be connected to dependent apps like Radarr, Sonarr, Lidarr, Readarr, and Prowlarr. Simply install additional media apps from the Umbrel App Store, and everything will work seamlessly together.


  If you want to modify the default categories that are set up on install, you can do so by navigating to the Categories section in the SABnzbd settings tab.
  If you do this, you will need to update the categories in your other apps like Sonarr and Radarr to match the ones you set up in SABnzbd in order for them to work together.

  You can also set up API keys for integration with other apps like Sonarr and Radarr by navigating to the General section in the SABnzbd app.
developer: sabnzbd
website: https://sabnzbd.org/
dependencies: []
repo: https://github.com/sabnzbd/sabnzbd
support: https://forums.sabnzbd.org/
port: 9876
releaseNotes: >-
  This release includes bug fixes and improvements:
    - Corrected platform detection on Linux
    - Fixed issues with 'From SxxEyy' RSS filters


  Highlights from version 4.5.0:
    - Improved failure detection by downloading additional par2 files right away
    - Added more diagnostic information about the system
    - Added Turkish translation
    - Added option to supply custom Unrar parameters
    - Improved handling of filenames that exceed maximum filesystem lengths
    - Directly decompress gzip responses when retrieving NZB's


  Full release notes are available at https://github.com/sabnzbd/sabnzbd/releases
permissions:
  - STORAGE_DOWNLOADS
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: false
submitter: Parth Jadhav
submission: https://github.com/getumbrel/umbrel-apps/pull/810
