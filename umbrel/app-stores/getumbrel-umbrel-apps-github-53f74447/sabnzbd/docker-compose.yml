version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: sabnzbd_web_1
      APP_PORT: 8080
      PROXY_AUTH_WHITELIST: "/api*"

  web:
    image: linuxserver/sabnzbd:4.5.1@sha256:dc87d24f47257c738394cd2d82b547c0482b0b5e5824f483d42a8699ad5793de
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data/config:/config
      - ${UMBREL_ROOT}/data/storage/downloads:/downloads
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Etc/UTC
