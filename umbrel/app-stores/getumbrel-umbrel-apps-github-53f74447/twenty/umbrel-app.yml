manifestVersion: 1.1
id: twenty
name: Twenty
tagline: An adaptable CRM solution for seamless team collaboration and growth
category: files
version: "1.0.1"
port: 2020
description: >-
  Twenty is the leading open-source CRM, built by a community of hundreds of contributors to meet the unique needs of your business. Designed for flexibility, it helps businesses of all sizes efficiently manage customer relationships and streamline workflows.


  With Twenty, you can easily manage customer data through its contact management system. You can create custom objects and fields to capture information specific to your business operations, ensuring that your CRM is tailored to your needs. The platform offers both Kanban and Table Views, allowing you to visualize your workflow and manage projects in a way that works best for your team.


  It also features pipeline visualization, giving you a clear overview of your processes and helping you track your sales funnel. Email integration is another valuable feature, allowing you to view emails related to a specific customer or company directly within your workspace. This integration ensures that your team can collaborate efficiently without leaving the platform.


  Notes and tasks can be easily added to each record, enabling your team to document key information and track customer interactions. For those needing more advanced automation, Twenty offers API and Webhook support, making it easy to connect to other applications and automate workflows.


  Benefits of using Twenty include improved organization, streamlined communication, better data management, and enhanced collaboration within your team. The flexibility and customization options also ensure that Twenty can adapt as your business grows and changes.


  ⚠️ The Twenty CRM app does not come with preconfigured email or calendar providers. To use services like Gmail or Microsoft Outlook, you need to create your own OAuth credentials and add them in the Settings under **Other / Admin Panel / Config Variables**. Providers will only appear in the interface once valid credentials are set.


  You can find detailed setup instructions here: https://twenty.com/developers/section/self-hosting/setup#for-gmail-and-google-calendar
developer: Twenty.com PBC
website: https://twenty.com/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2484
repo: https://github.com/twentyhq/twenty
support: https://github.com/twentyhq/twenty/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
releaseNotes: >-
  This release includes multiple new features, improvements and bug fixes:
    - Fixed side panel, record picker, pagination, and dropdown UI issues
    - Improved permissions handling, including role creation, API access, and V2 activation
    - Enhanced workflow UX with visual improvements, manual triggers, and seed data
    - Added new features like AI Agent node, HTTP request node, short number formatting, and metrics logging
    - Improved import handling with upsert support, duplicate checks, and better error messages
    - Boosted performance in metadata sync, queries, and record table rendering
    - Enhanced onboarding, captcha feedback, and error telemetry
    - Fixed various bugs in auth, SSO, calendar, and filters
    - Continued i18n translation updates and CI/test improvements


  For a complete list of changes, please visit https://github.com/twentyhq/twenty/releases
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
