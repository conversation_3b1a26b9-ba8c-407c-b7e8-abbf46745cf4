manifestVersion: 1
id: matter-server
name: Matter Server
tagline: A Matter Controller Server over WebSockets
category: automation
version: "8.0.0"
port: 5580
description: >-
  🛜 This project implements a Matter Controller Server over WebSockets using the official Matter (formerly CHIP) SDK as a base and provides both a server and client implementation.


  🏠 The goal of this project is primarily to have Matter support in Home Assistant but its universal approach makes it suitable to be used in other projects too.
developer: Home Assistant
website: https://home-assistant.io
repo: https://github.com/home-assistant-libs/python-matter-server
submitter: al-lac
submission: https://github.com/getumbrel/umbrel-apps/pull/1866
support: https://community.home-assistant.io/
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  This release includes several improvements and bug fixes.


  Key updates:
    - Improved Matter DNS-SD service parsing
    - Added Mounted device types (1.4)
    - Removed unnecessary libgirepository dependency
    - Enhanced multicasts for service discovery
    - Added custom cluster for Inovelli VTM31-SN Dimmer
    - Improved support for local updates with the same version string


  Full release notes are available at https://github.com/home-assistant-libs/python-matter-server/releases
dependencies: []
path: ""
