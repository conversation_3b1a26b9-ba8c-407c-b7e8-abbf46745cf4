manifestVersion: 1
id: trilium-notes
category: files
name: Trilium Notes
version: "v0.95.0"
tagline: Build your personal knowledge base with Trilium Notes
description: >-
  Features


  - Notes can be arranged into arbitrarily deep tree. Single note can be placed into multiple places in the tree (see cloning)

  - Rich WYSIWYG note editing including e.g. tables, images and math with markdown autoformat

  - Support for editing notes with source code, including syntax highlighting

  - Fast and easy navigation between notes, full text search and note hoisting

  - Seamless note versioning

  - Note attributes can be used for note organization, querying and advanced scripting

  - Synchronization with self-hosted sync server (there's a 3rd party service for hosting synchronisation server)

  - Sharing (publishing) notes to public internet

  - Strong note encryption with per-note granularity

  - Sketching diagrams with bult-in Excalidraw (note type "canvas")

  - Relation maps and link maps for visualizing notes and their relations

  - Scripting - see Advanced showcases

  - REST API for automation

  - Scales well in both usability and performance upwards of 100 000 notes

  - Touch optimized mobile frontend for smartphones and tablets

  - Night theme

  - Evernote and Markdown import & export

  - Web Clipper for easy saving of web content
developer: TriliumNext
website: https://github.com/TriliumNext
dependencies: []
repo: https://github.com/TriliumNext/Trilium
support: https://github.com/TriliumNext/Trilium/wiki
port: 3779
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
path: /#root
defaultUsername: ""
defaultPassword: ""
torOnly: false
submitter: Pranshu Agrawal
submission: https://github.com/getumbrel/umbrel-apps/pull/199
releaseNotes: >-
  ⚠️ This release brings a big version jump with lots of new features and improvements. Consider taking a backup before upgrading.


  Key Highlights:
    - New default theme "Trilium.Rocks" with refreshed UI and icons
    - Full TypeScript rewrite of the codebase for better stability and maintainability
    - Built-in syntax highlighting for code blocks and math rendering support (LaTeX-style)
    - Enhanced calendar with start/end times, week/year/list views
    - Zen Mode improvements and keyboard shortcut for formatting (F9)
    - Mermaid diagrams and mind maps can now be exported as PNG
    - Markdown export improvements (better formatting, HTML tables support)
    - Improved mobile and tablet layout
    - Numerous bug fixes and dependency upgrades


  Full release notes are available at https://github.com/TriliumNext/Trilium/releases
