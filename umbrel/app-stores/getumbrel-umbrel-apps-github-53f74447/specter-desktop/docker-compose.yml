version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: specter-desktop_web_1
      APP_PORT: 25441

  web:
    image: lncm/specter-desktop:v2.1.1@sha256:cc3c718086efa4a906e0d6178e14288484cdf69c48d29becb58b8efd8524c5ef
    stop_signal: SIGINT
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data:/data
    command:
      - --host=0.0.0.0
      - --specter-data-folder=/data
    environment:
      # TODO: APP_PASSWORD
      # These env vars no longer work from v2.0.0 onwards (after introduction of public electrum server option). Keeping them here for potential future use if <PERSON><PERSON><PERSON> Deskt<PERSON> adds support for them again. 
      BTC_RPC_USER: $APP_BITCOIN_RPC_USER
      BTC_RPC_PASSWORD: $APP_BITCOIN_RPC_PASS
      BTC_RPC_HOST: $APP_BITCOIN_NODE_IP
      BTC_RPC_PORT: $APP_BITCOIN_RPC_PORT
      BTC_RPC_PROTOCOL: http