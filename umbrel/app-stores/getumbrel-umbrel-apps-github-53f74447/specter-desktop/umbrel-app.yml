manifestVersion: 1
id: specter-desktop
category: bitcoin
name: <PERSON><PERSON><PERSON> Desktop
version: "2.1.1"
tagline: Multisig with hardware wallets made easy
description: >-
  Specter Desktop can be used to connect to the Bitcoin Core running on your Umbrel or an Electrum Server.
  It functions as a watch-only coordinator for multi-signature and single-key
  Bitcoin wallets. At the moment <PERSON><PERSON><PERSON> Desktop is working with all major
  hardware wallets including:


  - SeedSigner

  - Specter DIY (optionally airgapped, using QR codes)

  - Blockstream Jade

  - ColdCard (optionally airgapped, using SD card)

  - Passport

  - BitBox02

  - Electrum (optionally airgapped, if running Electrum on an airgapped computer/phone)

  - Keystone (airgapped, using QR codes)

  - Trezor

  - Ledger

  - KeepKey


  Specter Desktop also supports using the Bitcoin Core on your Umbrel as a hot wallet, by importing or generating a random BIP39 mnemonic, but this feature is experimental and we do not recommend using it at this stage. We plan to add support for other hardware wallets as they come up.
developer: Crypto Advance
website: https://specter.solutions
dependencies:
  - bitcoin
repo: https://github.com/cryptoadvance/specter-desktop
support: https://t.me/spectersupport
port: 25441
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  🎉 Specter Desktop is back on the umbrelOS app store and compatible with the latest version of Bitcoin Core!


  Key changes in this release:
    - Specter Desktop is now compatible with Bitcoin Core v28.0
    - Added wallet export to Jade via QR codes
    - Fixed backup zip download issues
    - Various bug fixes and improvements


  Full release notes and detailed information is available at https://github.com/cryptoadvance/specter-desktop/releases
submitter: k9ert
submission: https://github.com/getumbrel/umbrel/pull/339