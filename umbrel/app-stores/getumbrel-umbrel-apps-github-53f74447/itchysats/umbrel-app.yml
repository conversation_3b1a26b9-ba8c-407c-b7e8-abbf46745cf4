manifestVersion: 1.2
id: itchysats
category: bitcoin
name: ItchySats
version: "0.7.0"
tagline: Peer-2-peer derivatives on Bitcoin
description: >-
  ⚠️ Removal Notice: The ItchySats project is no longer maintained and is non-functional. For more information, visit the project's GitHub repository.


  ItchySats enables peer-2-peer CFD trading on Bitcoin using DLCs
  (discreet log contracts). No account needed, no trusted third-party - just you
  and your keys.


  This is beta software. We tested it on test- and mainnet, but there are no guarantees that it will always behave as expected.

  Please be mindful with how much money you trust the application with.

  CFDs trading is inherently risky, be sure to read up on it before using this application.


  That said: This is pretty awesome, go nuts!


  1. Fund the ItchySats wallet

  2. Open a position

  3. Monitor the price movement

  4. Profit


  Limitations of the mainnet beta:


  1. Minimum position quantity is $100, maximum $1000


  With 0.4.0 your CFDs are perpetual positions that are extended hourly. This means your CFD position will remain open forever unless you decide to close it. A funding fee is collected hourly when the CFD is extended.


  With 0.4.8 you can open long and short positions, previously only long positions were possible.


  With 0.5.0 you can chose from different from leverage. Leverage choices are configured by the maker and might initially be restricted to x1, x2 and x3.

  With 0.6.0 you can open positions on BTCUSD and ETHUSD prices. #ShortTheMerge

  With 0.7.0 we replaced basic authentication with cookie-based authentication. Deterministic password is the same as before (see Umbrel app store).
developer: 10101
website: https://itchysats.network
dependencies:
  - electrs
repo: https://github.com/get10101/itchysats
support: https://github.com/get10101/itchysats/issues
port: 7113
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
deterministicPassword: true
torOnly: false
releaseNotes: >-
  With 0.7.0 we replaced basic authentication with cookie-based authentication. Deterministic password is the same as before (see Umbrel app store).

  Full changelog can be found here: https://github.com/itchysats/itchysats/releases/tag/0.7.0
submitter: 10101
submission: https://github.com/getumbrel/umbrel/pull/1149
disabled: true