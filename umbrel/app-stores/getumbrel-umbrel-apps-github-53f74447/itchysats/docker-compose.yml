version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: itchysats_web_1
      APP_PORT: 8000

  web:
    image: ghcr.io/get10101/itchysats/taker:0.7.0@sha256:0c29162897a6c2bc2093373306f920400a0718960e1e4b1d80bd44beb485a498
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data:/data
    command:
      - --password=$APP_PASSWORD
      - --app-seed=$APP_SEED
      - $APP_BITCOIN_NETWORK
      - --electrum=tcp://$APP_ELECTRS_NODE_IP:$APP_ELECTRS_NODE_PORT
    environment:
      - ITCHYSATS_ENV=umbrel
