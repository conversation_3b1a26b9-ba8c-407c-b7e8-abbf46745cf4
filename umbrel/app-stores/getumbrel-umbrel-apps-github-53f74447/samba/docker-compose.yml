version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: samba_homepage_1
      APP_PORT: 80

  homepage:
    image: loficafe/static-web-server-samba:1.0.1@sha256:783667f052d9535bb75946ff9bc807989b041e25437ef10c30b291589d041e6e
    restart: on-failure
    user: "1000:1000"
    volumes:
      - ${APP_DATA_DIR}/data/samba/smb.conf:/public/smb.conf:ro

  server:
    image: dockurr/samba:4.21.6@sha256:3b7ffd8f614deb8e93158a32709a7aea468ee30b7687b162bb2143ed04ac8340
    restart: on-failure
    environment:
      USER: umbrel
      PASS: ${APP_PASSWORD}
      UID: 1000
      GID: 1000
    ports:
      - '446:445'
    volumes:
      - ${APP_DATA_DIR}/data/samba/smb.conf:/etc/samba/smb.conf
      - ${UMBREL_ROOT}/data/storage:/storage
