manifestVersion: 1.1
id: solidtime
name: Solidtime
tagline: A simple, secure, and private time tracking app
category: files
version: "0.8.0"
port: 8770
description: >-
  🕒 Solidtime is a simple, secure, and private time tracking app that helps you keep track of your time and stay productive. It's designed to be easy to use and respects your privacy by storing your data locally.


  📊 Features:
    - Track your time with a simple and intuitive interface
    - View detailed reports of your time usage
    - Export your data to CSV for further analysis
    - Secure and private: your data is stored locally on your Umbrel

  ⚠️ New users can only be created via the terminal for now. Example:

  `sudo docker exec -i solidtime_scheduler_1 bash -c "echo '<password' | php artisan admin:user:create '<username>' '<email>' --ask-for-password --verify-email"`


  The application takes a few minutes until it becomes available after installation. Please be patient.
developer: Solidtime
website: https://www.solidtime.io/
submitter: al-lac
submission: https://github.com/getumbrel/umbrel-apps/pull/2588
repo: https://github.com/solidtime-io/solidtime
support: https://github.com/solidtime-io/solidtime/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
defaultUsername: "<EMAIL>"
deterministicPassword: true
defaultPassword: ""
dependencies: []
releaseNotes: >-
  This release includes new features and improvements:

    - New formatting options for numbers, currency, date, time, and intervals
    - New date range picker
    - Improved reporting functionality
    - Various bug fixes and small changes

  For full release notes, visit https://github.com/solidtime-io/solidtime/releases
path: ""
