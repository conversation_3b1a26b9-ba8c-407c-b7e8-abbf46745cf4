manifestVersion: 1
id: code-server
category: developer
name: code-server
version: "4.101.2"
tagline: Run VS Code on your Umbrel
description: >-
  Run VS Code on your Umbrel and access it in the browser so you can
  code on any device with a consistent development environment. This way you can
  use your Umbrel not only to code from any device, anywhere, but to also speed
  up tests, compilations, downloads, and more.


  By running all intensive tasks run on your Umbrel, preserve battery life of your devices when you're on the go.
releaseNotes: >-
  This release updates the underlying VS Code editor to 1.101.2.


  Full release notes are available at https://github.com/coder/code-server/releases
developer: Coder
website: https://coder.com
dependencies: []
repo: https://github.com/cdr/code-server
support: https://github.com/cdr/code-server/discussions
port: 8091
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
deterministicPassword: true
torOnly: false
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/commit/14f8b3aec8cb99ef0940ba0cd9df25b7126a95cc
