manifestVersion: 1
id: mempool
category: bitcoin
name: mempool
version: "3.2.1"
tagline: Be your own explorer
description: >-
  Run your own instance of The Mempool Open Source Project and trust no one.

  
  Use your own node to beautifully visualize data about the Bitcoin network, transactions, blockchain, mempool, and more.


  This product includes GeoLite2 data created by MaxMind, available from https://www.maxmind.com
releaseNotes: >-
  This update brings the mempool app to version 3.2.1, which includes a hotfix for a potential crash loop if the list of mining pools fails to get updated from our GitHub repo.


  Highlights from version 3.2.0:
    - Support for v3 transactions
    - Support for anchor outputs
    - New UTXO bubble chart on the address page
    - DATUM miner tags
    - Tags to identify runestone messages and inscriptions
    - Package broadcast
    - Stratum job data visualizations
    - Taproot multisig labels
    - Transaction & PSBT preview feature
    - Address poisoning detection


  Full release notes are found at https://github.com/mempool/mempool/releases
developer: Mempool Space K.K.
website: https://mempool.space/about
dependencies:
  - bitcoin
  - electrs
repo: https://github.com/mempool/mempool
support: https://mempool.support
port: 3006
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
widgets:
  - id: "stats"
    type: "four-stats"
    refresh: "5s"
    endpoint: "widget-server:3000/widgets/fees"
    link: ""
    example:
      type: "four-stats"
      link: ""
      items:
        - title: "No priority"
          text: "2"
          subtext: "sat/vB"
        - title: "Low priority"
          text: "6"
          subtext: "sat/vB"
        - title: "Medium priority"
          text: "18"
          subtext: "sat/vB"
        - title: "High priority"
          text: "27"
          subtext: "sat/vB"
submitter: Mempool Space K.K.
submission: https://github.com/getumbrel/umbrel/pull/470
