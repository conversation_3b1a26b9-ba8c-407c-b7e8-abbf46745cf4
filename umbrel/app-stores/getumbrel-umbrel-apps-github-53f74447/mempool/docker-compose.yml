version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: $APP_MEMPOOL_IP
      APP_PORT: $APP_MEMPOOL_PORT
      PROXY_AUTH_ADD: "false"
  web:
    image: mempool/frontend:v3.2.1@sha256:dd126cf383bd425ad46710925697c6a7925675a535c1026c206f2c092231e106
    user: "1000:1000"
    init: true
    restart: on-failure
    stop_grace_period: 1m
    command: "./wait-for mariadb:3306 --timeout=720 -- nginx -g 'daemon off;'"
    environment:
      FRONTEND_HTTP_PORT: $APP_MEMPOOL_PORT
      BACKEND_MAINNET_HTTP_HOST: $APP_MEMPOOL_API_IP
      NGINX_PORT: $APP_MEMPOOL_PORT
      NGINX_HOSTNAME: $APP_MEMPOOL_API_IP
      LIGHTNING_DETECTED_PORT: $APP_MEMPOOL_LIGHTNING_NODE_PORT
    networks:
      default:
        ipv4_address: $APP_MEMPOOL_IP
  api:
    image: mempool/backend:v3.2.1@sha256:d3531090e3bdd9a3dd38151349c5027768c3b7132438db267df8d8f026e15e61
    user: "1000:1000"
    init: true
    restart: on-failure
    stop_grace_period: 1m
    command: "./wait-for-it.sh mariadb:3306 --timeout=720 --strict -- ./start.sh"
    volumes:
      - ${APP_DATA_DIR}/data:/backend/cache
      - ${UMBREL_ROOT}/app-data/lightning/data/lnd:/lnd:ro
    environment:
      CORE_RPC_HOST: $APP_BITCOIN_NODE_IP
      CORE_RPC_PORT: $APP_BITCOIN_RPC_PORT
      CORE_RPC_USERNAME: $APP_BITCOIN_RPC_USER
      CORE_RPC_PASSWORD: $APP_BITCOIN_RPC_PASS
      ELECTRUM_HOST: $APP_ELECTRS_NODE_IP
      ELECTRUM_PORT: $APP_ELECTRS_NODE_PORT
      ELECTRUM_TLS: "false"
      DATABASE_HOST: $APP_MEMPOOL_DB_IP
      DATABASE_PORT: "3306"
      DATABASE_DATABASE: "mempool"
      DATABASE_USERNAME: "mempool"
      DATABASE_PASSWORD: "mempool"
      MEMPOOL_HTTP_PORT: "8999"
      MEMPOOL_CACHE_DIR: "/backend/cache"
      MEMPOOL_CLEAR_PROTECTION_MINUTES: "20"
      MEMPOOL_INDEXING_BLOCKS_AMOUNT: "52560"
      MEMPOOL_STDOUT_LOG_MIN_PRIORITY: "info"
      LIGHTNING_ENABLED: "true"
      LIGHTNING_BACKEND: "lnd"
      LIGHTNING_STATS_REFRESH_INTERVAL: 3600
      LIGHTNING_GRAPH_REFRESH_INTERVAL: 3600
      LND_TLS_CERT_PATH: "/lnd/tls.cert"
      LND_MACAROON_PATH: "/lnd/data/chain/bitcoin/$APP_BITCOIN_NETWORK/readonly.macaroon"
      LND_REST_API_URL: "https://$APP_MEMPOOL_LIGHTNING_NODE_IP:$APP_MEMPOOL_LIGHTNING_NODE_REST_PORT"
      LND_TIMEOUT: 120000
      NODE_OPTIONS: "--max-old-space-size=2048"
    networks:
       default:
         ipv4_address: $APP_MEMPOOL_API_IP
  mariadb:
    image: mariadb:10.5.12@sha256:dfcba5641bdbfd7cbf5b07eeed707e6a3672f46823695a0d3aba2e49bbd9b1dd
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/mysql/data:/var/lib/mysql
    environment:
      MYSQL_DATABASE: "mempool"
      MYSQL_USER: "mempool"
      MYSQL_PASSWORD: "mempool"
      MYSQL_ROOT_PASSWORD: "moneyprintergobrrr"
    networks:
       default:
         ipv4_address: $APP_MEMPOOL_DB_IP
  widget-server:
    image: getumbrel/umbrel-mempool-widget-server:v1.0.0@sha256:099846e613c7310deba842241477737babcdf78250fba6edf39d456712eeefc3
    environment:
      MEMPOOL_API_URL: "http://$APP_MEMPOOL_IP:$APP_MEMPOOL_PORT/api/v1/fees/recommended"
    restart: on-failure