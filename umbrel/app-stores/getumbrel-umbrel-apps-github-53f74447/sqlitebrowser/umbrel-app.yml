manifestVersion: 1
id: sqlitebrowser
name: SQLite Browser
tagline: A database browser for SQLite
category: developer
version: "3.13.1"
port: 8887
description: >-
  DB Browser for SQLite (DB4S) is a high quality, visual, open source tool to create, design, and edit database files compatible with SQLite.


  DB4S is for users and developers who want to create, search, and edit databases. DB4S uses a familiar spreadsheet-like interface, so complicated SQL commands do not have to be learned.


  Controls and wizards are available for users to:

  - Create and compact database files

  - Create, define, modify and delete tables

  - Create, define, and delete indexes

  - Browse, edit, add, and delete records

  - Search records

  - Import and export records as text

  - Import and export tables from/to CSV files

  - Import and export databases from/to SQL dump files

  - Issue SQL queries and inspect the results

  - Examine a log of all SQL commands issued by the application

  - Plot simple graphs based on table or query data

gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
dependencies: []
releaseNotes: ""
path: ""
defaultUsername: ""
defaultPassword: ""
developer: SQLiteBrowser
website: https://sqlitebrowser.org/
repo: https://github.com/sqlitebrowser/sqlitebrowser
support: https://github.com/sqlitebrowser/sqlitebrowser/issues
submitter: dirstel
submission: https://github.com/getumbrel/umbrel-apps/pull/2223
