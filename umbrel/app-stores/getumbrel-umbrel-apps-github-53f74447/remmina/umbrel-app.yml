manifestVersion: 1
id: remmina
category: networking
name: Remmina
version: "1.4.35"
tagline: Remote access screen and file sharing
description: >-
  Use other desktops remotely, from a tiny screen or large monitors.


  - RDP, VNC, SPICE, X2Go, SSH, WWW (HTTP protocol) and EXEC network protocols are supported.
  
  
  - Written in GTK, with a port to Qt underway.


  - Released as "remmina" (the main program) and "remmina-plugins".


  - Remmina is copylefted libre software.


  What is libre software?


  The ability to use, see, modify, and share with all.
  Requiring sharing on equal terms is what (additionally) makes it copyleft, meaning none of these software freedoms can be taken away.
developer: Remmina
website: https://remmina.org
dependencies: []
repo: https://gitlab.com/Remmina/Remmina
support: https://www.remmina.org/support/
port: 8764
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: Pranshu Agrawal
submission: https://github.com/getumbrel/umbrel-apps/pull/291
releaseNotes: >-
  This release includes several improvements and bug fixes:
    - Fixed build issues for OpenSSL 1.1
    - Added support for running SSH commands when connecting via SSH tunnel
    - Resolved compilation issues with GCC-14
    - Fixed a crash related to the 'disable password storing' feature
    - Updated handling of load balancing information

  For full release notes, visit https://gitlab.com/Remmina/Remmina/-/releases