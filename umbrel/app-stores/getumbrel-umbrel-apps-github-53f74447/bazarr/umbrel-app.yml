manifestVersion: 1
id: bazarr
category: media
name: Bazarr
version: "1.5.2"
tagline: Manage and download subtitles for Sonarr and Radarr
description: >-
  Bazarr is a companion application to Sonarr and Radarr that manages and downloads subtitles based on your requirements.
  You define your preferences by TV show or movie and <PERSON>zarr takes care of everything for you.


  Bazarr does not scan your disk to detect series and movies. It only takes care of the series and movies that are indexed in Sonarr and Radarr.


  🛠️ SET-UP INSTRUCTIONS
  
  During initial set-up, you will need to input your Umbrel device's IP address to connect to Sonarr and/or Radarr.
  You can find your device's IP address by visiting your router's admin dashboard or by using an IP scanning tool like Angry IP Scanner.
  You will also need to input your API key for Sonarr and/or Radarr. You can find your API keys in the settings of the Sonarr and Radarr apps.
releaseNotes: >-
  Key improvements in this release include:

    - Enhanced subtitle search and matching capabilities
    - Improved handling of embedded subtitles
    - Better support for multiple languages and translations
    - Added new subtitle provider options
    - Improved series and episode management
    - Enhanced settings organization and user interface
    - Various performance improvements and bug fixes


  Full release notes are found at https://github.com/morpheus65535/bazarr/releases
developer: morpheus65535
website: https://www.bazarr.media/
dependencies: []
repo: https://github.com/morpheus65535/bazarr
support: https://github.com/morpheus65535/bazarr/issues
port: 6767
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: false
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/610
