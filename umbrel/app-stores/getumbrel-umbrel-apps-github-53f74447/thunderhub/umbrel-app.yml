manifestVersion: 1
id: thunderhub
category: bitcoin
name: ThunderHub
version: "0.13.32"
tagline: Take full control of your Lightning node
description: >-
  ThunderHub allows you to take full control of your Lightning node
  with a slick and awesome UI. Explore all the options that ThunderHub has to
  offer, from sending and receiving Lightning payments, to checking your node's
  health statistics, and even more advanced options like channel rebalancing and
  multi-path lightning payments.


  Managing and monitoring your node has never been easier with transaction reports, graphs and a huge assortment of different features all bundled inside of this great tool.
developer: <PERSON>
website: https://thunderhub.io/
dependencies:
  - lightning
repo: https://github.com/apotdevin/thunderhub
support: https://t.me/thunderhub
port: 3000
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
deterministicPassword: true
releaseNotes: >-
  This release fixes issues when fetching multiple Boltz swap statuses.

  
  Full release notes can be found at https://github.com/apotdevin/thunderhub/releases/tag/v0.13.32
submitter: <PERSON>
submission: https://github.com/getumbrel/umbrel/pull/343
