version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: thunderhub_web_1
      APP_PORT: 3000

  web:
    image: apotdevin/thunderhub:v0.13.32@sha256:a1db59155b4e6f9af027e7ac722aed7b186d3406648d08add753f3dc7d44086a
    # We now have to run as root to avoid schema.gql permission errors
    # user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
      - ${APP_DATA_DIR}/data:/data
    environment:
      MASTER_PASSWORD_OVERRIDE: "$APP_PASSWORD"
      NO_VERSION_CHECK: "true"
      LOG_LEVEL: "debug"
      ACCOUNT_CONFIG_PATH: "/data/thubConfig.yaml"
      YML_ENV_1: "$APP_LIGHTNING_NODE_IP:$APP_LIGHTNING_NODE_GRPC_PORT"
      TOR_PROXY_SERVER: "socks://$TOR_PROXY_IP:$TOR_PROXY_PORT"
