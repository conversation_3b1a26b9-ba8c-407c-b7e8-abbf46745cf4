manifestVersion: 1
id: calibre-web
category: files
name: Calibre Web
version: "0.6.24"
tagline: A clean web app for your eBooks
description: >-
  Calibre-Web is a web app providing a clean interface for browsing, reading and downloading eBooks using an existing Calibre database.
  It is also possible to integrate google drive and edit metadata and your calibre library through the app itself. 
  
  
  🛠️ SET-UP INSTRUCTIONS

    - Library Setup: On the initial setup screen, enter /books as your calibre library location.
    - Uploading eBooks: If you wish to enable the upload feature, navigate to Admin > Edit Basic Configuration > Feature Configuration and toggle "Enable Upload".
    - Documentation: It is recommended to read through the official documentation available at https://github.com/janeczku/calibre-web/wiki
developer: Janeczku
website: https://github.com/janeczku/calibre-web
dependencies: []
repo: https://github.com/janeczku/calibre-web
support: https://github.com/linuxserver/docker-calibre-web/issues
port: 8098
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: "admin"
defaultPassword: "admin123"
torOnly: false
releaseNotes: >-
  This update includes several new features and improvements:

    - Added metadata extraction for audio files during upload
    - Updated PDF viewer with improved functionality
    - New upload interface with progress tracking and drag & drop support
    - Improved shelf sorting capabilities
    - Better contrast for epub reader in dark modes
    - Enhanced support for multiple e-reader email addresses
    - Improved whitespace handling and string formatting
    - Various improvements to the Kobo sync and browser experience
    - Multiple bug fixes for database handling and metadata operations
    - Enhanced support for Windows installations

  Full release notes can be found here: https://github.com/janeczku/calibre-web/releases
submitter: Xosten
submission: https://github.com/getumbrel/umbrel-apps/pull/74