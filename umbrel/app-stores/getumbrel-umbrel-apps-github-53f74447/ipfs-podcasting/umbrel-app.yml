manifestVersion: 1
id: ipfs-podcasting
category: files
name: IPFS Podcasting
version: "0.6"
tagline: Crowd-host podcasts over IPFS
description: |-
  Turn your Umbrel into an IPFS node for self-hosting, crowd-hosting, and archiving of your favorite podcasts to the IPFS network.

  IPFSPodcasting.net uses participating nodes to manage & track hashes of podcast episodes and generates IPFS/RSS feeds that load media from the IPFS network.

  You only need to run the app to participate. You can optionally configure your email, and manage your node from the website. View the FAQ to learn more.
developer: IPFSPodcasting.net
website: https://IPFSPodcasting.net
dependencies: []
repo: https://github.com/Cameron-IPFSPodcasting/podcastnode
support: https://github.com/Cameron-IPFSPodcasting/podcastnode/issues
port: 8675
gallery:
- 1.jpg
- 2.jpg
- 3.jpg
path: ""
defaultPassword: ""
submitter: IPFSPodcasting.net
submission: https://github.com/getumbrel/umbrel/pull/1356
releaseNotes: ""