version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: ipfs-podcasting_web_1
      APP_PORT: 8675

  web:
    image: ipfspodcasting/podcastnode:v0.6@sha256:773d7c34d452764be973e8bc495990ec47f8042287493997784198c64e924393
    init: true
    restart: on-failure
    stop_grace_period: 1m
    user: "1000:1000"
    ports:
      - 4001:4001  # IPFS P2P
    volumes:
      - ${APP_DATA_DIR}/ipfs:/ipfs-podcasting/ipfs
      - ${APP_DATA_DIR}/cfg:/ipfs-podcasting/cfg
