manifestVersion: 1
id: paperless
category: files
name: Paperless-ngx
version: "2.17.1"
tagline: Transform your physical documents into a searchable online archive
description: >-
  Paperless-ngx is a community-supported open-source document management system that transforms your physical documents into a searchable online archive so you can keep, well, less paper.

  - Organize and index your scanned documents with tags, correspondents, types, and more.

  - Your data is stored locally on your server and is never transmitted or shared in any way.

  - Performs OCR on your documents, adding searchable and selectable text, even to documents scanned with only images.

  - Utilizes the open-source Tesseract engine to recognize more than 100 languages.

  - Documents are saved as PDF/A format which is designed for long term storage, alongside the unaltered originals.

  - Uses machine-learning to automatically add tags, correspondents and document types to your documents.

  - Supports PDF documents, images, plain text files, Office documents (Word, Excel, Powerpoint, and LibreOffice equivalents)1 and more.

  - Paperless stores your documents plain on disk. Filenames and folders are managed by paperless and their format can be configured freely with different configurations assigned to different documents.
releaseNotes: >-
  ⚠️ This version includes important fixes for scheduled workflows. If you configured scheduled workflows in v2.16.x with inverted offsets, you must now adjust the offset sign to match the corrected logic.


  This update includes bug fixes and improvements:
    - Fixed scheduled workflow offset behavior to restore intuitive pre-v2.16 behavior
    - Added Persian translation support
    - Enhanced import functionality with support for zipped exports
    - Fixed various API crashes and schema issues
    - Improved websocket URL handling
    - Better version logging and backend/frontend mismatch detection


  Full release notes are found at https://github.com/paperless-ngx/paperless-ngx/releases
developer: Paperless-ngx Community
website: https://docs.paperless-ngx.com/
dependencies: []
repo: https://github.com/paperless-ngx/paperless-ngx
support: https://github.com/paperless-ngx/paperless-ngx/discussions
port: 2349
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: "admin"
deterministicPassword: true
submitter: highghlow
submission: https://github.com/getumbrel/umbrel-apps/pull/1047
