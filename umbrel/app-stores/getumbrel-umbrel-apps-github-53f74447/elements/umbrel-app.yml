manifestVersion: 1.1
id: elements
category: bitcoin
name: Elements Core
version: "23.2.7"
tagline: Liquid Network full node
description: >-
  Elements is an open source full node implementation to run the Liquid
  Network, a Bitcoin layer-2 solution enabling the fast, confidential settlement and
  issuance of digital assets, such as stablecoins, security tokens, and other financial
  instruments, on top of the Bitcoin timechain.


  This app has the potential to use up to 6GB of RAM. It's important to confirm that your device meets the necessary hardware requirements before installing and running the app.
releaseNotes: >-
  This release updates elements from version 23.2.4 to 23.2.7, and features various bug fixes and improvements. In contrast to Bitcoin, Elements is not changing the default wallet format from legacy to descriptors, this is because many things (like peg-ins and peg-outs) still work only on legacy wallets.



  For more detailed information on this update, please refer to the full release notes 
  available at https://github.com/ElementsProject/elements/releases
developer: Elements Core Developers
website: https://elementsproject.org
dependencies:
- bitcoin
repo: https://github.com/ElementsProject/elements
support: https://t.me/liquid_community
port: 3042
gallery:
- 1.jpg
- 2.jpg
- 3.jpg
path: ''
deterministicPassword: false
torOnly: false
submitter: <PERSON>
submission: https://github.com/getumbrel/umbrel/pull/1319
