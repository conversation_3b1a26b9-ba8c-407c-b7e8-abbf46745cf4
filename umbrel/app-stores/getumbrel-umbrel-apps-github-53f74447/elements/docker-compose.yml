version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: elements_web_1
      APP_PORT: 8080

  node:
    image: ghcr.io/vulpemventures/elements:23.2.7@sha256:f357f9faeef4fed703599f006cccfa46fcb00e1c00ac94b8a8f973524196fed9
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    ports:
      - $APP_ELEMENTS_NODE_RPC_PORT:$APP_ELEMENTS_NODE_RPC_PORT # JSONRPC
      - $APP_ELEMENTS_NODE_P2P_PORT:$APP_ELEMENTS_NODE_P2P_PORT # P2P PORT
    volumes:
      - ${APP_DATA_DIR}/data:/home/<USER>/.elements
    command:
      - -listen=0
      - -txindex=1
      - -trim_headers=1
      # Current Elements Core requires >8GB of RAM to verify confidential proofs during IBD
      # We skip historical block verification for now, as we don't have a way to set the memory limit
      - -assumevalid=546a756e0238e8c5c2c8bee43f0395740519f96aaee5994d05ef6569ce2d0821
      - -validatepegin=1
      - -fallbackfee=0.000001
      # Attach to Bitcoin network
      - -mainchainrpchost=$APP_BITCOIN_NODE_IP
      - -mainchainrpcport=$APP_BITCOIN_RPC_PORT
      - -mainchainrpcuser=$APP_BITCOIN_RPC_USER
      - -mainchainrpcpassword=$APP_BITCOIN_RPC_PASS
      # Elements RPC
      - -rpcuser=elements
      - -rpcpassword=$APP_PASSWORD
      - -rpcbind=0.0.0.0 # can we do better here?
      - -rpcallowip=0.0.0.0/0 # can we do better here?
      - -rpcport=$APP_ELEMENTS_NODE_RPC_PORT
      # P2P 
      - -port=$APP_ELEMENTS_NODE_P2P_PORT
      - -blockfilterindex=1
      - -peerblockfilters=1
      # Enable creating discounted CT on mainnet
      - -creatediscountct=1
  
  web: 
    image: ghcr.io/tiero/elements-web:v0.0.3@sha256:b80dc0df2a4693668a97b874aad6d0a8d3239d03f4d05b4342600166934d0b87
    user: "1000:1000"
    depends_on:
      - node
    restart: on-failure
    environment:
      RPC_USER: elements
      RPC_PASS: $APP_PASSWORD
      RPC_PORT: $APP_ELEMENTS_NODE_RPC_PORT
      RPC_HOST: elements_node_1
      P2P_PORT: $APP_ELEMENTS_NODE_P2P_PORT
      # Remote 
      REMOTE_RPC_HOST: $APP_ELEMENTS_RPC_HIDDEN_SERVICE
      REMOTE_P2P_HOST: $APP_ELEMENTS_P2P_HIDDEN_SERVICE
  
  tor:
    image: getumbrel/tor:*******@sha256:2ace83f22501f58857fa9b403009f595137fa2e7986c4fda79d82a8119072b6a
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/torrc:/etc/tor/torrc:ro
      - ${TOR_DATA_DIR}:/data
    environment:
      HOME: "/tmp"
