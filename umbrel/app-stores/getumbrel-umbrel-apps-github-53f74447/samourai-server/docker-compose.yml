version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: $APP_SAMOURAI_SERVER_IP
      APP_PORT: 8081
      PROXY_AUTH_ADD: "false"
  db:
    image: louneskmt/dojo-db:1.5.0-low-mem@sha256:5af4f90818d55360e572033fcc493cd945143e7a3aaef91fa7e102f3c66c7a5b
    init: true
    restart: on-failure
    stop_grace_period: 5m
    user: "1000:1000"
    environment:
      MYSQL_DATABASE: samourai-main
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_USER: samourai
      MYSQL_PASSWORD: password
    volumes:
      - ${APP_DATA_DIR}/mysql/data:/var/lib/mysql
    networks:
      default:
        ipv4_address: $APP_SAMOURAI_SERVER_DB_IP

  node:
    # image: louneskmt/dojo-nodejs:1.16.1@sha256:49de92774ecfcb88af1dc67f8d498641d750c4ec9acaab3c448d70c4f2d4bfe7
    image: nmfretz/dojo-nodejs:1.16.1-rpcfix@sha256:bd047ed34e04b605c662c94ed5819322c0db65b6e3425acd3607486ef28a83ce
    init: true
    restart: on-failure
    command: "/home/<USER>/app/wait-for-it.sh ${APP_SAMOURAI_SERVER_DB_IP}:3306 --timeout=720 --strict -- /home/<USER>/app/restart.sh"
    user: "1000:1000"
    environment:
      # GLOBAL
      COMMON_BTC_NETWORK: $APP_BITCOIN_NETWORK
      DOJO_VERSION_TAG: 1.16.1
      NET_DOJO_TOR_IPV4: $TOR_PROXY_IP
      TOR_SOCKS_PORT: $TOR_PROXY_PORT
      NET_DOJO_MYSQL_IPV4: $APP_SAMOURAI_SERVER_DB_IP

      # MYSQL
      MYSQL_DATABASE: samourai-main
      MYSQL_USER: samourai
      MYSQL_PASSWORD: password

      # NODEJS
      NODE_GAP_EXTERNAL: 100
      NODE_GAP_INTERNAL: 100
      NODE_ADDR_FILTER_THRESHOLD: 1000
      NODE_ADDR_DERIVATION_MIN_CHILD: 2
      NODE_ADDR_DERIVATION_MAX_CHILD: 2
      NODE_ADDR_DERIVATION_THRESHOLD: 10
      NODE_TXS_SCHED_MAX_ENTRIES: 10
      NODE_TXS_SCHED_MAX_DELTA_HEIGHT: 18
      NODE_JWT_ACCESS_EXPIRES: 900
      NODE_JWT_REFRESH_EXPIRES: 7200
      NODE_PREFIX_STATUS: status
      NODE_PREFIX_SUPPORT: support
      NODE_PREFIX_STATUS_PUSHTX: status
      NODE_TRACKER_MEMPOOL_PERIOD: 10000
      NODE_TRACKER_UNCONF_TXS_PERIOD: 300000
      NODE_ACTIVE_INDEXER: local_indexer
      NODE_FEE_TYPE: ECONOMICAL

      # SECURITY
      NODE_API_KEY: $APP_SAMOURAI_SERVER_NODE_API_KEY
      NODE_ADMIN_KEY: $APP_SAMOURAI_SERVER_NODE_ADMIN_KEY
      NODE_JWT_SECRET: $APP_SAMOURAI_SERVER_NODE_JWT_SECRET

      # BITCOIN
      BITCOIND_IP: $APP_BITCOIN_NODE_IP
      BITCOIND_RPC_PORT: $APP_BITCOIN_RPC_PORT
      BITCOIND_RPC_USER: $APP_BITCOIN_RPC_USER
      BITCOIND_RPC_PASSWORD: $APP_BITCOIN_RPC_PASS
      BITCOIND_ZMQ_RAWTXS: $APP_BITCOIN_ZMQ_RAWTX_PORT
      BITCOIND_ZMQ_BLK_HASH: $APP_BITCOIN_ZMQ_HASHBLOCK_PORT

      # EXPLORER
      EXPLORER_INSTALL: "off"

      # INDEXER
      INDEXER_IP: $APP_ELECTRS_NODE_IP
      INDEXER_RPC_PORT: $APP_ELECTRS_NODE_PORT
      INDEXER_PROTOCOL: tcp
      INDEXER_BATCH_SUPPORT: inactive # 'active' for ElectrumX, 'inactive' otherwise
    depends_on:
      - db
    networks:
      default:
        ipv4_address: $APP_SAMOURAI_SERVER_NODE_IP

  whirlpool:
    image: louneskmt/whirlpool:0.10.15@sha256:3b766327dfa4c2ae40f190c18afa1f3c9cb9101d7964049dc79bdcb89fd21a90
    init: true
    restart: on-failure
    user: "1000:1000"
    command:
      - "--listen"
      - "--cli.apiKey=$APP_SAMOURAI_SERVER_WHIRLPOOL_API_KEY"
      - "--cli.api.http-enable=true"
      - "--cli.api.http-port=$APP_SAMOURAI_SERVER_WHIRLPOOL_PORT"
      - "--cli.tor=true"
      - "--cli.torConfig.coordinator.enabled=true"
      - "--cli.torConfig.coordinator.onion=true"
      - "--cli.torConfig.backend.enabled=false"
      - "--cli.torConfig.backend.onion=false"
      - "--cli.mix.liquidityClient=true"
      - "--cli.mix.clientsPerPool=1"
      - "--resync"
    environment:
      WHIRLPOOL_BITCOIN_NETWORK: $APP_BITCOIN_NETWORK
      WHIRLPOOL_DOJO: "on"
      WHIRLPOOL_DOJO_IP: $APP_SAMOURAI_SERVER_IP
    depends_on:
      - node
    volumes:
      - ${APP_DATA_DIR}/whirlpool:/home/<USER>/.whirlpool-cli
    networks:
      default:
        ipv4_address: $APP_SAMOURAI_SERVER_WHIRLPOOL_IP

  nginx:
    image: nginx:1.21-alpine@sha256:686aac2769fd6e7bab67663fd38750c135b72d993d0bb0a942ab02ef647fc9c3
    init: true
    restart: on-failure
    command: /bin/sh -c "envsubst < /var/www/connect/js/conf.template.js > /var/www/connect/js/conf.js && /wait-for node:8080 --timeout=720 -- nginx"
    volumes:
      - ${APP_DATA_DIR}/nginx/wait-for:/wait-for
      - ${APP_DATA_DIR}/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ${APP_DATA_DIR}/nginx/${APP_BITCOIN_NETWORK}.conf:/etc/nginx/sites-enabled/dojo.conf
      - ${APP_DATA_DIR}/nginx/connect.conf:/etc/nginx/sites-enabled/connect.conf
      - ${APP_DATA_DIR}/nginx/connect:/var/www/connect
    environment:
      COMMON_BTC_NETWORK: $APP_BITCOIN_NETWORK
      DOJO_LOCAL_PORT: $APP_SAMOURAI_SERVER_DOJO_PORT
      DOJO_HIDDEN_SERVICE: $APP_SAMOURAI_SERVER_DOJO_HIDDEN_SERVICE
      WHIRLPOOL_HIDDEN_SERVICE: $APP_SAMOURAI_SERVER_WHIRLPOOL_HIDDEN_SERVICE
      NODE_PREFIX_SUPPORT: support
      NODE_ADMIN_KEY: $APP_SAMOURAI_SERVER_NODE_ADMIN_KEY
      WHIRLPOOL_API_KEY: $APP_SAMOURAI_SERVER_WHIRLPOOL_API_KEY
    ports:
      - "$APP_SAMOURAI_SERVER_DOJO_PORT:80"
    depends_on:
      - node
    networks:
      default:
        ipv4_address: $APP_SAMOURAI_SERVER_IP

  tor:
    image: getumbrel/tor:*******@sha256:2ace83f22501f58857fa9b403009f595137fa2e7986c4fda79d82a8119072b6a
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/torrc:/etc/tor/torrc:ro
      - ${TOR_DATA_DIR}:/data
    environment:
      HOME: "/tmp"
