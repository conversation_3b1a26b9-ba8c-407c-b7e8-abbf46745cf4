manifestVersion: 1.1
id: samourai-server
category: bitcoin
name: Samourai Server
version: "1.16.1-hotfix-4"
tagline: Your private backing server for Samourai Wallet
description: >-
  ⚠️ Whirlpool functionality no longer works due to the shutdown of Samourai's Whirlpool coordinator. Dojo functionality is unaffected.


  Samourai Server is an exclusive Umbrel app that runs Samourai Dojo
  and Whirlpool backing servers, and provides you easy step-by-step instructions
  to connect your Samourai Wallet to them. Samourai Wallet is unrivaled in
  transaction privacy, but its default configuration is still subject to network
  level privacy loss.


  By default, Samourai Wallet will connect to its centrally controlled Dojo hosted in Iceland. By hosting your own Samourai Server, you can bypass its servers completely when using Samourai Wallet or Sentinel, and gain total financial sovereignty without any complexity.


  Bundles Whirlpool CLI v0.10.15.
developer: Samourai
website: https://samouraiwallet.com
dependencies:
  - bitcoin
  - electrs
repo: https://github.com/louneskmt/umbrel-samourai-dojo/tree/v1.16.1-umbrel
support: https://t.me/SamouraiWallet
port: 3021
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  This update fixes a bug where Samourai Server could not connect to Bitcoin Core v28.0.


  Note that Whirlpool functionality no longer works due to the shutdown of Samourai's Whirlpool coordinator. Dojo functionality is unaffected.
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/pull/461