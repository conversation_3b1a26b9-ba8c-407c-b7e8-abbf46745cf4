# Proxy WebSockets
# https://www.nginx.com/blog/websocket-nginx/
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# WebSocket server listening here
upstream websocket {
    server node:8080;
}

# Site Configuration
server {
    listen 80;
    server_name _;

    # Set proxy timeouts for the application
    proxy_connect_timeout 600;
    proxy_read_timeout 600;
    proxy_send_timeout 600;
    send_timeout 600;

    # Proxy WebSocket connections first
    location /test/v2/inv {
        proxy_pass http://websocket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
    }

    # PushTX server is separate, so proxy first
    location /test/v2/pushtx/ {
        proxy_pass http://node:8081/;
    }

    # Tracker server is separate, so proxy first
    location /test/v2/tracker/ {
        proxy_pass http://node:8082/;
    }

    # Proxy requests to maintenance tool
    location = /admin/conf/index.js {
        proxy_pass http://node:8080/static/admin/conf/index-testnet.js;
    }

    location /admin/ {
        proxy_pass http://node:8080/static/admin/;
    }

    # Proxy all other v2 requests to the accounts server
    location /test/v2/ {
        proxy_pass http://node:8080/;
    }

    # Redirect onion address to maintenance tool
    location = / {
        return 301 /admin;
    }

    # Serve remaining requests
    location / {
        return 200 '{"status":"ok"}';
        add_header Content-Type application/json;
    }

    location /test/ {
        return 200 '{"status":"ok"}';
        add_header Content-Type application/json;
    }
}
