<!doctype html>

<html lang="en">

<head>
    <meta charset="utf-8">

    <title>Samourai Server</title>

    <link rel="shortcut icon" type="image/jpg" href="img/icon.svg" />

    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <section class="container">
        <div class="app">
            <img class="app-icon" src="img/icon.svg" />
            <div class="app-details">
                <span class="app-status success">&#9679; Running</span>
                <h1 class="app-name">Samourai Server</h1>
            </div>
        </div>
        <p class="text-muted">Follow the instructions below to pair Do<PERSON> and Whirlpool running on your Umbrel to your
            Samourai Wallet.
        </p>
    </section>
    <hr />
    <section class="container">
        <div class="heading">
            <span class="number">1</span>
            <h2 class="text">How to connect Dojo</h2>
        </div>
        <ol class="steps">
            <li class="step">Open the Samourai Wallet app on your phone.</li>
            <li class="step">If you already have an existing wallet on it, send all of your funds to a different wallet
                that you
                control and erase your existing wallet from Settings > Wallet > Secure Erase Wallet. If you don’t
                have a wallet setup, skip this step.</li>
            <li class="step">Select Mainnet.</li>
            <li class="step">Tap the 3-dot menu and select “Connect to existing Dojo”.</li>
            <li class="step">Tap “Connect to existing Dojo” and scan this QR Code:
              <div class="qr">
               <div class="qr-contents"></div>
                    <div class="icon">
                        <img src="img/icon.svg" />
                    </div>
                </div>
              </div>

            </li>
            <li class="step">Tap "Start New Wallet" and finish the wallet creation process.</li>
            <li class="step">Congratulations! Your Samourai Wallet is now backed by the Dojo server running on your
                Umbrel. Open
                Network Options by tapping the WiFi-like icon on the top to verify if “Dojo Full Node” is successfully
                enabled (it should display a green dot).</li>
        </ol>
        <div class="note">
            <h5 class="note-heading text-muted text-small">Advanced</h5>
            <p class="note-text">
                If you need to access the Dojo Maintenance Tool, <a id="dmt-link" href="#" target="_blank">click here</a>.
            </p>
            <p class="note-text text-small">
                Admin key: <code id="dojo-admin-key"></code>
            </p>
        </div>
    </section>
    <hr />
    <section class="container">
        <div class="heading">
            <span class="number">2</span>
            <h2 class="text">How to connect Whirlpool</h2>
        </div>
        <ol class="steps">
            <li class="step">Install <a href="https://gist.github.com/lukechilds/0be1d56ecd28092822e4fa750b5945c0" target="_blank">Tor</a> on your computer.</li>
            <li class="step">Download and install <a href="https://code.samourai.io/whirlpool/whirlpool-gui/-/releases"
                    target="_blank">Whirlpool GUI</a>.</li>
            <li class="step">Select: “Advanced: remote CLI”.</li>
            <li class="step">Enter "<b id="whirlpool-hidden-service"></b>" (without quotes) in “CLI
                address”.</li>
            <li class="step">Tor proxy should now auto enable and set itself to “socks5://127.0.0.1:9050”.</li>
            <li class="step">Click “Configure API key?”.</li>
            <li class="step">Enter "<b id="whirlpool-api-key"></b>" (without quotes) in “API key”.</li>
            <li class="step">Click “Connect”.</li>
            <li class="step">Click QR code icon to scan a QR code from Samourai Wallet on your phone.</li>
            <li class="step">Open Samourai Wallet on your phone.</li>
            <li class="step">Go Settings > Transactions > Experimental > Pair to Whirlpool GUI. Show the QR code on your
                phone to your desktop's webcam to scan it.</li>
            <li class="step">Click “Initialize GUI”.</li>
            <li class="step">Enter your Samourai Wallet’s passphrase (BIP39 passphrase set in Samourai wallet).</li>
            <li class="step">Choose a number of mixes for a UTXO.</li>
            <li class="step">Click “Mix”.</li>
            <li class="step">Congratulations! Whirlpool is now mixing your UTXOs on your Umbrel!</li>
        </ol>
        <p><strong>Note:</strong> You'll need to open Whirlpool GUI and re-enter your password to continue mixing after restarting or updating your Umbrel.</p>
    </section>
    <script src="js/qrcode.min.js"></script>
    <script src="js/conf.js?v1.10.1"></script>
    <script src="js/script.js?v1.10.1"></script>
</body>

</html>
