body {
    background-color: #1D1B1B;
    font-family: system-ui,-apple-system,BlinkMacSystemFont,Roboto,Helvetica Neue,Segoe UI,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;
    color: #fff;
}

*, h1, h2, h3, h4, h5, h6, p, span {
    color: #fff;
    font-size: 20px;
    font-weight: normal;
}

.success {
    color: #63FB72;
}

.text-muted {
    opacity: 0.8;
}

.text-small {
    font-size: 14px;
}

hr {
    width: 100%;
    height: 2px;
    background: #2F2C2C;
    border: none;
}

.container {
    padding: 40px;
    max-width: 1440px;
    margin: auto;
}

.app-icon {
    border-radius: 20px;
}

.app {
    display: flex;
    margin: 20px 0 40px 0;
}

.app > .app-icon {
    flex-shrink: 0;
    height: 140px;
    width: 140px;
    box-shadow: 0 0 40px 0 rgba(0,0,0,0.95);
    margin-right: 24px;
}

.app > .app-details > .app-status {
    display: block;
    font-size: 20px;
    margin: 10px 0 0 0;
}

.app > .app-details > .app-name {
    font-size: 52px;
    line-height: 52px;
    font-weight: bold;
    margin: 10px 0 0 0;
}

.heading {
    display: flex;
}

.heading > .number {
    flex-shrink: 0;
    background: #C12525;
    height: 66px;
    width: 66px;
    border-radius: 100%;
    line-height: 66px;
    text-align: center;
    font-size: 36px;
    font-weight: bold;
    box-shadow: 0 0 20px 0 rgba(0,0,0,0.8);
}

.heading > .text {
    font-size: 52px;
    line-height: 52px;
    font-weight: bold;
    display: inline-block;
    margin: 5px 0 0 20px;
}

.steps {
    margin: 40px 0 0 9px;
}

.steps > .step {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: normal;
}

.qr {
    position: relative;
    width: 260px;
    height: 260px;
    margin: 20px 0;
}

.qr > .icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    height: 66px;
    width: 66px;
    background: #ffffff;
}

.qr > .icon > img {
    display: block;
    width: 60px;
    height: 60px;
    margin: 3px 0 0 3px;
    border-radius: 15%;
}

.note {
    margin-top: 30px;
    background: #111010;
    border-radius: 8px;
    padding: 30px;
    border: 2px dashed #3D3838;
}

.note > .note-heading {
    margin: 0 0 20px 0;
    font-weight: 300;
}

.note > .note-text {
    word-wrap: break-word
}