# Connect UI Configuration
server {
    listen 8081;
    server_name _;

    root /var/www/connect;
    index index.html;

    # Proxy pairing endpoint to dojo api
    location /v2/support/pairing {
        proxy_pass http://node:8080/support/pairing;
    }

    location /test/v2/support/pairing {
        proxy_pass http://node:8080/support/pairing;
    }

    # Server Connect UI static files
    location / {
        try_files $uri $uri/ =404;
    }
}
