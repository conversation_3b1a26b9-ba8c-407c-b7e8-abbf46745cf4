user  nginx;
worker_processes  auto;
daemon off;

# Log critical errors and higher to stderr
# (see https://github.com/nginxinc/docker-nginx/blob/594ce7a8bc26c85af88495ac94d5cd0096b306f7/mainline/alpine/Dockerfile#L104)
error_log  /var/log/nginx/error.log crit;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Disable activity logging for privacy.
    access_log  off;

    # Do not reveal the version of server
    server_tokens  off;

    sendfile  on;

    keepalive_timeout  95;

    # Enable response compression
    gzip  on;
    # Compression level: 1-9
    gzip_comp_level  1;
    # Disable gzip compression for older IE
    gzip_disable  msie6;
    # Minimum length of response before gzip kicks in
    gzip_min_length  128;
    # Compress these MIME types in addition to text/html
    gzip_types  application/json;
    # Help with proxying by adding the Vary: Accept-Encoding response
    gzip_vary  on;

    include  /etc/nginx/sites-enabled/*.conf;
}
