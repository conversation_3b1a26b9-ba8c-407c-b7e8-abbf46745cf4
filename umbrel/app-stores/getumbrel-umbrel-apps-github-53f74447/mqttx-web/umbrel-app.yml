manifestVersion: 1
id: mqttx-web
name: MQTTX Web
tagline: Your All-in-One MQTT Client Toolbox
category: automation
version: "1.12.0"
port: 9012
description: >-
  MQTTX Web is an open-source MQTT 5.0 WebSocket client on the browser to help you debug your MQTT services and applications faster.


  The user interface (UI) of MQTTX adopts a chat-based layout, simplifying operational logic. It enables users to establish multiple MQTT connections, thereby facilitating swift testing of MQTT/MQTTS connections, as well as message subscription and publication.
developer: EMQ
website: https://mqttx.app/
submitter: dirstel
submission: https://github.com/getumbrel/umbrel-apps/pull/1772
repo: https://github.com/emqx/MQTTX
support: https://github.com/emqx/MQTTX/discussions
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  Major AI-powered upgrade with enhanced desktop experience and improved functionality.


  Key features and improvements:
    - Auto-save and restore window position for seamless experience
    - Active topic and payload search highlighting for quicker navigation
    - AI Copilot 2.0 with multi-model support including Gemini 2.5, Claude-4, GPT-4.1, and Azure OpenAI
    - One-click custom function and schema generation
    - Native MCP integration with stdio and HTTP SSE server support
    - Session management with enhanced reasoning capabilities
    - Random payload generation with customizable size for stress testing
    - Improved performance with Node 18 runtime and Electron 33


  Bug fixes:
    - Resolved SQLite disconnect warnings on wake-from-sleep
    - Fixed paste functionality in Monaco editor
    - Prevented accidental drag on connection list rows
    - Applied active search filters to live message stream
    - Fixed loading state issues and input focus problems


  Full release notes can be found at https://github.com/emqx/MQTTX/releases/tag/v1.12.0
path: ""
dependencies: []
