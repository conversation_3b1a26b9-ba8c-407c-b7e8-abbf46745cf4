manifestVersion: 1
id: budibase
name: Budibase
tagline: The low code platform you'll enjoy using 
category: developer
version: "3.13.6"
port: 6733
description: >-
  Budibase is an open-source low-code platform that saves engineers 100s of hours building forms, portals, and approval apps, securely.


  🔨 Build and ship real software

  Unlike other platforms, with Budibase you build and ship single page applications. Budibase applications have performance baked in and can be designed responsively, providing users with a great experience. 


  🗄️ Load data or start from scratch

  Budibase pulls data from multiple sources, including MongoDB, CouchDB, PostgreSQL, MySQL, Airtable, S3, DynamoDB, or a REST API. And unlike other platforms, with Budibase you can start from scratch and create business apps with no data sources.


  🎨 Design and build apps with powerful pre-made components

  Budibase comes out of the box with beautifully designed, powerful components which you can use like building blocks to build your UI. We also expose many of your favourite CSS styling options so you can go that extra creative mile.


  ⚙️ Automate processes, integrate with other tools and connect to webhooks

  Save time by automating manual processes and workflows. From connecting to webhooks to automating emails, simply tell <PERSON><PERSON><PERSON> what to do and let it work for you.
developer: Bud<PERSON>se
website: https://budibase.com
submitter: Shark<PERSON>
submission: https://github.com/getumbrel/umbrel-apps/pull/1178
repo: https://github.com/Budibase/budibase
support: https://github.com/Budibase/budibase/discussions
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  Key highlights in this release include:
    - Improved migration experience and handling
    - Fixed app routing issues for embedded applications
    - Enhanced builder navigation updates
    - Fixed issues with creating new screens
    - Resolved app loading problems
    - Better handling of unpublished changes
    - Improved undo/redo functionality


  Full release notes are found at https://github.com/Budibase/budibase/releases
dependencies: []
path: ""
