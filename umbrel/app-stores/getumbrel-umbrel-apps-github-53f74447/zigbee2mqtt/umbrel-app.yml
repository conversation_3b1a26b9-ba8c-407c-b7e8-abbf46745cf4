manifestVersion: 1
id: "zigbee2mqtt"
name: "Zigbee2MQTT"
tagline: "Zigbee to MQTT bridge, get rid of your proprietary Zigbee bridges"
category: "automation"
version: "2.4.0"
port: 60082
description: >-
  Allows you to use your Zigbee devices without the vendor's bridge or gateway.

  It bridges events and allows you to control your Zigbee devices via MQTT. In this way you can integrate your Zigbee devices with whatever smart home infrastructure you are using.


  **Compatible**

  Zigbee2MQTT supports various Zigbee adapters and a big bunch of devices.


  **Integrations**

  Zigbee2MQTT integrates well with most home automation solutions because it uses MQTT.


  **Open Source**

  Zigbee2MQTT is licenced under the free GNU General Public License 3.


  **Setup** 

  After the install you will be presented with a setup wizard. Select your device and get started. After the setup you need to re-open the app to access the web interface.
developer: "@Koenkk"
website: "https://www.zigbee2mqtt.io/"
submitter: "dirstel"
submission: "https://github.com/getumbrel/umbrel-apps/pull/2472"
repo: "https://github.com/Koenkk/zigbee2mqtt"
support: "https://github.com/Koenkk/zigbee2mqtt/issues"
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: ""
dependencies:
  - mosquitto
path: ""
