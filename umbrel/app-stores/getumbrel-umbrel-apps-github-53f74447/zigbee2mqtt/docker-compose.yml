services:

  app_proxy:
    environment:
      APP_HOST: zigbee2mqtt_app_1
      APP_PORT: 8080

  app:
    image: koenkk/zigbee2mqtt:2.4.0@sha256:dd7479e5416c4b95725333d0a69ff504bcbda9a7a3431e5d5dd1e27c16465cdd
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data:/app/data:rw
      - /run/udev:/run/udev:ro
    privileged: true
    environment:
      ZIGBEE2MQTT_CONFIG_FRONTEND_ENABLED: "true"
      ZIGBEE2MQTT_CONFIG_FRONTEND_PORT: 8080
      ZIGBEE2MQTT_CONFIG_MQTT_SERVER: "mqtt://mosquitto_broker_1:1883"
      Z2M_ONBOARD_NO_REDIRECT: 1
