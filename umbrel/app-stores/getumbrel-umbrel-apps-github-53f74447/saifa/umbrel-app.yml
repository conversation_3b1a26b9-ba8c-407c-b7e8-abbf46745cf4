manifestVersion: 1
id: saifa
category: bitcoin
name: Saifa
version: "1.0.0"
tagline: Lightning network invoice manager
description: >-
  Saifa is designed to enhance the interoperability and efficiency of applications interacting with the Lightning Network Daemon (LND).
  

  Our project simplifies the integration process, allowing applications to seamlessly connect to LND through an intuitive webhook system. 
  

  With Saifa, users can effortlessly create new invoices using a straightforward REST API, cancel invoices, and receive real-time updates on invoice status.
  

  This system is crafted to streamline operations and facilitate a smoother transaction experience on the Lightning Network, making it an essential tool for developers looking to optimize their applications for better performance and user satisfaction.
releaseNotes: ""
developer: patra nakamo
website: https://github.com/patranakamo
email: <EMAIL>
dependencies:
  - bitcoin
  - lightning
repo: https://github.com/patranakamo/saifa-be
support: https://github.com/patranakamo/saifa-be/discussions
port: 9989
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
path: ""
defaultUsername: ""
deterministicPassword: true
submitter: patra nakamo
torOnly: false
submission: https://github.com/getumbrel/umbrel/pull/1021