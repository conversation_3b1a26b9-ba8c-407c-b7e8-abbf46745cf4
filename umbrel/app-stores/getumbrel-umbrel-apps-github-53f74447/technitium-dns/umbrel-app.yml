manifestVersion: 1
id: technitium-dns
category: networking
name: Technitium DNS Server
version: "13.6.0"
tagline: Block ads & malware at the DNS level for your entire network
description: >-
  Technitium DNS Server is an open source authoritative, as well as recursive, DNS server that can be used for self hosting a DNS server for privacy & security.
  It works out-of-the-box with no or minimal configuration.
releaseNotes: >-
  This release includes several improvements:
    - Added support for importing zone files when creating new zones
    - Enhanced web GUI with customizable lists and improved zone record filtering
    - Fixed DNS-over-QUIC connection handshake issue
    - Updated Query Logs app with database VACUUM option
    - Improved Geo apps with macro variable support for simpler configuration
    - Added Ed25519 and Ed448 DNSSEC algorithm support
    - Enabled use of user-specified DNSSEC private keys
    - Improved DNS log output options and zone file parser compatibility
    - Removed NS Revalidation feature due to complexity and resolution issues
    - Introduced app preference ordering and updated several apps for it
    - Improved DNS admin panel flexibility and log exporter header configuration
    - Multiple minor fixes and usability enhancements


  Full release notes can be found at https://github.com/TechnitiumSoftware/DnsServer/blob/master/CHANGELOG.md
developer: Technitium
website: https://technitium.com/dns/
dependencies: []
repo: https://github.com/TechnitiumSoftware/DnsServer
support: https://github.com/TechnitiumSoftware/DnsServer/discussions
port: 5380
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: admin
deterministicPassword: true
submitter: highghlow
submission: https://github.com/getumbrel/umbrel/pull/1046