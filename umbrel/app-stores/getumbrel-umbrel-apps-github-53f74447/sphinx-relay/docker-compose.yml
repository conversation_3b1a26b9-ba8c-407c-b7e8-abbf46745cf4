version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: sphinx-relay_server_1
      APP_PORT: $APP_SPHINX_RELAY_PORT
      PROXY_AUTH_ADD: "false"

  server:
    image: sphinxlightning/sphinx-relay:v2.5.2@sha256:d37e2b72756cff670b759036fea6d4faad4cc10952256afd93f901ea36d652e2
    init: true
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
      - ${APP_DATA_DIR}/data:/relay/.lnd/
    environment:
      PUBLIC_URL: "$APP_HIDDEN_SERVICE:$APP_SPHINX_RELAY_PORT"
      LND_IP: $APP_LIGHTNING_NODE_IP
      CONNECT_UI: "true"
      LND_PORT: $APP_LIGHTNING_NODE_GRPC_PORT
      TLS_LOCATION: /lnd/tls.cert
      MACAROON_LOCATION: /lnd/data/chain/bitcoin/${APP_BITCOIN_NETWORK}/admin.macaroon
      LND_LOG_LOCATION: /lnd/logs/bitcoin/${APP_BITCOIN_NETWORK}/lnd.log