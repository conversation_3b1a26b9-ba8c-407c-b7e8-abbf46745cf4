manifestVersion: 1.1
id: sphinx-relay
category: social
name: Sphinx Relay
version: "2.5.2"
tagline: Chat and pay over the Lightning Network
description: >-
  Sphinx Relay turns your Lightning node into a personal
  communication server. Messages are end-to-end encrypted and transmitted over
  the Bitcoin Lightning Network. Download Sphinx on your phone from
  https://sphinx.chat and pair it with Sphinx Relay on Umbrel.


  Communication between Sphinx Relay nodes takes place entirely on the Lightning Network, so it is decentralized, untraceable, and encrypted. Messages are encrypted using client public keys on the Sphinx app.


  You can join tribes at https://tribes.sphinx.chat. If you join a podcast tribe, you can listen to the podcast in Sphinx and stream donations to the host.
developer: Stakwork
website: https://sphinx.chat
dependencies:
  - lightning
repo: https://github.com/stakwork/sphinx-relay
support: https://t.me/joinchat/Fb4OcRo4LrRD6NXfsNdeeQ
port: 3300
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: /connect
defaultUsername: ""
defaultPassword: ""
submitter: Stakwork
submission: https://github.com/getumbrel/umbrel/pull/341
releaseNotes: >-
  This release updates Sphinx Relay to version 2.5.2.


  Key improvements in this update include:
    - Enhanced invoice handling and payment processing
    - Improved MQTT subscription handling after network reconnection
    - Added threading functionality for messages
    - Implemented rate limiting for better performance
    - Improved direct messaging between users on the same relay
    - Enhanced spam protection measures
    - Various bug fixes and performance optimizations


  Full release notes and detailed information is available at https://github.com/stakwork/sphinx-relay/releases