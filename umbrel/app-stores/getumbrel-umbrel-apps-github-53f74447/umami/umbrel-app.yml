manifestVersion: 1
id: umami
name: Umami
tagline: Simple, fast, privacy-focused alternative to Google Analytics
category: developer
version: "2.18.1"
port: 25727
description: >-
  Empowering insights. Preserving privacy.


  Umami makes it easy to collect, analyze, and understand your web data — while maintaining visitor privacy and data ownership.


  ⚠️ Note

  Change the default password by clicking on the avatar in the top right corner and then 'profile'.

  You may need to expose Umami to the web in order to use the app in its intended way with websites.

  The easiest way to do this is to use the 'Cloudflare Tunnel' app from the umbrel app store, and expose the Umami app using your own domain.


  😌 Easy to use
  
  Umami is easy to use and understand without needing to run complicated reports. All data is available at a glance.


  💾 Own your data
  
  Data ownership is vitally important for maintinaing compliance with ever changing privacy laws. Rest assured knowing your data is in your control.


  🔒 Respect data privacy

  Umami lets you gather the data you need while respecting the privacy of your users. All data is anonymized and no personal data is ever collected. Cookie banners not required.
developer: Umami
website: https://umami.is
submitter: Sharknoon
submission: https://github.com/getumbrel/umbrel-apps/pull/1083
repo: https://github.com/umami-software/umami
support: https://github.com/umami-software/umami/discussions
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
defaultUsername: admin
defaultPassword: umami
dependencies: []
releaseNotes: >-
  This update introduces several new features and improvements:

    - New Attribution report to trace user source origin and measure ad campaign effectiveness
    - Distinct ID property for sessions to help identify users
    - New data-before-send attribute for the tracker to inspect and modify payload before sending
    - Events chart filtering by clicking on items in the events table
    - Option to use alternative hosts for icons with the FAVICON_URL environment variable


  ⚠️ Note: This release includes database migrations for handling UTM parameters. The migrations will apply the new schema automatically, but you may need to run a separate script to migrate old data.


  Full release notes are found at https://github.com/umami-software/umami/releases
path: "/dashboard"
