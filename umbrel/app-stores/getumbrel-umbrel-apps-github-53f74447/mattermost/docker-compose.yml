version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: mattermost_app_1
      APP_PORT: 8000
      PROXY_AUTH_ADD: "false"
  
  app:
    image: alexlack/mattermost-app:v10.9.1@sha256:46831e0a148144f27f59d2738ecf7ea3fc6191005419d49bf78ba246061ddd66
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    environment:
      MM_USERNAME: admin
      MM_PASSWORD: admin
      MM_DBNAME: mattermost
      MM_SQLSETTINGS_DATASOURCE: *******************************************/mattermost?sslmode=disable&connect_timeout=10
    volumes:
      - ${APP_DATA_DIR}/data/app/mattermost/config:/mattermost/config:rw
      - ${APP_DATA_DIR}/data/app/mattermost/data:/mattermost/data:rw
      - ${APP_DATA_DIR}/data/app/mattermost/logs:/mattermost/logs:rw
      - ${APP_DATA_DIR}/data/app/mattermost/plugins:/mattermost/plugins:rw
      - ${APP_DATA_DIR}/data/app/mattermost/client-plugins:/mattermost/client/plugins:rw
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "8443:8443/tcp" # WebRTC over tcp
      - "8443:8443/udp" # WebRTC over udp
    depends_on:
      db:
        condition: service_healthy

  db:
    image: alexlack/mattermost-db:v10.9.1@sha256:61b5e25aed287ae07c37a056586fc727d52b59e101a0acf3f8b6dc41da0bf6d8
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    environment:
      MM_USERNAME: admin
      MM_PASSWORD: admin
      MM_DBNAME: mattermost
    volumes:
      - ${APP_DATA_DIR}/data/db:/var/lib/postgresql/data
      - /etc/localtime:/etc/localtime:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
