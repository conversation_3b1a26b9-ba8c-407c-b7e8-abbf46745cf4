manifestVersion: 1
id: mattermost
name: Mattermost
tagline: Team Chat, Open Source, Self-Hosted
category: social
version: "10.9.1"
port: 8765
description: >-
  🚉 Mattermost is an open-source platform designed for secure collaboration throughout the software development lifecycle.


  💬 It serves as a team messaging application that emphasizes developer productivity and the benefits of open-source software.


  💾 Mattermost offers a self-hosted alternative to proprietary SaaS messaging services such as Slack and Microsoft Teams.


  Note: Mattermost can be used locally within your network. However, if you want others outside your local network to access your server, you'll need to make it accessible on the public internet.
  If you have a domain name, you can set this up using the Cloudflare Tunnel or Nginx Proxy Manager apps in the Umbrel Apps Store.
developer: Mattermost
website: https://mattermost.com
submitter: al-lac
submission: https://github.com/getumbrel/umbrel-apps/pull/1714
repo: https://github.com/mattermost/mattermost
support: https://forum.mattermost.com/
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
defaultUsername: ""
defaultPassword: ""
dependencies: []
releaseNotes: >-
  This release includes a number of new features, bug fixes and improvements.


  Key highlights include:
    - Fixed a bug where some messages didn't appear right away when opening the app
    - Improved performance when switching between teams and reconnecting after network changes
    - Redesigned channel settings into a single, easy-to-use menu
    - Added colorful banners for channels (admin feature)
    - Improved login, signup, and password reset screens with better titles and layout
    - Made the app faster and smoother when typing drafts
    - Upgraded support for the latest versions of Chrome, Edge, and Firefox
    - Enhanced support for screen readers and keyboard navigation
    - Fixed layout issues with emojis, image previews, and tooltips
    - Added new admin tools for customizing user access and automating channel membership
    - Improved issue reporting options in settings
    - General performance improvements and bug fixes across the app


  Full release notes are available at https://docs.mattermost.com/about/mattermost-v10-changelog.html
path: ""
