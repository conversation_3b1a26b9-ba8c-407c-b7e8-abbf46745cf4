version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: $APP_BITCOIN_KNOTS_IP
      APP_PORT: 3005
  
  server:
    image: ghcr.io/retropex/umbrel-bitcoin-knots:v0.7.1@sha256:bf938e6448f89c9ec03a0fa049442f3a191718f9baa12ecef0fce3dc41ad6698
    depends_on: [bitcoind]
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/app:/data # volume to persist advanced settings json
      - ${APP_BITCOIN_KNOTS_DATA_DIR}:/bitcoin/.bitcoin # volume to persist umbrel-bitcoin.conf and bitcoin.conf
    environment:
      PORT: "3005"
      BITCOIN_HOST: "${APP_BITCOIN_KNOTS_NODE_IP}"
      # used to connect to internal bitcoind port
      RPC_PORT: "${APP_BITCOIN_KNOTS_INTERNAL_RPC_PORT}"
      # displayed to user in connection details for external connections
      BITCOIN_RPC_PORT: "${APP_BITCOIN_KNOTS_RPC_PORT}"
      RPC_USER: "${APP_BITCOIN_KNOTS_RPC_USER}"
      BITCOIN_RPC_USER: "${APP_BITCOIN_KNOTS_RPC_USER}"
      RPC_PASSWORD: "${APP_BITCOIN_KNOTS_RPC_PASS}"
      BITCOIN_RPC_PASSWORD: "${APP_BITCOIN_KNOTS_RPC_PASS}"
      BITCOIN_RPC_HIDDEN_SERVICE: "${APP_BITCOIN_KNOTS_RPC_HIDDEN_SERVICE}"
      BITCOIN_P2P_HIDDEN_SERVICE: "${APP_BITCOIN_KNOTS_P2P_HIDDEN_SERVICE}"
      # displayed to user in connection details for external connections
      BITCOIN_P2P_PORT: "${APP_BITCOIN_KNOTS_P2P_PORT}"
      DEVICE_DOMAIN_NAME: "${DEVICE_DOMAIN_NAME}"
      BITCOIN_DEFAULT_NETWORK: "${BITCOIN_KNOTS_DEFAULT_NETWORK:-mainnet}"
      # not needed
      BITCOIN_INITIALIZE_WITH_CLEARNET_OVER_TOR: "${BITCOIN_INITIALIZE_WITH_CLEARNET_OVER_TOR:-unset}"
      BITCOIND_IP: "${APP_BITCOIN_KNOTS_NODE_IP}"
      TOR_PROXY_IP: "${APP_BITCOIN_KNOTS_TOR_PROXY_IP}"
      TOR_PROXY_PORT: "9050"
      TOR_PROXY_CONTROL_PORT: "9051"
      TOR_PROXY_CONTROL_PASSWORD: "moneyprintergobrrr"
      I2P_DAEMON_IP: "${APP_BITCOIN_KNOTS_I2P_DAEMON_IP}"
      I2P_DAEMON_PORT: "7656"
    networks:
      default:
        ipv4_address: $APP_BITCOIN_KNOTS_IP

  bitcoind:
    image: ghcr.io/retropex/bitcoin:v28.1@sha256:9dab3c9e0e21bf17ae7e624e1a3fabc51dff812ee8382fb10f8ebdf6e0af5801
    user: "1000:1000"
    command: "${APP_BITCOIN_KNOTS_COMMAND}"
    restart: unless-stopped
    stop_grace_period: 15m30s
    volumes:
      - "${APP_BITCOIN_KNOTS_DATA_DIR}:/data/.bitcoin"
    ports:
      - "${APP_BITCOIN_KNOTS_P2P_PORT}:${APP_BITCOIN_KNOTS_INTERNAL_P2P_PORT}"
      - "${APP_BITCOIN_KNOTS_RPC_PORT}:${APP_BITCOIN_KNOTS_INTERNAL_RPC_PORT}"
    networks:
      default:
        ipv4_address: $APP_BITCOIN_KNOTS_NODE_IP

  tor:
    image: getumbrel/tor:*******@sha256:2ace83f22501f58857fa9b403009f595137fa2e7986c4fda79d82a8119072b6a
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/torrc:/etc/tor/torrc:ro
      - ${TOR_DATA_DIR}:/data
    environment:
      HOME: "/tmp"
    networks:
      default:
        ipv4_address: "${APP_BITCOIN_KNOTS_TOR_PROXY_IP}"
  
  i2pd_daemon:
    image: purplei2p/i2pd:release-2.44.0@sha256:d154a599793c393cf9c91f8549ba7ece0bb40e5728e1813aa6dd4c210aa606f6
    user: "root"
    command: --sam.enabled=true --sam.address=0.0.0.0 --sam.port=7656 --loglevel=error
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/i2pd:/home/<USER>/data
    networks:
      default:
        ipv4_address: "${APP_BITCOIN_KNOTS_I2P_DAEMON_IP}"
