version: "3.7"

services:
   app_proxy:
      environment:
         APP_HOST: minio_web_1
         APP_PORT: 9011
         PROXY_AUTH_ADD: "false"

   web:
      image: quay.io/minio/minio:RELEASE.2025-06-13T11-33-47Z@sha256:064117214caceaa8d8a90ef7caa58f2b2aeb316b5156afe9ee8da5b4d83e12c8
      restart: on-failure
      user: "1000:1000"
      stop_grace_period: 1m
      ports:
         # MinIO Api Server Port
         - "9010:9000"
      command: ["server", "/data"]
      volumes:
         - ${APP_DATA_DIR}/data/minio:/data
      environment:
         MINIO_ROOT_USER: "umbrel"
         MINIO_ROOT_PASSWORD: "$APP_PASSWORD"
         MINIO_CONSOLE_ADDRESS: ":9011"
