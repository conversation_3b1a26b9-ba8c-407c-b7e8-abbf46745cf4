manifestVersion: 1
id: minio
category: developer
name: MinIO
version: "RELEASE.2025-06-13T11-33-47Z"
tagline: The Object Store for AI Data Infrastructure
description: >-
   MinIO is a high-performance, S3 compatible object store. It is built for
   large scale AI/ML, data lake and database workloads. It is software-defined
   and runs on any cloud or on-premises infrastructure. MinIO is dual-licensed
   under open source GNU AGPL v3 and a commercial enterprise license.


   Understand the Features that Make MinIO the Industry's Most Widely Deployed Object Store:

     - Bucket & Object Immutability
     - Encryption
     - Active Active Replication for Object Storage
     - Automated Data Management Interfaces
     - Object Storage Built for S3
     - Data Life Cycle Management & Tiering
     - Scalable Object Storage
     - Identity & Access Management

developer: "MinIO, Inc."
submitter: maipal-c
submission: https://github.com/getumbrel/umbrel-apps/pull/982
website: https://min.io
dependencies: []
repo: https://github.com/minio/minio
support: https://slack.min.io
port: 9011
gallery:
   - 1.jpg
   - 2.jpg
   - 3.jpg
path: ""
deterministicPassword: true
defaultUsername: umbrel
releaseNotes: >-
  This release includes improvements to system performance and reliability:
    - Fixed ILM-related S3 listing and upload issues
    - Improved replication metrics and region config
    - Enhanced network policy and job egress control
    - Minor code modernizations and stability fixes


  Full release notes are found at https://github.com/minio/minio/releases
