manifestVersion: 1
id: ntfy
name: ntfy
tagline: Send notifications to your phone or desktop via scripts from any computer, and/or using a REST API
category: automation
version: "2.12.0"
port: 13119
description: >-
  ntfy (pronounced notify) is a simple HTTP-based pub-sub notification service. It allows you to send notifications to
  your phone or desktop via scripts from any computer, and/or using a REST API.


  ℹ️ iOS Notifications

  Apple's restrictions require a publicly reachable server for instant notifications on iOS. We use https://ntfy.sh for poll
  requests by default. This doesn't send your notifications through this service, but instead only prompts your iOS device to
  download the notification from your local ntfy instance. Advanced users can remove the "NTFY_UPSTREAM_BASE_URL" environment variable from the Docker compose file if desired; however, this will disable instant notifications on iOS devices.


  🔔 Send push notifications from your app or script

  Publishing messages can be done via PUT or POST. Topics are created on the fly by subscribing or publishing to them.
  If you use ntfy without sign-up, the topic is essentially a password, so pick something that's not easily guessable.


  📱 Receive notifications on your phone

  Subscribe to a topic and receive notifications, with different priorities, attachments, action buttons, tags & emojis,
  and even for automation.


  💻 Get notified on your computer

  You can use the web app to subscribe to topics as well. If you do, notifications will pop up as desktop notifications.
  Simply type in the topic name and click the Subscribe button. The browser will keep a connection open and listen for
  incoming notifications.


  ⚙️ Integrate with your favorite tools

  Publishing messages is just a HTTP request, so pretty much everything under the sun can integrate with ntfy. Hundreds
  of integrations, projects or scripts already support ntfy.

developer: ntfy LLC
website: https://ntfy.sh/
submitter: Sharknoon
submission: https://github.com/getumbrel/umbrel-apps/pull/<number>
repo: https://github.com/binwiederhier/ntfy
support: https://github.com/binwiederhier/ntfy/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  This release includes new features and bug fixes:

    - Added username/password authentication for email publishing
    - Introduced a 'latest' subscription parameter to retrieve the most recent message
    - Added support for changing passwords via the v1/users API
    - Increased the default WebPush subscription warning/expiry to 55/60 days
    - Added support for systemd user service 'ntfy-client.service'
    - Fixed iOS delivery issues for read-protected topics
    - Improved IP address parsing when behind a proxy
    - Made Markdown in the web app scrollable horizontally
    - Increased the number of access tokens per user to 60

  Full release notes can be found at https://github.com/binwiederhier/ntfy/releases
dependencies: []
path: ""
defaultUsername: umbrel
deterministicPassword: true
