version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: photoprism_web_1
      APP_PORT: 8087
      # These endpoints are protected by HTTP Basic Auth by Photoprism.
      # More details here: https://docs.photoprism.app/user-guide/sync/webdav/#server-url
      PROXY_AUTH_WHITELIST: "/originals/*,/import/*"
  
  web:
    image: photoprism/photoprism:250426@sha256:19fdf0d52676d07abe63376a3b2d07779cac010a1bfca563af11968abd2df218
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: "1m"
    volumes:
      - "${APP_DATA_DIR}/originals:/photoprism/originals"
      - "${APP_DATA_DIR}/storage:/photoprism/storage"
    environment:
      PHOTOPRISM_ADMIN_PASSWORD: "${APP_PASSWORD}"
      PHOTOPRISM_ORIGINALS_LIMIT: 10000
      PHOTOPRISM_HTTP_COMPRESSION: "gzip"
      PHOTOPRISM_HTTP_PORT: 8087
      PHOTOPRISM_DATABASE_DRIVER: "mysql"
      PHOTOPRISM_DATABASE_SERVER: "photoprism_db_1:3306"
      PHOTOPRISM_DATABASE_NAME: "photoprism"
      PHOTOPRISM_DATABASE_USER: "photoprism"
      PHOTOPRISM_DATABASE_PASSWORD: "photoprism"
      PHOTOPRISM_SITE_URL: "http://${APP_DOMAIN}:8087/"
      PHOTOPRISM_SITE_TITLE: "PhotoPrism"
      PHOTOPRISM_SITE_CAPTION: "Digital Asset Management"
      PHOTOPRISM_SITE_DESCRIPTION: ""
      PHOTOPRISM_SITE_AUTHOR: ""

  db:
    image: mariadb:10.5.12@sha256:dfcba5641bdbfd7cbf5b07eeed707e6a3672f46823695a0d3aba2e49bbd9b1dd
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: "1m"
    command: mysqld --transaction-isolation=READ-COMMITTED --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --max-connections=512 --innodb-rollback-on-timeout=OFF --innodb-lock-wait-timeout=50
    volumes:
      - "${APP_DATA_DIR}/database:/var/lib/mysql"
    environment:
      MYSQL_ROOT_PASSWORD: "photoprism"
      MYSQL_DATABASE: "photoprism"
      MYSQL_USER: "photoprism"
      MYSQL_PASSWORD: "photoprism"
