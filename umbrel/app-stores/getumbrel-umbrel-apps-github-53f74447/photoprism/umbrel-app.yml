manifestVersion: 1
id: photoprism
category: files
name: PhotoPrism
version: "250426-27ec7a128"
tagline: Self-host your photo and video library
description: >-
  PhotoPrism® is a privately hosted app for browsing, organizing, and
  sharing your photo collection. It makes use of the latest technologies to tag
  and find pictures automatically without getting in your way. Say goodbye to
  solutions that force you to upload your visual memories to the cloud!


  - Our intuitive user interface makes browsing and organizing your photo collection as easy as it should be — whether it's on a phone, tablet, or desktop computer.


  - Index everything without worrying about duplicates or RAW to JPEG conversion.


  - Automatic image classification based on Google TensorFlow. In addition, our indexer detects colors, chroma, luminance, quality, panoramic projection, location type, and many other properties.


  - Includes four high-resolution world maps to see where you've been, and for rediscovering long-forgotten shots.


  - WebDAV clients, like Microsoft's Windows Explorer or Apple's Finder, may connect directly to PhotoPrism so that you to can open, edit, and delete files from your computer or phone as if they were local. You may easily sync your pictures with PhotoSync as well.


  - Because PhotoPrism is built as a progressive web app, it provides a native app-like experience, and you may install it on your home screen. There's also a community-maintained native app in development.
developer: PhotoPrism
website: https://photoprism.app
dependencies: []
repo: https://github.com/photoprism/photoprism
support: https://gitter.im/browseyourlife/community
port: 8087
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: "admin"
deterministicPassword: true
torOnly: false
releaseNotes: >-
  ⚠️ It is recommended to perform a complete rescan of your library after upgrading to ensure optimal performance with video files.


  Key features:
    - Upgraded TensorFlow to v2.18.0
    - Vision API supports remote instances/models
    - Reduced frontend size by 54%
    - Asynchronous updates for better backend performance
    - Added arrow key navigation, keyboard shortcuts, and focus management
    - Fixed search result view resets and added UI language change option
    - Improved manual tagging and search filtering
    - Enhanced viewer with collapsible sidebar and new actions (Album Cover, Archive)
    - Transcoding optimized for streaming with improved MP4 support
    - Added zip archive upload support and restricted web upload sizes
    - WebDAV service discovery improved with Depth header
    - Docker upgraded to Ubuntu 25.04 with updated tools
    - Security updates with Go v1.24.2
    - Preinstalled Linux Video Acceleration API (VAAPI) drivers
    - Improved hardware driver support for Intel QSV
    - Updated translations for Arabic, French, and German


  Full release notes can be found at: https://github.com/photoprism/photoprism/releases/
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/commit/bf4c6f127f1fcf4dbe8eea55729502dfb34278e9
