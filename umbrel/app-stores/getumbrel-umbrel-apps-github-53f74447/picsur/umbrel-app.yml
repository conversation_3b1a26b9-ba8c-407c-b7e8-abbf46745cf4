manifestVersion: 1
id: picsur
name: Pi<PERSON>ur
tagline: An easy to use image sharing service
category: files
version: "0.5.6"
port: 8286
description: >-
  🖼️ Picsur is a lightweight, self-hosted image hosting platform designed for simplicity and control. It allows users to upload, store, and share images directly from their own server, without relying on third-party services. Inspired by the functionality of platforms like Imgur and Pastebin, Picsur focuses on offering a clean and efficient experience for users who need a straightforward way to manage images online.


  Unlike more complex photo management solutions such as PhotoPrism or Immich, which include features like facial recognition, albums, and AI-assisted sorting, Picsur is purpose-built for quick uploads and direct sharing. It supports a wide range of image formats, including common ones like JPEG, PNG, and GIF, as well as newer or less standard types such as QOI and animated WebP files. Once uploaded, images can be edited with basic tools such as resizing, rotating, flipping, or applying grayscale and negative effects.


  The platform includes a user-friendly web interface that supports drag-and-drop uploads and provides direct links for sharing images instantly. Administrators can access a backend panel to manage uploaded files and user permissions, offering full control over how the service operates.


  Because Picsur is self-hosted, it ensures maximum privacy and independence from cloud providers. This makes it an ideal solution for individuals or small teams looking to host images privately, efficiently, and with minimal overhead.
developer: CaramelFur
website: https://picsur.org/upload
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2589
repo: https://github.com/CaramelFur/Picsur
support: https://github.com/CaramelFur/Picsur/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
releaseNotes: ""
dependencies: []
path: ""
defaultUsername: "admin"
deterministicPassword: true
