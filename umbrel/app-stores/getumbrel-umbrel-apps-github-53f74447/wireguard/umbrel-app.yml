manifestVersion: 1
id: wireguard
name: WireGuard
tagline: Run your own free and unlimited WireGuard VPN
category: networking
version: "14"
port: 51821
description: >-
  WireGuard is an extremely simple yet fast and modern VPN that utilizes state-of-the-art cryptography.
  
  
  It aims to be faster, simpler, leaner, and more useful than IPsec, while avoiding the massive headache. It intends to be considerably more performant than OpenVPN.
  
  
  WireGuard is designed as a general purpose VPN for running on embedded interfaces and super computers alike, fit for many different circumstances.


  ☺️ Simple & Easy-to-use
  
  WireGuard is as easy to set up. It handles VPN connections with simple public key exchanges and manages connections automatically.
  
  
  🔐 Cryptographically Sound
  
  WireGuard uses state-of-the-art cryptography like the Noise protocol framework, Curve25519, ChaCha20, and others. It's been reviewed by cryptographers.
  

  🛡️ Minimal Attack Surface
  
  WireGuard is designed for simplicity and ease of implementation, making it easily auditable for security vulnerabilities.
  
  
  ⏱️ High Performance
  
  Thanks to high-speed cryptographic primitives and its integration with the Linux kernel, WireGuard ensures fast, secure networking.
  
  
  📐 Well Defined & Thoroughly Considered
  
  WireGuard is the product of careful academic consideration, with a clearly defined protocol and well-considered design decisions.


  ℹ️ Access from outside the local network
  
  To access WireGuard from outside the local network, you need to open up the port 51820/udp on your router and forward it to your Umbrel device.

  If you scan the QR code or download the configuration file, make sure to change the hostname to your device's domain name or public IP address.
releaseNotes: >-
  This release upgrades WireGuard Easy to version 14.
dependencies: []
developer: Emile Nijssen
website: https://github.com/wg-easy/wg-easy
submitter: Sharknoon
submission: https://github.com/getumbrel/umbrel-apps/pull/1084
repo: https://github.com/wg-easy/wg-easy
support: https://github.com/wg-easy/wg-easy/discussions
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""