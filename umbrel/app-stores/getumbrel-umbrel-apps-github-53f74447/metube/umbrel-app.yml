manifestVersion: 1
id: metube
category: media
name: MeTube
version: "2025-06-10"
tagline: Download videos from YouTube
description: >-
  Download videos from YouTube (and dozens of other sites) directly to your Umbrel.
developer: <PERSON>
dependencies: []
repo: https://github.com/alexta69/metube
port: 3700
releaseNotes: >-
  This release contains bug fixes and improvements.


  Highlights:
    - Upgraded underlying video downloader for better compatibility
    - Added download totals display at the top of the interface
    - Added version information display
    - Fixed download folder selection dropdown
    - Upgraded to Angular 19 for improved performance
    - UI cleanup and reorganization for better user experience
    - Added support for custom directory exclusion patterns
    - Fixed download issues with impersonation features
support: https://github.com/alexta69/metube/issues
website: https://github.com/alexta69/metube
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: MontejoJorge
submission: https://github.com/getumbrel/umbrel-apps/pull/864
