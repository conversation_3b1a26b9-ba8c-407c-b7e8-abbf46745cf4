manifestVersion: 1
id: portainer
category: developer
name: Portainer
version: "2.27.8"
tagline: Run custom Docker containers on your Umbrel
description: >-
  ⚠️ Make sure to only use named Docker volumes for your stacks and containers. Data in bind-mounted volumes
  will be lost when the Portainer app is restarted or updated.


  ⚠️ Watch out for port conflicts between your custom Docker containers and your umbrelOS apps.


  Portainer is the ultimate Docker management solution that simplifies running Docker containers and Docker Compose
  setups on your Umbrel, putting comprehensive control at your fingertips.


  Portainer provides seamless container management, allowing you to efficiently monitor, start, stop, and
  modify containers, networks, volumes, and images. You can also deploy multi-container applications using Docker Compose
  with ease.


  🛠️ Portainer on Umbrel is for power users, follow these best practices to avoid issues:


  1. Data persistence: Make sure to only used named Docker volumes for your stacks and containers. Data in bind-mounted
  volumes will be lost when the Portainer app is restarted or updated.


  2. Port management: Watch out for potential port conflicts between your custom containers and umbrelOS' service containers,
  apps you have installed from the Umbrel App Store or Community App Stores, and any apps you go to install in the future.


  3. Container restart policy: Set your containers to "unless-stopped" or "always" restart policies. This will allow your containers
  to restart automatically when the Portainer app is restarted or updated.


  4. Web access to containers: Access your custom containers in your browser at umbrel.local:PORT_NUMBER. For example, for a container
  with a web UI running on port 4545, navigate to umbrel.local:4545 to access it.
website: https://portainer.io
dependencies: []
repo: https://github.com/portainer/portainer
support: https://github.com/portainer/portainer/issues
port: 9000
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: "admin"
defaultPassword: "changeme"
releaseNotes: >-
  This update includes security fixes and improvements:


    - Fixed an issue where the Kubernetes client cache was not cleared and created correctly per user
    - Fixed an issue where Kubernetes namespace access changes were not reflected for API users after sign-in
    - Resolved several security vulnerabilities


  Full release notes are found at https://github.com/portainer/portainer/releases.
developer: Portainer
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/774
