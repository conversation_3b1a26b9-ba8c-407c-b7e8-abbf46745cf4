version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: wikijs_server_1
      APP_PORT: 3000
      PROXY_AUTH_ADD: "false"
  
  server:
    user: "1000:1000"
    image: linuxserver/wikijs:2.5.307@sha256:b76681d67c25c15dca2cbf010d3e71ea5a5951fdef7115d085231836ea943f9c
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data:/data
      - ${APP_DATA_DIR}/repo:/app/wiki/data/repo
      - ${APP_DATA_DIR}/config:/config
