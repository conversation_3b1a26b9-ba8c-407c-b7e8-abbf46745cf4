manifestVersion: 1
id: ghost
name: Ghost
tagline: A platform for creators to publish, grow, and monetize their content
category: files
version: "5.128.0"
port: 3368
description: >-
  Ghost is a powerful, open-source content management system (CMS) primarily designed for professional bloggers, journalists, and content creators. Unlike more traditional CMS platforms like WordPress, which offer a wide range of plugins and customization options, Ghost focuses on providing a streamlined, fast, and minimalist platform that prioritizes the writing and publishing experience. It was initially created with bloggers in mind, but over time, it has evolved to include more features such as membership management, email newsletters, and monetization tools.


  The platform is known for its speed and performance, ensuring fast load times for websites. This is important for improving user experience and search engine optimization (SEO). Ghost's user interface is designed to be clean, intuitive, and distraction-free, allowing creators to focus solely on their content without getting bogged down by unnecessary options or settings.


  One of the standout features of Ghost is its membership and subscription functionality. Content creators can monetize their work by offering paid subscriptions to readers, providing them with exclusive content or newsletters. This makes <PERSON> an ideal choice for those looking to build a business around their content, such as premium blogs or news outlets.


  Although Ghost is designed to be simple and straightforward, it also offers customization options for developers. Users can modify themes, add custom code, or integrate third-party services, allowing them to tailor the platform to meet their needs. Additionally, Ghost includes built-in SEO features, such as customizable meta descriptions, clean URLs, and a focus on fast, responsive designs, all of which contribute to better search engine rankings.


  Overall, <PERSON> is a highly efficient and user-friendly platform for content creation and publishing. Its focus on speed, simplicity, and monetization makes it an excellent option for anyone who wants to create a professional online presence without the complexity of other CMS platforms.


  ⚠️ Your published website will be available at "http://umbrel.local:3368". The easiest way to expose your site to the public internet is to use the 'Cloudflare Tunnel' app from the Umbrel app store, and tunnel to your own domain.
developer: Ghost Foundation Ltd
website: https://ghost.org/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2178
repo: https://github.com/TryGhost/Ghost
support: https://github.com/TryGhost/Ghost/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
  - 6.jpg
  - 7.jpg
releaseNotes: >-
  This release of Ghost includes the following updates:
    - Fixed infinite `/offers/` fetch loop for sites with >100 offers
    - Fixed "Something went wrong" errors when activating themes
    - Fixed LimitModal to support HTML content in prompts
    - Added Hindi translations for the newsletter
    - Updated Hungarian translations for Ghost, Comments, and Portal


  Full release notes can be found at https://github.com/TryGhost/Ghost/releases
dependencies: []
path: "/ghost"
defaultUsername: ""
defaultPassword: ""
