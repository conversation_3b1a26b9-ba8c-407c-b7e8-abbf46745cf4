manifestVersion: 1.1
id: electrumx
implements:
  - electrs
category: bitcoin
name: ElectrumX
version: "1.16.0"
tagline: A lightweight Electrum server
description: >
  Run your personal Electrum server and connect your Electrum-compatible wallet,
  including BitBoxApp, BlueWallet, Electrum Wallet (Android and Desktop), Nunchuk 
  (Desktop), Phoenix, and Sparrow Wallet to it instead of using a third-party
  Electrum server.


  Powered by ElectrumX from Thomas <PERSON>.


  An official app from Umbrel.
developer: Umbrel
website: https://umbrel.com/
dependencies:
  - bitcoin
repo: https://github.com/getumbrel/umbrel-electrumx
support: https://community.getumbrel.com/c/bitcoin-and-lightning
port: 1999
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultPassword: ""
releaseNotes: ""
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/pull/1813
