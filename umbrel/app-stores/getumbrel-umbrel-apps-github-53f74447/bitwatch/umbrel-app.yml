manifestVersion: 1
id: bitwatch
category: bitcoin
name: Bitwatch
version: "1.7.1"
tagline: Monitor Bitcoin addresses in real-time
description: >-
  Monitor Bitcoin addresses in the mempool and on-chain using the mempool.space API and websocket.


  Track balance changes, get telegram notifications, and manage expectations for multiple addresses.


  Features:
    - Build list of addresses in collections to sum-up balances accross wallets
    - Subscribes to mempool.space (or locally hosted) websocket for real-time mempool activity
    - Double checks data against mempool.space (or locally hosted) API on a configured interval
    - Track both on-chain and mempool activity
    - Configure auto-acceptance or alert mode of changes (chain_in, chain_out, mempool_in, mempool_out)
      - by default, incoming transactions are auto-accepted, outgoing transactions are alerted
      - all activity is alerted to a configured telegram connection once (even when auto-accepted)
    - Option to use your own local node for privacy


  Recommended to have Mempool + Fulcrum to run privately but not required.
developer: Zap-O-Matic
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  Telegram healthcheck, Performance improvements, and remembers configs:
    - saves bitcoin/satoshi viewing preference to local storage
    - adds a healthcheck for telegram connection to prevent loss of telegram notifications
    - removes excess UI console logs that were causing UI performance issues

website: https://github.com/zapomatic/bitwatch
repo: https://github.com/zapomatic/bitwatch
support: https://github.com/zapomatic/bitwatch/issues
dependencies: []
port: 3117
path: ""
submitter: Zap-O-Matic
submission: https://github.com/getumbrel/umbrel-apps/pull/2410
