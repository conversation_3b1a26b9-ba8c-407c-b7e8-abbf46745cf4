version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: teamspeak_ui_1
      APP_PORT: 8080
  
  ui:
    image: joni1802/ts3-manager:v2.2.1@sha256:d69890c323e8715cef0f625036c7c51ec7e7da321438897633fff5df83da1e39
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    environment:
      JWT_SECRET: $APP_SEED

  server:
    image: teamspeak:3.13.7@sha256:b5bdec55bc87b992c5658e739f3ce465644efa6d997633110b12be3dc3a10796
    # TeamSpeak does not support running with a different user
    #user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    ports:
      - 9987:9987/udp
      - 10011:10011
      - 30033:30033
    volumes:
      - ${APP_DATA_DIR}/data:/var/ts3server
    environment:
      TS3SERVER_LICENSE: accept
      TS3SERVER_SERVERADMIN_PASSWORD: $APP_PASSWORD