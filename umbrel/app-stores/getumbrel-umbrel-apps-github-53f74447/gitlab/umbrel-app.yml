manifestVersion: 1.1
id: gitlab
name: GitLab
tagline: Software. Faster.
category: developer
version: "18.0.2"
port: 8929
description: >-
  ⚠️ This app is RAM-intensive (+6GB recommended) and can take 15-20 minutes to start after installation on low-powered devices.


  🔐 After installing the App, you can create a new user account via web interface, or you can connect via SSH/Terminal and change the `root` password by running the `gitlab-rake "gitlab:password:reset[root]"` command


  GitLab is a comprehensive, web-based DevOps lifecycle tool that provides a robust platform for managing projects and repositories. It offers a wide range of features that cater to the entire software development lifecycle, including version control, CI/CD, code review, issue tracking, and more. All operations are performed in a secure, collaborative environment, ensuring your code and data are safe and accessible.

  
  📁 Repository Management
  
  GitLab provides a full-featured GUI for creating, managing, and organizing repositories. You can clone, fork, and browse repositories with ease. Advanced features like branch management, tagging, and merging are also available, enabling efficient version control and collaboration.
  
  
  🔄️ Continuous Integration/Continuous Deployment (CI/CD)
  
  Automate your software development process with GitLab's powerful CI/CD pipelines. Easily configure builds, tests, and deployments to streamline your workflow. GitLab CI/CD supports multiple languages and frameworks, ensuring compatibility with your existing tools and processes.
  
  
  🛡️ Security & Compliance
  
  Ensure the security and compliance of your projects with GitLab's built-in security features. Perform static and dynamic application security testing (SAST/DAST), dependency scanning, container scanning, and more. GitLab also offers audit logs, compliance management, and code quality checks to maintain high standards.
  
  
  👥 Collaboration & Code Review
  
  Enhance team collaboration with GitLab's integrated code review and discussion tools. Perform peer reviews, leave comments, and suggest changes directly within the platform. GitLab's merge request system simplifies the code review process, making it easy to propose, discuss, and approve changes.
  
  
  📈 Project Management
  
  Manage your projects efficiently with GitLab's project management features. Track issues, plan sprints, and visualize progress with boards, milestones, and burndown charts. GitLab also integrates with popular project management tools, allowing seamless synchronization of tasks and updates.
  
  
  🛠️ Other Features:
  
  Leverage GitLab's extensive set of tools to optimize your development process. Use the integrated wiki for documentation, manage packages with GitLab's built-in package registry, monitor performance with Prometheus and Grafana integration, and deploy applications with Kubernetes support. GitLab also offers robust APIs for extending functionality and integrating with other services.
  
  
  GitLab is your all-in-one DevOps platform, empowering your team to build, deploy, and manage projects with ease and efficiency.

developer: GitLab
website: https://about.gitlab.com
submitter: hugofnm
submission: https://github.com/getumbrel/umbrel-apps/pull/1290
repo: https://github.com/zengxs/gitlab-arm64
support: https://github.com/zengxs/gitlab-arm64/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  🚨 This version contains important security fixes and we strongly recommend updating immediately.


  The GitLab UI may be unresponsive for a few minutes after the update, which is completely normal.


  Key improvements in this release:
    - Multiple security vulnerabilities have been fixed
    - Improved stability and performance
    - Various bug fixes and enhancements

  Full release notes are available at: https://about.gitlab.com/releases/2025/05/15/gitlab-18-0-released/
dependencies: []
path: ""
deterministicPassword: true
defaultUsername: "root"
