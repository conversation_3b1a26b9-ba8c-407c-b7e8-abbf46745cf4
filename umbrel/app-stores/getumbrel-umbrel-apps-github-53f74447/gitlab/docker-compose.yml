version: "3.7"

services:

  app_proxy:
    environment:
      APP_HOST: gitlab_gitlab_1
      APP_PORT: 8929
      PROXY_AUTH_ADD: "false"

  gitlab:
    image: zengxs/gitlab:18.0.2-ce.0@sha256:107ee285aea892fa6ed8e039df8a6d850aa5d3469247e9783dcf765789b70872
    restart: on-failure
    hostname: '${DEVICE_DOMAIN_NAME}:8929'
    ulimits:
      nofile:
        soft: 4096
        hard: 8192
    environment:
      PUID: 1000
      PGID: 1000
      GITLAB_OMNIBUS_CONFIG: |
        # Add any other gitlab.rb configuration here, each on its own line
        gitlab_rails['initial_root_password'] = '${APP_PASSWORD}'
        gitlab_rails['gitlab_shell_ssh_port'] = 2424
        nginx['listen_port'] = 8929
        nginx['listen_https'] = false
    ports:
      - '2424:22'
    volumes:
      - ${APP_DATA_DIR}/data/config:/etc/gitlab
      - ${APP_DATA_DIR}/data/logs:/var/log/gitlab
      - ${APP_DATA_DIR}/data/data:/var/opt/gitlab
    shm_size: '256m'
