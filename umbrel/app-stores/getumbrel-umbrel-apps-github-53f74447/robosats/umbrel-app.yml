manifestVersion: 1
id: robosats
category: bitcoin
name: RoboSats
version: "v0.7.9-alpha"
tagline: Simple and Private Bitcoin P2P Exchange
description: >-
  RoboSats is a simple and private app to exchange bitcoin for national currencies. 
  Robosats simplifies the P2P user experience and uses lightning hold invoices to 
  minimize custody and trust requirements. The deterministically generated robot 
  avatars help users stick to best privacy practices.


  Features:


  - Privacy focused: your robot avatar is deterministically generated, no need for registration.

  - More than 10 languages available and over 60 fiat currencies

  - Safe: simply lock a lightning hodl invoice and show you are real and committed.

  - No data collection. Your communication with your peer is PGP encrypted, only you can read it.

  - Lightning fast: the average sovereign trade finishes in ~ 8 minutes. Faster than a single block confirmation!

  - Fully collateralized escrow: your peer is always committed and cannot run away with the funds.

  - Strong incentives system: attempts of cheating are penalized with the slashing of the Sats in the fidelity bond.

  - Guides and video tutorials available at https://learn.robosats.com/watch/en


  You can join other cool Robots and get community support at https://t.me/robosats telegram group.
  
releaseNotes: >-
  Highlights:
    - Orders can be created now with a password (Advance settings). These orders won't appear in the public orders view and can be taken only if the robot inputs the same password.
    - Orders can be fetch now by URL in the Book view
    - New coordinator: FreedomSats
    - Peach orders now available in Global orders book
    - Chat fix

  Full release notes are available at https://github.com/RoboSats/robosats/releases/tag/v0.7.9-alpha
developer: RoboSats
website: https://learn.robosats.com
dependencies: []
repo: https://github.com/reckless-satoshi/robosats
support: https://t.me/robosats
port: 12596
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: RoboSats
submission: https://github.com/getumbrel/umbrel-apps/pull/122