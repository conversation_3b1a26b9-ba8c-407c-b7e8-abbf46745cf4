manifestVersion: 1
id: memos
category: files
name: Memos
version: "0.24.4"
tagline: Easily capture and share your great thoughts
description: >-
  A privacy-first, lightweight note-taking service.
  Easily capture and share your great thoughts.

  - Open source and free forever. Embrace a future where creativity knows no boundaries with our open-source solution – free today, tomorrow, and always.

  - Self-hosting with <PERSON><PERSON> in just seconds. Enjoy the flexibility, scalability, and ease of setup that Docker provides, allowing you to have full control over your data and privacy.

  - Pure text with added Markdown support. Say goodbye to the overwhelming mental burden of rich formatting and embrace a minimalist approach.

  - Customize and share your notes effortlessly. With our intuitive sharing features, you can easily collaborate and distribute your notes with others.

  - RESTful API for third-party services. Embrace the power of integration and unleash new possibilities with our RESTful API support.
releaseNotes: >-
  New features and improvements:
    - Automatically add a new table row in the editor when pressing enter
    - Add infinite scrolling for memos
    - Make the save button disabled after a successful update
    - Use server title and description for RSS feed, if configured
    - Inherits memo visibility for default comment visibility
    - Refactor memo editor


  Full release notes are available at https://github.com/usememos/memos/releases
developer: Memos
website: https://www.usememos.com/
dependencies: []
repo: https://github.com/usememos/memos
support: https://github.com/usememos/memos/discussions
port: 5230
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: highghlow
submission: https://github.com/getumbrel/umbrel/pull/1043
