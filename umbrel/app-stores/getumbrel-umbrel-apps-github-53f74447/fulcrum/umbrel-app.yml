manifestVersion: 1.1
id: fulcrum
implements:
  - electrs
category: bitcoin
name: Fulcrum
version: "1.12.0-graceful-shutdown"
tagline: A fast and nimble Electrum Server
description: >-
  Run your personal Electrum server and connect your Electrum-compatible wallet,
  including BitBoxApp, BlueWallet, Electrum Wallet (Android and Desktop), Nunchuk 
  (Desktop), Phoenix, and Sparrow Wallet to it instead of using a third-party
  Electrum server.


  Powered by Fulcrum from Calin Culianu
developer: Calin Culianu
website: https://fulcrumserver.org/
submitter: Sahil <PERSON>
submission: https://github.com/getumbrel/umbrel-apps/pull/1531
dependencies:
  - bitcoin
repo: https://github.com/cculianu/Fulcrum
support: https://github.com/cculianu/Fulcrum/issues
port: 2109
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultPassword: ""
releaseNotes: >-
  This release add a graceful shutdown method to mitigate any database corruption issues.


  **Previous release notes below.**


  Key highlights:
    - Added support for UPnP
    - Added support for bitcoind's ZMQ pubhashtx message
    - Improved Fulcrum's mempool model efficiency
    - Fixed a rare and esoteric bug
    - Added support for Unix domain sockets in ZeroMQ notifications
    - Assorted code quality/readability/etc improvements
