version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: suredbits-wallet_web_1
      APP_PORT: 3002

  web:
    image: bitcoinscala/wallet-server-ui:1.9.7@sha256:50da55b6d607d03775041aa2c056c5fe281a33f0e471d58e07a99dd2670718e3
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data/wallet:/bitcoin-s
      - ${APP_DATA_DIR}/data/log:/log
    environment:
      LOG_PATH: "/log/"
      BITCOIN_S_HOME: "/bitcoin-s/"
      #MEMPOOL_API_URL: "http://umbrel.local:${APP_MEMPOOL_PORT}/api"
      WALLET_SERVER_API_URL: "http://suredbits-wallet_walletserver_1:9999/"
      WALLET_SERVER_WS: "ws://suredbits-wallet_walletserver_1:19999/events"
      TOR_PROXY: socks5://${TOR_PROXY_IP}:${TOR_PROXY_PORT}
      DEFAULT_UI_PASSWORD: "none" 
      BITCOIN_S_SERVER_RPC_PASSWORD: $APP_PASSWORD
    depends_on: 
      - walletserver
  
  walletserver:
    image: bitcoinscala/bitcoin-s-server:1.9.7@sha256:cee2c4682d6172d6af2f0edfae016e3cc7aacc15ba56eb0652d9d65d31e7baf3
    entrypoint: ["/opt/docker/bin/bitcoin-s-server", "--datadir", "/bitcoin-s", "--conf", "/opt/docker/docker-application.conf"]
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/wallet:/bitcoin-s
    environment:
      BITCOIN_S_NODE_MODE: "bitcoind"
      BITCOIN_S_NETWORK: $APP_BITCOIN_NETWORK
      BITCOIN_S_KEYMANAGER_ENTROPY: $APP_SEED  
      BITCOIN_S_PROXY_ENABLED: "false"
      BITCOIN_S_TOR_ENABLED: "false"
      BITCOIN_S_TOR_PROVIDED: "true"
      BITCOIN_S_DLCNODE_PROXY_ENABLED: "true"
      BITCOIN_S_DLCNODE_PROXY_SOCKS5: "${TOR_PROXY_IP}:${TOR_PROXY_PORT}"
      BITCOIN_S_DLCNODE_EXTERNAL_IP: $APP_SUREDBITS_WALLET_P2P_HIDDEN_SERVICE
      BITCOIN_S_BITCOIND_HOST: $APP_BITCOIN_NODE_IP
      BITCOIN_S_BITCOIND_PORT: $APP_BITCOIN_RPC_PORT
      BITCOIN_S_BITCOIND_USER: $APP_BITCOIN_RPC_USER
      BITCOIN_S_BITCOIND_PASSWORD: $APP_BITCOIN_RPC_PASS
      BITCOIN_S_SERVER_RPC_PASSWORD: $APP_PASSWORD
      JAVA_OPTS: "-Xmx756m"
      DISABLE_JLINK: "1"
    ports:
      - "$APP_SUREDBITS_WALLET_P2P_PORT:$APP_SUREDBITS_WALLET_P2P_PORT"
  
  tor:
    image: getumbrel/tor:*******@sha256:2ace83f22501f58857fa9b403009f595137fa2e7986c4fda79d82a8119072b6a
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/torrc:/etc/tor/torrc:ro
      - ${TOR_DATA_DIR}:/data
    environment:
      HOME: "/tmp"
