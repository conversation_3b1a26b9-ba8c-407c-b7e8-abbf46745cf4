version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: btc-rpc-explorer_web_1
      APP_PORT: 8080

  web:
    image: getumbrel/btc-rpc-explorer:v3.5.0@sha256:11b55b2efd7c6a27cd1c13474ad78e8f0b91fdac2e8a48141e40ed38c5efd186
    restart: on-failure
    stop_grace_period: 1m
    environment:
      # Docker requirements
      BTCEXP_HOST: 0.0.0.0
      BTCEXP_PORT: 8080
      # Bitcoin Core
      BTCEXP_BITCOIND_HOST: $APP_BITCOIN_NODE_IP
      BTCEXP_BITCOIND_PORT: $APP_BITCOIN_RPC_PORT
      BTCEXP_BITCOIND_USER: $APP_BITCOIN_RPC_USER
      BTCEXP_BITCOIND_PASS: $APP_BITCOIN_RPC_PASS
      # Electrum
      BTCEXP_ADDRESS_API: electrumx
      BTCEXP_ELECTRUMX_SERVERS: "tcp://$APP_ELECTRS_NODE_IP:$APP_ELECTRS_NODE_PORT"
      # Log level
      DEBUG: "btcexp:*,electrumClient"
      # Performance
      BTCEXP_SLOW_DEVICE_MODE: "true"
      BTCEXP_NO_INMEMORY_RPC_CACHE: "true"
      # Privacy
      BTCEXP_PRIVACY_MODE: "true"
      BTCEXP_NO_RATES: "true"
      # Disable RPC
      BTCEXP_RPC_ALLOWALL: "false"
      BTCEXP_BASIC_AUTH_PASSWORD: ${APP_PASSWORD}
