manifestVersion: 1
id: btc-rpc-explorer
category: bitcoin
name: BTC RPC Explorer
version: "3.5.0"
tagline: Simple, database-free blockchain explorer
description: >-
  BTC RPC Explorer is a full-featured, self-hosted explorer for the
  Bitcoin blockchain. With this explorer, you can explore not just the
  blockchain database, but also explore the functional capabilities of your
  Umbrel.


  It comes with a network summary dashboard, detailed view of blocks, transactions, addresses, along with analysis tools for viewing stats on miner activity, mempool summary, with fee, size, and age breakdowns. You can also search by transaction ID, block hash/height, and addresses.


  It's time to appreciate the "fullness" of your node.
releaseNotes: >-
  ⚠️ It may take a few minutes for the app to be accessible after updating. Please be patient.

  
  This is a minor update that fixes a few bugs and includes the following improvements:
    - Mempool total fees are now displayed on the main page
    - Improved fee estimates for the next block

  
  Note: the version shown within the app will still be 3.4.0 even though the app will be running 3.5.0. The developers of BTC RPC Explorer are aware of this cosmetic issue.


  Full release notes for BTC RPC Explorer versions are available at https://github.com/janoside/btc-rpc-explorer/releases
developer: <PERSON>
website: https://bitcoinexplorer.org/
dependencies:
  - bitcoin
  - electrs
repo: https://github.com/janoside/btc-rpc-explorer
support: https://github.com/janoside/btc-rpc-explorer/discussions
port: 3002
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: "umbrel"
deterministicPassword: true
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/pull/334
