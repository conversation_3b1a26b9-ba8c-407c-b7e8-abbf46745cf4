version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: pingvin-share_web_1
      APP_PORT: 3000
      PROXY_AUTH_ADD: "false"
  
  web:
    image: stonith404/pingvin-share:v1.13.0@sha256:6bf2bcd3043ee68cb61264f0857511ccf7f212fdb984382b7f2d491635184ad6
    user: "1000:1000"
    restart: on-failure
    volumes:
      - "${APP_DATA_DIR}/data/backend:/opt/app/backend/data"
      - "${APP_DATA_DIR}/data/images:/opt/app/frontend/public/img"
