manifestVersion: 1.1
id: pingvin-share
category: files
name: Pingvin Share
version: "1.13.0"
tagline: Self-hosted file sharing platform
description: >-
  Pingvin Share is self-hosted file sharing platform and an alternative for WeTransfer.


  🛠️ To use Pingvin Share as intended you may need to expose it to the public internet. The easiest way to do this is to download the 'Cloudflare Tunnel' app from the umbrel app store, and expose Pingvin Share (running on port 6453) using your own domain.


  ✨ Features
    - Share files using a link
    - Unlimited file size (restricted only by disk space)
    - Set an expiration date for shares
    - Secure shares with visitor limits and passwords
    - Email recipients
    - Reverse shares
    - OIDC and LDAP authentication
developer: <PERSON>
website: https://stonith404.github.io/pingvin-share/introduction
repo: https://github.com/stonith404/pingvin-share
support: https://github.com/stonith404/pingvin-share/issues
dependencies: []
port: 6453
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  Key changes in this release:
    - Added option to use Redis cache instead of memory cache
    - Added ability to define path to the config file


  Full release notes are available at https://github.com/stonith404/pingvin-share/releases
torOnly: false
submitter: maltekiefer
submission: https://github.com/getumbrel/umbrel-apps/pull/1229
