manifestVersion: 1.1
id: endurain
name: Endurain
tagline: Fitness tracking, Open Source, Self-Hosted
category: social
version: "0.12.7"
port: 8865
description: >-
  Endurain is a self-hosted fitness tracking service designed to give users full control over their data and hosting environment. 


  It's similar to Strava but focused on privacy and customization.


  Features:

  - **Seamless Activity Tracking** - Import workouts via manual entry, bulk upload (.gpx and .fit files), or sync automatically with Strava and Garmin Connect

  - **Complete Gear Management** - Monitor usage and performance of your shoes, bicycles, wetsuits, and more

  - **Personalized Dashboard** - View your activity feeds, weekly/monthly statistics, and progress at a glance

  - **Social Connection** - Follow friends, share achievements, and build your fitness community

  - **Customizable Experience** - Enjoy multi-language support, dark/light themes, and interfaces that adapt to admin or user roles

  - **Comprehensive Health Tracking** - Sync body composition data and log weight measurements

  - **Privacy Controls** - Manage who sees your activities with flexible privacy settings

  - **Third-Party Integration** - Connect with your favorite fitness apps for a unified experience
developer: <PERSON>
website: https://docs.endurain.com/
submitter: al-lac
submission: https://github.com/getumbrel/umbrel-apps/pull/2764
repo: https://github.com/joaovitoriasilva/endurain
support: https://github.com/joaovitoriasilva/endurain/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
defaultUsername: "admin"
defaultPassword: "admin"
dependencies: []
releaseNotes: ""
path: ""
