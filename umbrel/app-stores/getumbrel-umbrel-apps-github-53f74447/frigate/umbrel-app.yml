manifestVersion: 1
id: frigate
category: automation
name: Frigate
version: "0.15.1"
tagline: A complete and local NVR
description: >-
  A complete and local NVR designed for Home Assistant with AI object detection. 
  Uses OpenCV and Tensorflow to perform realtime object detection locally for IP cameras.


  🛠️ SET-UP


  - You can configure your cameras directly within the Frigate app, there is no need to manually edit the configuration files.


  - Your RTSP password (if needed) is the "default app password" that is displayed on Frigate's page in the app store (shown after install).
releaseNotes: >-
  ⚠️ As usual, Frigate will attempt to update its configuration automatically. It is still recommended to back up your current config and database before upgrading.


  This is a maintenance release that includes two bugfixes:
    - Fixed a crash in the 'get_current_frame()' function
    - Fixed the TensorRT model preparation script


  Full release notes are available at https://github.com/blakeblackshear/frigate/releases
developer: <PERSON>
website: https://frigate.video/
repo: https://github.com/blakeblackshear/frigate
support: https://github.com/blakeblackshear/frigate/issues
port: 5004
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
deterministicPassword: true
dependencies: []
submitter: ~dibref-labter
submission: https://github.com/getumbrel/umbrel-apps/pull/843
