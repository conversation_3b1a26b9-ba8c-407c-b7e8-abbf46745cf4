version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: lidarr_server_1
      APP_PORT: 8686
      PROXY_AUTH_WHITELIST: "/api/*"

  server:
    image: linuxserver/lidarr:2.12.4@sha256:71fe6d5702691c6ac8961b9b1042fdea1ff833a49c82c5e165346fa88999a48a
    environment:
      - PUID=1000
      - PGID=1000
    volumes:
      - ${APP_DATA_DIR}/data/config:/config
      - ${UMBREL_ROOT}/data/storage/downloads:/downloads
    restart: on-failure

  mac:
    image: getumbrel/media-app-configurator:v1.3.0@sha256:67e75dd9f5a14402b7816119a8e20189bc2465484cea077909d164687e59742b
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/config:/config
      - ${UMBREL_ROOT}/data/storage/downloads:/downloads
    environment:
      APP_ID: "lidarr"
      APP_URL: "http://lidarr_server_1:8686"
      TRANSMISSION_HOST: "transmission_server_1"
      TRANSMISSION_PORT: 9091
      ROOT_FOLDER: "/downloads/music"
      # optional qBittorrent download client
      QBITTORRENT_INSTALLED: ${APP_LIDARR_QBITTORRENT_INSTALLED:-"false"}
      QBITTORRENT_HOST: "qbittorrent_server_1"
      QBITTORRENT_PORT: 8080
      # optional SABnzbd download client
      SABNZBD_INSTALLED: ${APP_LIDARR_SABNZBD_INSTALLED:-"false"}
      SABNZBD_HOST: "sabnzbd_web_1"
      SABNZBD_PORT: 8080
      SABNZBD_API_KEY: ${APP_LIDARR_SABNZBD_API_KEY:-""}
