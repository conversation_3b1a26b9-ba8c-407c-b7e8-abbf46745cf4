manifestVersion: 1.1
id: lidarr
category: media
name: Lidarr
version: "2.12.4.4658"
tagline: Looks and smells like Sonarr but made for music
description: >-
  Lidarr is a music collection manager for Usenet and BitTorrent users.
  It can monitor multiple RSS feeds for new albums from your favorite artists and will interface with clients and indexers to grab, sort, and rename them.
  It can also be configured to automatically upgrade the quality of existing files in the library when a better quality format becomes available.


  🛠️ SETUP INSTRUCTIONS

  Lidarr on umbrelOS will automatically connect to download clients installed from the Umbrel App Store. Choose from Transmission, qBittorerent, and SABnzbd. Simply install your preferred client(s).


  All you need to do from there is add an indexer so Lidarr can search for music. You can add indexers directly within Lidarr, or install Prowlarr from the Umbrel App Store for easier management of indexers across multiple apps.
  Add your indexers to Prowlarr and they will be automatically available in Lidarr.

developer: Lidarr
website: https://lidarr.audio/
dependencies:
  - transmission
repo: https://github.com/Lidarr/Lidarr
support: https://github.com/Lidarr/Lidarr/issues
port: 8686
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: false
releaseNotes: >-
  Key improvements in this release:
    - Real-time UI updates for provider changes
    - Improved sorting of artists by genre in index table view
    - Enhanced parsing of FLAC 24-bit audio formats
    - Better handling of queue filtering by multiple qualities
    - Improved messaging for grab errors due to tag mismatches
    - Fixed issues with loading queue for deleted artists
    - Enhanced natural sorting for tags and other UI lists
    - Better support for qBittorrent v5.0 and Transmission 4.0 labels
    - Improved webhook and notification features with artist tags and genres
    - Various UI improvements and bug fixes


  Full release notes are found at https://github.com/Lidarr/Lidarr/releases
permissions:
  - STORAGE_DOWNLOADS
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/commit/60878f278d544b204d8e7c96240c797f43a9b319
