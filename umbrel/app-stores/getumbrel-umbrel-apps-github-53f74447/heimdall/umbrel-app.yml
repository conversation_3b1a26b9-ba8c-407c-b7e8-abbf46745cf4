manifestVersion: 1
id: heimdall
name: Heimdall
tagline: Organize your most used web sites in a simple way
category: files
version: "2.6.3"
port: 7392
description: >-
  Heimdall is a dashboard for all your web applications. It doesn't need to be limited to applications though, you can add links to anything you like.

  
  Heimdall is an elegant solution to organise all your web applications. It's dedicated to this purpose so you won't lose your links in a sea of bookmarks.

  
  Why not use it as your browser start page? It even has the ability to include a search bar using either Google, Bing or DuckDuckGo.

  
  You can use the app to link to any site or application, but Foundation apps will auto fill in the icon for the app and supply a default color for the tile. In addition Enhanced apps allow you provide details to an apps API, allowing you to view live stats directly on the dashboad. For example, the NZBGet and Sabnzbd Enhanced apps will display the queue size and download speed while something is downloading.

  
  Supported applications are recognized by the title of the application as entered in the title field when adding an application. For example, to add a link to pfSense, begin by typing "p" in the title field and then select "pfSense" from the list of supported applications.
developer: LinuxServer.io
website: https://heimdall.site
repo: https://github.com/linuxserver/Heimdall
support: https://github.com/linuxserver/Heimdall/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >
  Highlights:
    - Fixed search functionality when using category mode
    - Fixed URL-based searching
    - Improved visibility of UI elements on light backgrounds
    - Updated Spanish translations

  The full release notes are available at https://github.com/linuxserver/Heimdall/releases
dependencies: []
path: ""
torOnly: false
defaultUsername: ""
defaultPassword: ""
submitter: AxelPilop
submission: https://github.com/getumbrel/umbrel-apps/pull/390