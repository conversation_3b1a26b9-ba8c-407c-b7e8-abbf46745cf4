version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: public-pool_proxy_1
      APP_PORT: 80
      PROXY_AUTH_WHITELIST: "/api/*"

  web:
    image: smolgrrr/public-pool-ui:8cd2563@sha256:95884d33475b1681cf645831aefadb0cc31a89c6f537ede643251bc874311804
    restart: on-failure
    stop_grace_period: 30s
    environment:
      - DOMAIN=$DEVICE_DOMAIN_NAME

  server:
    image: sethforprivacy/public-pool:9c14003@sha256:becfe82228a4cc814da8832d0c909000a382e2b39ef8a5d5cb6a8fb0bc554850
    restart: on-failure
    stop_grace_period: 30s
    ports:
      - "2018:2018/tcp"
    volumes:
      - "${APP_DATA_DIR}/data/database:/public-pool/DB"
    environment:
      - NODE_ENV=production
      - BITCOIN_RPC_URL=http://${APP_BITCOIN_NODE_IP}
      - BITCOIN_RPC_USER=${APP_BITCOIN_RPC_USER}
      - BITCOIN_RPC_PASSWORD=${APP_BITCOIN_RPC_PASS}
      - BITCOIN_RPC_PORT=${APP_BITCOIN_RPC_PORT}
      - BITCOIN_RPC_TIMEOUT=10000
      - API_PORT=2019
      - STRATUM_PORT=2018
      - NETWORK=mainnet
      - API_SECURE=false
      - ENABLE_SOLO=true
      - ENABLE_PROXY=false
      - POOL_IDENTIFIER="Public Pool on Umbrel"

  proxy:
    image: nginx:1.25.3@sha256:4c0fdaa8b6341bfdeca5f18f7837462c80cff90527ee35ef185571e1c327beac
    volumes:
      - ${APP_DATA_DIR}/data/proxy/nginx.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - web
      - server
    restart: on-failure

  widget-server:
    image: ghcr.io/getumbrel/umbrel-public-pool-widget:v1.0.0@sha256:b8861a5b79471377f1bfbf6733f11350970b482103d4ef4185f372350ffff6b3
    user: "1000:1000"
    environment:
      - PUBLIC_POOL_API_URL=http://public-pool_proxy_1/api/pool
    restart: on-failure
