manifestVersion: 1
id: public-pool
category: bitcoin
name: Public Pool
version: "9c14003-w"
tagline: Fully Open Source Solo Bitcoin Mining Pool
description: >-
  Public Pool is an open-source, solo Bitcoin mining application that lets you run your own pool using your own node.
  Instead of relying on centralized pools, Public Pool enables your mining hardware to submit work to your personal setup, ensuring that you have full control over your mining operation.
  If you successfully mine a block, you receive the entire block reward. No splitting with other miners. No fees. No middleman.
  
  
  On umbrelOS, Public Pool is automatically configured to work with your Bitcoin node—no extra setup required. Simply install the app, and it's ready to go.
  
  
  To start mining, just point your mining hardware to your Public Pool instance using the stratum details provided in the app.
  Monitor your total hashrate, track individual miners, and receive rewards directly to your chosen Bitcoin address. Mine independently and help strengthen Bitcoin's decentralization.
developer: <PERSON>
website: https://web.public-pool.io/#/
dependencies:
  - bitcoin
repo: https://github.com/benjamin-wilson/public-pool
support: https://github.com/benjamin-wilson/public-pool/issues
port: 2019
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
releaseNotes: >-
  This update brings a brand new Public Pool widget, allowing you to see your pool stats at a glance right from your umbrelOS home screen!


  To add this widget, right-click on your home screen and select "Edit widgets", or click on Widgets in the Dock.
defaultPassword: ""
widgets:
  - id: "stats"
    type: "four-stats"
    refresh: "5s"
    endpoint: "widget-server:3000/widgets/pool"
    link: ""
    example:
      type: "four-stats"
      link: ""
      items:
        - title: "Hash Rate"
          text: "968"
          subtext: "GH/s"
        - title: "Miners"
          text: "1"
        - title: "Blocks Found"
          text: "1"
        - title: "Block Height"
          text: "883453"
submitter: smolgrrr
submission: https://github.com/getumbrel/umbrel-apps/pull/915
