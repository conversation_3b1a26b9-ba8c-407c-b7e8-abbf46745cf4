manifestVersion: 1.1
id: woofbot-lnd
category: bitcoin
name: WoofBot (LND)
version: "0.9.8"
tagline: A chatbot for your personal node
description: >-
  WoofBot is a chat bot that runs on your personal Bitcoin node and sends telegram notifications
  for configured events: payments from/to specified addresses, transactions being confirmed,
  new-blocks and more.

  This version of WoofBot integrates with LND lightning api. WoofBot is NOT affiliated with
  Lightning Labs, the creators of LND. WoofBot only communicates with LND's api.
developer: WoofBotApp
website: https://github.com/woofbotapp
dependencies:
  - bitcoin
  - lightning
repo: https://github.com/woofbotapp/woofbotapp
support: https://github.com/woofbotapp/woofbotapp/discussions
port: 8093
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
releaseNotes: >-
  - Show users' invoices watches in dashboard

  - New emojies for notifications about address being paid
  
  - Upgrade server-side dependencies
submitter: WoofBotApp
submission: https://github.com/getumbrel/umbrel-apps/pull/442
