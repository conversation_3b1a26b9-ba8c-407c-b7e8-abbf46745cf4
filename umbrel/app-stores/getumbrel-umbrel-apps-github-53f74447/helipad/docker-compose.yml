version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: helipad_web_1
      APP_PORT: 2112

  web:
    image: podcastindexorg/podcasting20-helipad:0.2.1@sha256:97caa32c6f5c7662d37bf6b0d48a0b54b384af3f2794e53b9129ededfe71dfc2
    init: true
    restart: on-failure
    stop_grace_period: 1m
    user: "1000:1000"
    volumes:
      - ${APP_DATA_DIR}/data:/data
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
    environment:
      LND_URL: "$APP_LIGHTNING_NODE_IP:$APP_LIGHTNING_NODE_GRPC_PORT"
