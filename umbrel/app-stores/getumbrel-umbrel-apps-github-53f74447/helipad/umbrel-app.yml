manifestVersion: 1
id: helipad
category: bitcoin
name: Helipad
version: "0.2.1"
tagline: View boosts & boostagrams from Podcasting 2.0 apps
description: Helipad shows boosts and boostagram messages coming in to your
  Lightning node from your listeners who are using Podcasting 2.0 apps.
developer: Podcastindex.org
website: https://podcastindex.org
dependencies:
  - lightning
repo: https://github.com/Podcastindex-org/helipad
support: https://podcastindex.social
port: 2112
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultPassword: ""
submitter: Podcastindex.org
submission: https://github.com/getumbrel/umbrel/pull/1174
releaseNotes: >-
  This update includes the following improvements:

  - Add podcast filter

  - Add njump links for Nostr references and setting to resolve npubs to names

  - Add custom reports to settings page

  - Show hosted wallet info

  - Add icon for wherever.audio

  - Format timestamp hover according to current locale

  - Add ability to trigger webhooks on certain amounts

  - Add a pew queue to make sure overlapping pews do not get skipped

  - Fix reply boosts to handle empty wallet key/values and Fountain wallet ids

  - Fix sound upload caching issues
