manifestVersion: 1.1
id: jam
category: bitcoin
name: Jam
version: "0.4.0"
tagline: Your sats. Your privacy. Your profit.
description: >-
  Jam is a user-interface for JoinMarket with focus on
  user-friendliness.

  It is time for top-notch privacy for your bitcoin. Widespread use of JoinMarket improves bitcoin's fungibility and privacy for all.


  The app provides sensible defaults and is easy to use for beginners while still providing the features advanced users expect.
releaseNotes: >-
  - Fidelity Bond: Ability to select destination jar when unlocking fidelity bond

  - Send: Color-coded checkboxes for UTXO states

  - Rescan: Show progress during timechain rescan

  - Orderbook: Ability to reload and/or hard refresh

  - Theme: Fast theme toggle in navbar

  - UI: Various UI improvements and bugfixes


  Full release notes can be found at https://github.com/joinmarket-webui/jam/releases
developer: JoinMarket WebUI Org.
website: https://jamapp.org
dependencies:
  - bitcoin
repo: https://github.com/joinmarket-webui/jam
support: https://github.com/joinmarket-webui/jam/issues
port: 5002
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: umbrel
defaultPassword: ""
deterministicPassword: true
submitter: JoinMarket WebUI Org.
submission: https://github.com/getumbrel/umbrel/pull/1216
