version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: jam_web_1
      APP_PORT: 80

  web:
    image: ghcr.io/joinmarket-webui/jam-standalone:v0.4.0-clientserver-v0.9.11@sha256:3742e15734b91ea520ae7b78c2e6b82aed715bdcda5a786c92efe77ffd89ecc7
    restart: on-failure
    stop_grace_period: 1m
    init: true
    volumes:
      - ${APP_DATA_DIR}/data/joinmarket:/root/.joinmarket
    environment:
      RESTORE_DEFAULT_CONFIG: "true"
      REMOVE_LOCK_FILES: "true"
      ENSURE_WALLET: "true"
      APP_USER: umbrel
      APP_PASSWORD: "${APP_PASSWORD}"
      jm_network: $APP_BITCOIN_NETWORK
      jm_rpc_host: $APP_BITCOIN_NODE_IP
      jm_rpc_port: $APP_BITCOIN_RPC_PORT
      jm_rpc_user: $APP_BITCOIN_RPC_USER
      jm_rpc_password: "${APP_BITCOIN_RPC_PASS}"
      jm_rpc_wallet_file: jam_default
