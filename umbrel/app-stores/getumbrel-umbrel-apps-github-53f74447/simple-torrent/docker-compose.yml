version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: simple-torrent_server_1
      APP_PORT: 8086

  server:
    image: boypt/cloud-torrent:1.3.9@sha256:90cc62869ebaabbdba31535cdff4c66ede98b341956d2ebcd6650610a37e9819
    user: "1000:1000"
    restart: on-failure
    command: >
      --port=8086
      --config-path /config/simple-torrent.json
    volumes:
      - ${APP_DATA_DIR}/data/torrents:/torrents
      - ${UMBREL_ROOT}/data/storage/downloads:/downloads
      - ${APP_DATA_DIR}/data/config:/config
