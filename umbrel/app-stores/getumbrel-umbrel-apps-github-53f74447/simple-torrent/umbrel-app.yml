manifestVersion: 1.1
id: simple-torrent
category: networking
name: SimpleTorrent
version: "1.3.9-hotfix-1"
tagline: Download torrents with your Umbrel
description: >-
  SimpleTorrent is a a self-hosted remote torrent client that starts
  torrents remotely, download sets of files on your Umbrel, which are then
  retrievable or streamable via web browser over HTTP. This project is a
  re-branded fork of cloud-torrent by jpillora. Features:


  - Individual file download control

  - Run external program on tasks completion: DoneCmd

  - Stops task when seeding ratio reached: SeedRatio

  - Download/Upload speed limiter: UploadRate/DownloadRate

  - Detailed transfer stats in web UI.

  - Torrent Watcher

  - Extra trackers from external source

  - Protocol Handler to magnet:

  - Magnet RSS subscribing supported


  ⚠️ SimpleTorrent downloads torrents over the Clearnet, not Tor.
releaseNotes: >-
  - SimpleTorrent now utilizes the shared downloads folder that can be accessed by other apps on Umbrel, such as Jellyfin, File Browser, Transmission, Plex, and more.
developer: Preston
website: https://github.com/boypt
dependencies: []
repo: https://github.com/boypt/simple-torrent
support: https://github.com/boypt/simple-torrent/issues
port: 8086
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
torOnly: false
permissions:
  - STORAGE_DOWNLOADS
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/commit/b12c581ae14255c2087fc8e2f0e7014a351a363b