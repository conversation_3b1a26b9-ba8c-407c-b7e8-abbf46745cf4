version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: krystal-bull_web_1
      APP_PORT: 3001
  
  web:
    image: bitcoinscala/oracle-server-ui:1.9.7@sha256:883df7037be517b4dc8155a1bfcd4101b7e47f7825367f53f3ac2bf15ffdc15e
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data/oracleserver:/bitcoin-s
      - ${APP_DATA_DIR}/data/log:/log
    environment:
      LOG_PATH: "/log/"
      BITCOIN_S_HOME: "/bitcoin-s/"
      ORACLE_SERVER_API_URL: "http://krystal-bull_oracleserver_1:9998/"
      TOR_PROXY: socks5://${TOR_PROXY_IP}:${TOR_PROXY_PORT}
      DEFAULT_UI_PASSWORD: "none"
      BITCOIN_S_ORACLE_RPC_PASSWORD: $APP_PASSWORD
    depends_on: 
      - oracleserver
  
  oracleserver:
    image: bitcoinscala/bitcoin-s-oracle-server:1.9.7@sha256:a7f5dd31bc5afa2a075563433862851d5bea8f894c1634a56426c34648001308
    entrypoint: ["/opt/docker/bin/bitcoin-s-oracle-server", "--datadir", "/bitcoin-s", "--conf", "/opt/docker/docker-application.conf"]   
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/oracleserver:/bitcoin-s
    environment:
      BITCOIN_S_KEYMANAGER_ENTROPY: $APP_SEED
      BITCOIN_S_ORACLE_RPC_PASSWORD: $APP_PASSWORD
      DISABLE_JLINK: "1"
      JAVA_OPTS: "-Xmx256m"
