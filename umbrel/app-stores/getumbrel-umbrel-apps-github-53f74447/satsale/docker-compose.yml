version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: satsale_web_1
      APP_PORT: 5000
      PROXY_AUTH_ADD: "false"
  
  web:
    image: satsale/satsale:0.1.13@sha256:98380ad6c5166d83e627c4d1f4efdbcfd5b9217096f7fb26b287fda89eebccbb
    restart: on-failure
    stop_grace_period: 1m
    volumes:
      - ${APP_DATA_DIR}/data:/data
      - ${APP_LIGHTNING_NODE_DATA_DIR}:/lnd:ro
    environment:
      DATA_DIR: "/data"
      # Lightning node connection details
      LND_DATA_DIR: $APP_LIGHTNING_NODE_DATA_DIR
      LND_HOST: $APP_LIGHTNING_NODE_IP
      LND_GRPC_PORT: $APP_LIGHTNING_NODE_GRPC_PORT
      MACAROON_FILE: /lnd/data/chain/bitcoin/${APP_BITCOIN_NETWORK}/invoice.macaroon
      TLS_FILE: /lnd/tls.cert
      APP_PASSWORD: $APP_PASSWORD
