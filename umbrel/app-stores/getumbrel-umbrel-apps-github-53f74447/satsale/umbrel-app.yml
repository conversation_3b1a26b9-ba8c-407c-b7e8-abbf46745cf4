manifestVersion: 1
id: satsale
category: bitcoin
name: SatSale
version: "0.1.13-build-1"
tagline: Lightweight Bitcoin payment processor
description: >-
  SatSale is a simple, easily deployable, lightweight Bitcoin payment
  processor that connects to your own Bitcoin node. You can use SatSale to
  instantly turn your Umbrel into a point-of-sale (settings under /admin).
  SatSale can process donations from your supporters, facilitating direct
  peer-to-peer payments without any middleman and greater privacy than reusing
  donation addresses.


  Host your own Bitcoin payment gateway for Woocommerce without a middleman or custodian, allowing you to turn any wordpress site into a bitcoin-accepting store.


  This Umbrel app contains a subset of the features we have in the works for SatSale, but will push updates out as they are ready. Check out the website and GitHub for more!


  Please note that SatSale is still in early development. As such, we are not responsible for any loss of funds, vulnerabilities, or any other grievances which arise. Always confirm large payments manually and use cold storage as much as possible. Please contribute if you can!
developer: <PERSON>
website: https://satsale.org
dependencies:
  - lightning
repo: https://github.com/nickfarrow/SatSale/
support: https://github.com/nickfarrow/SatSale/issues
port: 5000
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: /admin
deterministicPassword: true
submitter: Nick Farrow
submission: https://github.com/getumbrel/umbrel/pull/1102
releaseNotes: ""