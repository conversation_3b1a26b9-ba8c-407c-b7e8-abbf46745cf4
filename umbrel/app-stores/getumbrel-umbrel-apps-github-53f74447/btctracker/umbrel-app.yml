manifestVersion: 1
id: btctracker
name: BTC Tracker
tagline: A simple, privacy-focused Bitcoin investment tracker
category: bitcoin
version: "0.5.3"
port: 3947
description: >-
  BTC Tracker is a user-friendly yet powerful application designed for managing and analyzing Bitcoin investments. It was created to offer a privacy-focused and straightforward alternative to existing tracking tools, which are often cluttered with unnecessary features, complicated to set up, or designed for managing large, diversified portfolios. Many of those tools require connections to external services and the sharing of sensitive information such as wallet addresses or transaction histories.


  BTC Tracker takes a different approach. The entire application runs locally on your own device, with no data ever sent to external servers or third parties. All investment data, transactions, and settings are stored securely and privately on your machine, giving you full control over your personal information at all times. This makes BTC Tracker an ideal solution for anyone who values privacy and data ownership.


  Beyond simply recording investments, BTC Tracker provides powerful analysis features. Users can track the performance of their Bitcoin holdings over time, calculate profits and losses, and monitor their return on investment (ROI). The app supports multiple currencies, including EUR, USD, GBP, JPY, CHF, and PLN, allowing users to view their data in their preferred local currency.


  The interface is modern and responsive, designed to work seamlessly on both desktop and mobile devices. Users can choose between a light or dark theme depending on their visual preferences. BTC Tracker also includes CSV import and export features, making it easy to migrate data from other tools or create backups.


  Overall, BTC Tracker is a focused tool that puts privacy first and strips away unnecessary complexity. It gives you a clear, secure way to track your Bitcoin investments without compromising your data.


  ⚠️ To use BTC Tracker, you need to create a user account. The application cannot function without it, as all data is securely linked to your personal profile and stored locally.
developer: Szymon Szóstak 
website: https://github.com/wilqq-the
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2597
repo: https://github.com/wilqq-the/BTC-Tracker
support: https://github.com/wilqq-the/BTC-Tracker/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
releaseNotes: >-
  This release includes several new features and improvements:
    - Added Lightning Network support for fast, low-fee donations alongside Bitcoin
    - Major currency system improvements with better logging, caching, and error handling
    - UI/UX enhancements including modal styling, dropdown fixes, and mobile improvements
    - Introduced Bitcoin-themed validation messages and easter eggs
    - Upgraded charts with Yahoo Finance data, time slider, and comparison tools
    - Added support for Brazilian Real (BRL) currency
    - Overhauled testing infrastructure and improved test reliability
    - Technical fixes for startup reliability, dropdown interactions, and API handling
    - Improved performance, error handling, and logging across the app
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
