manifestVersion: 1
id: grafana
name: Grafana
tagline: The open-source platform for monitoring and observability
category: networking
version: "12.0.2"
port: 3030
description: >-
  Grafana allows you to query, visualize, alert on and understand your metrics no matter where they are stored. Create, explore, and share dashboards with your team and foster a data-driven culture:


  - Visualizations: Fast and flexible client side graphs with a multitude of options. Panel plugins offer many different ways to visualize metrics and logs.

  
  - Dynamic Dashboards: Create dynamic & reusable dashboards with template variables that appear as dropdowns at the top of the dashboard.

  
  - Explore Metrics: Explore your data through ad-hoc queries and dynamic drilldown. Split view and compare different time ranges, queries and data sources side by side.

  
  - Explore Logs: Experience the magic of switching from metrics to logs with preserved label filters. Quickly search through all your logs or streaming them live.

  
  - Alerting: Visually define alert rules for your most important metrics. Grafana will continuously evaluate and send notifications to systems like Slack, PagerDuty, VictorOps, OpsGenie.

  
  - Mixed Data Sources: Mix different data sources in the same graph! You can specify a data source on a per-query basis. This works for even custom datasources.


developer: Grafana Labs
website: https://grafana.com
repo: https://github.com/grafana/grafana
support: https://grafana.com/help/
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: >-
  ⚠️ Major version upgrades may include breaking changes affecting features, APIs, plugins, and integrations.


  This release includes important security fixes and enhancements:
    - Fixed alerting issues with value types and group-level labels
    - Improved Azure Application Insights metadata handling
    - Enhanced storage migration settings support
    - Fixed organization deletion functionality
    - Security fixes addressing multiple vulnerabilities


  Full release notes are available at https://github.com/grafana/grafana/releases
dependencies: []
path: ""
defaultUsername: "admin"
defaultPassword: "admin"
submitter: phelipebf
submission: https://github.com/getumbrel/umbrel-apps/pull/1664
