manifestVersion: 1
id: torbrowser
name: Tor Browser
tagline: An invisible shield for your browsing experience
category: networking
version: "14.0.3"
port: 5832
description: >-
  🧅 Tor Browser is a powerful tool designed to provide anonymous, secure, and private access to the internet. Built on Mozilla Firefox and optimized for privacy, Tor Browser uses the Tor network (The Onion Router) to protect users' identities and locations by encrypting and relaying their internet traffic through multiple nodes across the globe. This process, known as “onion routing,” conceals users' IP addresses, making it extremely difficult for websites, advertisers, or surveillance entities to track browsing activities or pinpoint locations.


  For those who value privacy or face restrictive internet environments, Tor Browser offers key privacy features. It blocks cookies, trackers, and fingerprinting technologies that typically monitor users across websites, and it clears browsing history and cookies automatically with each session. This ensures a high degree of anonymity and safeguards against data collection in ways that standard browsers cannot.


  In addition to enhanced privacy, Tor Browser enables unrestricted access to the open web, allowing users to bypass local censorship and reach content that may otherwise be blocked in certain regions or networks. While some trade-offs include slightly slower browsing speeds due to multi-node routing, Tor Browser is one of the most effective tools for secure, uncensored internet access. It empowers users to explore the internet freely and confidently, supporting a safer, more private online experience.
developer: The Tor Project
website: https://www.torproject.org/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/1928
repo: https://github.com/torproject/tor
support: https://support.torproject.org/
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
releaseNotes: ""
dependencies: []
path: ""
