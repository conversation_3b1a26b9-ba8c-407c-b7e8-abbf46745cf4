manifestVersion: 1.1
id: bitcoin
category: bitcoin
name: Bitcoin Node
version: "1.0.2"
tagline: Run your personal node powered by Bitcoin Core
description: >-
  Run your Bitcoin node and independently store and validate
  every single Bitcoin transaction with it. Achieve unparalleled
  privacy by connecting your wallet directly to your node to
  ensure that your wallet company can’t spy on your transactions,
  or worse — lie to you.


  Plus, transacting with your own node also helps keep the entire
  Bitcoin network decentralized; win-win!


  With over 20 advanced settings, you can take control of your node
  and customize it to your needs. Control how your node connects to
  other nodes over Tor, clearnet, and I2P, adjust your daily upload
  limit, set the maximum mempool size, fine-tune its performance,
  and much more.


  Don't trust. Verify. On steroids.


  An official app from Umbrel.
developer: Umbrel
website: https://umbrel.com
dependencies: []
repo: https://github.com/getumbrel/umbrel-bitcoin
support: https://community.getumbrel.com/c/bitcoin-and-lightning
port: 2100
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
path: ""
defaultPassword: ""
releaseNotes: >-
  This update fixes an issue where certain manually edited bitcoin.conf files were not parsed correctly, and also fixes a compatibility issue with Tor Browser.


  ### In addition, here's what's new in the all-new Bitcoin Node app.


  This update introduces a completely redesigned experience, rebuilt from the ground up, both architecturally and visually, to give you a powerful, data-rich view into every aspect of your node's operation.
  
  
  From visualizing the network in real time to fine-tuning your configuration, the tools you need for ultimate sovereignty are now at your fingertips.
  
  
  ### New features:
  
  - **Globe**: A stunning, real-time visualization of your node's global connections. See new peers connect and transactions broadcast around the world, as it happens.
  
  - **Blocks**: Follow along as each new block arrives, packed with transactions, and is verified by your own node.

  - **Insights**: Key metrics about your node and the network are now presented in a series of clean, elegant charts.

  - **Peers**: A completely new table gives you a detailed, sortable list of every peer your node is connected to.

  - **Settings**: Fine-tune your node with a simplified, searcheable, and more intuitive layout for the settings. 

  - **Configuration Editor**: Power users can now make custom fine-grained adjustments to their `bitcoin.conf` file directly from a new editor in the app's settings.


  A new chapter for self-sovereignty begins.
widgets:
  - id: "stats"
    type: "four-stats"
    refresh: "5s"
    endpoint: "app:3000/api/widget/stats"
    link: ""
    example:
      type: "four-stats"
      link: ""
      items:
        - title: "Connections"
          text: "11"
          subtext: "peers"
        - title: "Mempool"
          text: "257"
          subtext: "MB"
        - title: "Hashrate"
          text: "590"
          subtext: "EH/s"
        - title: "Blockchain size"
          text: "600"
          subtext: "GB"
  - id: "sync"
    type: "text-with-progress"
    refresh: "2s"
    endpoint: "app:3000/api/widget/sync"
    link: ""
    example:
      type: "text-with-progress"
      link: ""
      title: "Blockchain sync"
      text: "83%"
      progressLabel: "In progress"
      progress: 0.83
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel-apps/commit/576ecd2bef8d625abceed0f67ec9c487da9b2b1b
