version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: $APP_BITCOIN_NODE_IP
      APP_PORT: 3000

  app:
    image: ghcr.io/getumbrel/umbrel-bitcoin:v1.0.2-core.v29.0@sha256:87ced535f12c5590218e5392b2cb37eb9953819e4c007bbd256b49bda357a40f
    user: "1000:1000"
    restart: on-failure
    stop_grace_period: 15m30s
    # these ports are exposed for external connections to bitcoind
    ports:
      - "${APP_BITCOIN_P2P_PORT}:${APP_BITCOIN_P2P_PORT}"
      - "${APP_BITCOIN_RPC_PORT}:${APP_BITCOIN_RPC_PORT}"
    volumes:
      - ${APP_DATA_DIR}/data:/data # data/app and data/bitcoin
    environment:
      # DATA DIRECTORY SETTINGS
      # BITCOIN_DIR: bitcoin-dir-override
      # APP_STATE_DIR: app-state-dir-override

      # BITCOIND BINARY SETTINGS
      # BITCOIND_BIN: bitcoind-binary-path-override
      # comma-delimited list of extra startup flags to pass to bitcoind
      BITCOIND_EXTRA_ARGS: '-deprecatedrpc=create_bdb' # We can remove depratedrpc=create_bdb in a future update once Jam (JoinMarket) implements descriptor wallet support

      # BITCOIND CONF FILE SETTINGS
      APPS_SUBNET: "${NETWORK_IP}/16" # used to whitelist local apps as both peers (whitelist) and rpc connections (rpcallowip)
      P2P_PORT: "${APP_BITCOIN_P2P_PORT}" # port, bind (also used for connect details)
      RPC_PORT: "${APP_BITCOIN_RPC_PORT}" # rpcport (also used for rpc client and connect details)
      TOR_PORT: "${APP_BITCOIN_TOR_PORT}" # bind=onion
      ZMQ_RAWBLOCK_PORT: "${APP_BITCOIN_ZMQ_RAWBLOCK_PORT}" # zmqpubrawblock
      ZMQ_RAWTX_PORT: "${APP_BITCOIN_ZMQ_RAWTX_PORT}" # zmqpubrawtx
      ZMQ_HASHBLOCK_PORT: "${APP_BITCOIN_ZMQ_HASHBLOCK_PORT}" # zmqpubhashblock
      ZMQ_SEQUENCE_PORT: "${APP_BITCOIN_ZMQ_SEQUENCE_PORT}" # zmqpubsequence
      ZMQ_HASHTX_PORT: "${APP_BITCOIN_ZMQ_HASHTX_PORT}" # zmqpubhashtx
      TOR_HOST: "${APP_BITCOIN_TOR_PROXY_IP}" # proxy, onion, torcontrol
      TOR_SOCKS_PORT: "9050" # proxy, onion
      TOR_CONTROL_PORT: "9051" # torcontrol
      TOR_CONTROL_PASSWORD: "moneyprintergobrrr" # torpassword
      I2P_HOST: "${APP_BITCOIN_I2P_DAEMON_IP}" # i2psam
      I2P_SAM_PORT: "7656" # i2psam

      # RPC CLIENT SETTINGS
      BITCOIND_IP: "${APP_BITCOIN_NODE_IP}" # host for the rpc client. Also used for bitcoind conf file rpcbind & bind.
      RPC_USER: "${APP_BITCOIN_RPC_USER}" # also shown in the connect modal
      RPC_PASS: "${APP_BITCOIN_RPC_PASS}" # also shown in the connect modal

      # CONNECTION DETAILS (shown to user in the connect modal)
      DEVICE_DOMAIN_NAME: "${DEVICE_DOMAIN_NAME}"
      P2P_HIDDEN_SERVICE: "${APP_BITCOIN_P2P_HIDDEN_SERVICE}"
      RPC_HIDDEN_SERVICE: "${APP_BITCOIN_RPC_HIDDEN_SERVICE}"
    networks:
      default:
        ipv4_address: $APP_BITCOIN_NODE_IP

  tor:
    image: getumbrel/tor:*******@sha256:2ace83f22501f58857fa9b403009f595137fa2e7986c4fda79d82a8119072b6a
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/torrc:/etc/tor/torrc:ro
      - ${TOR_DATA_DIR}:/data
    environment:
      HOME: "/tmp"
    networks:
      default:
        ipv4_address: "${APP_BITCOIN_TOR_PROXY_IP}"
  
  i2pd_daemon:
    image: purplei2p/i2pd:release-2.44.0@sha256:d154a599793c393cf9c91f8549ba7ece0bb40e5728e1813aa6dd4c210aa606f6
    user: "root"
    command: --sam.enabled=true --sam.address=0.0.0.0 --sam.port=7656 --loglevel=error
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/i2pd:/home/<USER>/data
    networks:
      default:
        ipv4_address: "${APP_BITCOIN_I2P_DAEMON_IP}"
