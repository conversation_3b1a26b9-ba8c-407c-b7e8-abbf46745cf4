manifestVersion: 1.1
id: electrs
category: bitcoin
name: Electrs
version: "0.10.9"
tagline: A simple and efficient Electrum Server
description: >
  Run your personal Electrum server and connect your Electrum-compatible wallet,
  including BitBoxApp, BlueWallet, Electrum Wallet (Android and Desktop), Nunchuk 
  (Desktop), Phoenix, and Sparrow Wallet to it instead of using a third-party
  Electrum server.


  Powered by Electrs from Roman Zeyde.


  An official app from Umbrel.
developer: Umbrel
website: https://umbrel.com/
dependencies:
  - bitcoin
repo: https://github.com/getumbrel/umbrel-electrs
support: https://community.getumbrel.com/c/bitcoin-and-lightning
port: 2102
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
path: ""
defaultPassword: ""
releaseNotes: >
  Highlights:
    - Separates blocks reading & index writing into scoped threads
    - Sets HTTP Content-Type header for Prometheus response
    - Doesn't deserialize transactions if not needed
    - Allows concurrent DB background operations for SSDs


  Full release notes are available at https://github.com/romanz/electrs/blob/master/RELEASE-NOTES.md#0109-feb-01-2025
submitter: Umbrel
submission: https://github.com/getumbrel/umbrel/pull/242
