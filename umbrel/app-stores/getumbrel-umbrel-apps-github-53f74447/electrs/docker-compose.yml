version: "3.7"

services:
  app_proxy:
    environment:
      APP_HOST: $APP_ELECTRS_IP
      APP_PORT: 3006

  app:
    image: getumbrel/umbrel-electrs:v1.0.4@sha256:fa2fd04f4c7515aad84f21640f5feab1417bc18e20bc65cda7ed97e59b8458db
    depends_on:
      - electrs
    restart: on-failure
    environment:
      ELECTRUM_HIDDEN_SERVICE: "${APP_ELECTRS_RPC_HIDDEN_SERVICE}"
      ELECTRUM_LOCAL_SERVICE: "${DEVICE_DOMAIN_NAME}"
      ELECTRS_HOST: "${APP_ELECTRS_NODE_IP}"
      BITCOIN_HOST: "${APP_BITCOIN_NODE_IP}"
      RPC_USER: "${APP_BITCOIN_RPC_USER}"
      RPC_PASSWORD: "${APP_BITCOIN_RPC_PASS}"
      RPC_PORT: "${APP_BITCOIN_RPC_PORT}"
    networks:
      default:
        ipv4_address: $APP_ELECTRS_IP
  
  electrs:
    image: getumbrel/electrs:v0.10.9@sha256:622657fbdc7331a69f5b3444e6f87867d51ac27d90c399c8bf25d9aab020052b
    restart: always
    environment:
      ELECTRS_LOG_FILTERS: "INFO"
      ELECTRS_NETWORK: "${APP_BITCOIN_NETWORK_ELECTRS}"
      ELECTRS_DAEMON_RPC_ADDR: "${APP_BITCOIN_NODE_IP}:${APP_BITCOIN_RPC_PORT}"
      ELECTRS_DAEMON_P2P_ADDR: "${APP_BITCOIN_NODE_IP}:${APP_BITCOIN_P2P_PORT}"
      ELECTRS_ELECTRUM_RPC_ADDR: "0.0.0.0:${APP_ELECTRS_NODE_PORT}"
      ELECTRS_SERVER_BANNER: "Umbrel Electrs (${APP_VERSION})"
      ELECTRS_DB_DIR: "/data/db"
    volumes:
      - "${APP_BITCOIN_DATA_DIR}:/data/.bitcoin:ro"
      - "${APP_DATA_DIR}/data/electrs:/data"
    ports:
      - "${APP_ELECTRS_NODE_PORT}:${APP_ELECTRS_NODE_PORT}"
    networks:
      default:
        ipv4_address: $APP_ELECTRS_NODE_IP

  tor:
    image: getumbrel/tor:*******@sha256:2ace83f22501f58857fa9b403009f595137fa2e7986c4fda79d82a8119072b6a
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/torrc:/etc/tor/torrc:ro
      - ${TOR_DATA_DIR}:/data
    environment:
      HOME: "/tmp"
