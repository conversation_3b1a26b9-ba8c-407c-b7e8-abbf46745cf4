manifestVersion: 1.1
id: rustdesk-server
name: RustDesk Server
tagline: A fast and reliable solution for remote access
category: networking
version: "1.1.14"
port: 21080
description: >-
  RustDesk is an open-source, self-hosted, and secure remote desktop solution that allows users to access and control computers remotely. It provides functionality similar to other remote desktop tools like TeamViewer and AnyDesk but with the key advantage of being open-source, meaning users can inspect the code, contribute to its development, and even self-host the software on their own servers. This allows for greater control over the remote access environment and better privacy, as all communication can be routed through a server that the user owns or operates, rather than relying on a third-party provider.


  RustDesk is built using Rust, a systems programming language known for its performance, security, and memory safety. The software is lightweight and designed for ease of use, featuring an intuitive interface that makes remote desktop access accessible to both technical and non-technical users. It supports cross-platform functionality, meaning you can connect from Windows, macOS, Linux, iOS, and Android devices to control a remote machine. RustDesk offers essential features such as file transfer, clipboard synchronization, and screen sharing, ensuring a seamless experience for users needing remote assistance or working from different locations.


  One of the major benefits of RustDesk is its ability to function without relying on centralized servers. While it does offer a cloud service for convenience, users can set up their own server infrastructure, enhancing the security and privacy of their remote sessions. With this self-hosted option, users can have complete control over the data transmitted between the devices, which is ideal for individuals or organizations that prioritize privacy and data protection.


  Overall, RustDesk provides a powerful alternative to proprietary remote desktop software. Its open-source nature, self-hosting capability, and high performance make it an appealing choice for anyone seeking a reliable, secure, and customizable remote desktop solution.
developer: RustDesk Team
website: https://rustdesk.com
submitter: Knufle
submission: https://github.com/getumbrel/umbrel-apps/pull/2411
repo: https://github.com/rustdesk/rustdesk-server
support: https://github.com/rustdesk/rustdesk/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
defaultUsername: ""
defaultPassword: ""
dependencies: []
releaseNotes: ""
path: ""
