manifestVersion: 1.2
id: libretranslate
category: ai
name: LibreTranslate
version: "1.6.5"
tagline: Free and Open Source machine translation API
description: >-
  ⚠️ Removal Notice: The LibreTranslate app has been disabled over trademark issues.

  ⚠️ This app may take up to 10 minutes or more to become accessible after installation, depending on your hardware and internet connection. LibreTranslate must first download around 10 GB of translation models in the background before the UI becomes available. Please be patient.


  LibreTranslate is a free and Open Source Machine Translation API, entirely self-hosted.
  Unlike other APIs, it doesn't rely on proprietary providers such as Google or Azure to perform translations.
  Instead, its translation engine is powered by the open source Argos Translate library.

developer: LibreTranslate Contributors
website: https://libretranslate.com
dependencies: []
repo: https://github.com/LibreTranslate/LibreTranslate
support: https://github.com/LibreTranslate/LibreTranslate/discussions
port: 7017
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
path: ""
defaultUsername: ""
defaultPassword: ""
submitter: highghlow
submission: https://github.com/getumbrel/umbrel-apps/pull/1040
releaseNotes: >-
  This update includes several improvements and new features:
    - Improved Traditional Chinese translations
    - Enhanced language detection capabilities
    - Added emoji translation request detection
    - Various UI and accessibility improvements


  Full release notes are found at https://github.com/LibreTranslate/LibreTranslate/releases
disabled: true
