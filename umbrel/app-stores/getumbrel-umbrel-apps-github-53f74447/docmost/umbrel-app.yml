manifestVersion: 1
id: docmost
name: Docmost
tagline: An collaborative wiki and documentation software
category: files
version: "0.21.0"
port: 8941
description: >-
  Docmost is an open source collaborative documentation and knowledge management platform designed to help teams create and maintain structured content together. It serves as a modern alternative to tools like Notion and Confluence with a strong focus on real time collaboration and ease of use.


  At its core Docmost allows multiple users to work on the same document simultaneously without the risk of overwriting each other's changes. Every update appears in real time creating a smooth and intuitive editing experience similar to what users expect from cloud based productivity tools. This real time capability makes Docmost especially suitable for fast moving teams and projects that require collective input and quick iteration.


  The platform features a clean and user friendly rich text editor that supports advanced formatting tables comments and visual content. It integrates with tools such as Excalidraw Drawio and Mermaid to allow for the creation and embedding of diagrams directly within pages. This makes it easier to present ideas processes and relationships visually without needing to switch between applications.


  Docmost organizes content through a system of spaces pages and groups with flexible permission settings. This structure allows teams to maintain clear boundaries between departments projects or knowledge areas while keeping navigation straightforward. Users can also search across the entire knowledge base with ease ensuring that information is always accessible when needed.


  Built with extensibility in mind Docmost includes a plugin system supports translations and provides an API for custom integrations. Teams can self host the platform or opt for a managed cloud version depending on their infrastructure and compliance needs.


  The core software is licensed under the AGPL which ensures that it remains free and open while encouraging contributions and transparency. For organizations seeking additional features or enterprise level support Docmost also offers a commercial edition with advanced capabilities and dedicated services.


  Overall Docmost provides a powerful yet flexible solution for teams that value collaborative knowledge sharing and want full control over their data and workflows.
developer: Docmost, Inc.
website: https://docmost.com/
submitter: dennysubke
submission: https://github.com/getumbrel/umbrel-apps/pull/2870
repo: https://github.com/docmost/docmost
support: https://github.com/docmost/docmost/issues
gallery:
  - 1.jpg
  - 2.jpg
  - 3.jpg
  - 4.jpg
  - 5.jpg
  - 6.jpg
releaseNotes: ""
dependencies: []
path: ""
defaultUsername: ""
defaultPassword: ""
