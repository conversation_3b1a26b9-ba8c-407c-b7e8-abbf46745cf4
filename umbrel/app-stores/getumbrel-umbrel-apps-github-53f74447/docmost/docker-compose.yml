version: '3.7'

services:
  app_proxy:
    environment:
      APP_HOST: docmost_web_1
      APP_PORT: 3000

  web:
    image: docmost/docmost:0.21.0@sha256:7b69c758d1b3286eaa5ba961fcc1b263bc5768124f29b097ce39fff5e48c073f
    user: "1000:1000"
    depends_on:
      - db
      - redis
    environment:
      APP_URL: "http://${DEVICE_DOMAIN_NAME}:8941"
      APP_SECRET: ${APP_SEED}
      DATABASE_URL: '******************************************************/docmost?sslmode=disable'
      REDIS_URL: "redis://docmost_redis_1:6379"
      DISABLE_TELEMETRY: "true"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/docmost_data:/app/data/storage

  db:
    image: postgres:17.3@sha256:0321e2252ebfeecb8bc1a899755084d29bce872953e1a5a3e25ec0860b739098
    user: "1000:1000"
    environment:
      POSTGRES_DB: docmost
      POSTGRES_USER: docmostuser
      POSTGRES_PASSWORD: docmostpass
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/db_data:/var/lib/postgresql/data

  redis:
    image: redis:8.0.1@sha256:b3ad79880c88e302deb5e0fed6cee3e90c0031eb90cd936b01ef2f83ff5b3ff2
    user: "1000:1000"
    restart: on-failure
    volumes:
      - ${APP_DATA_DIR}/data/redis_data:/data
