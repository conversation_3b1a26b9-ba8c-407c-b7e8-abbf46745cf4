services:
  umbrel:
    image: dockurr/umbrel
    container_name: umbrel
    pid: host
    ports:
      - 80:80
    volumes:
      - ./umbrel:/data
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - DEVICE_DOMAIN_NAME=umbrel.reizedispatch.com
      - DEVICE_HOSTNAME=umbrel
      - UMBREL_DOMAIN=umbrel.reizedispatch.com
      - UMBREL_BASE_URL=https://umbrel.reizedispatch.com
    restart: always
    stop_grace_period: 1m
