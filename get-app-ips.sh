#!/bin/bash

echo "=== Umbrel App IPs on Main Network ==="
echo ""

# Get all containers on the main network
docker network inspect umbrel_main_network --format='{{range .Containers}}{{.Name}}: {{.IPv4Address}}{{"\n"}}{{end}}' | \
grep -E "(server|web|app)_1:" | \
grep -v "proxy" | \
sort | \
while IFS=': ' read -r container ip; do
    # Extract app name from container name
    app_name=$(echo "$container" | sed 's/_server_1\|_web_1\|_app_1//')
    
    # Get the port from docker inspect
    port=$(docker port "$container" 2>/dev/null | head -1 | cut -d':' -f2)
    
    if [ -n "$port" ]; then
        echo "📱 $app_name: http://${ip%/*}:$port"
    else
        echo "📱 $app_name: http://${ip%/*}"
    fi
done

echo ""
echo "=== For Cloudflare Tunnel Configuration ==="
echo "Use these URLs as 'Service' in your tunnel config:"
